/**
 * 简化版浏览器返回监控
 * 专门用于MBTI项目，当用户点击返回时弹出优惠弹窗
 */

$(document).ready(function() {
    // 浏览器返回监控功能
    function initBackMonitor() {
        // 检查浏览器支持
        if (!window.history || !window.history.pushState) {
            console.warn('浏览器不支持History API');
            return;
        }
        
        // 添加虚拟历史记录
        window.history.pushState('preventBack', null, '');
        
        // 监听返回事件
        window.addEventListener('popstate', function(event) {
            // 立即重新添加历史记录，阻止返回
            window.history.pushState('preventBack', null, '');
            
            // 显示优惠弹窗
            showBackDialog();
        });
        
        console.log('返回监控已启动');
    }
    
    // 显示返回拦截弹窗
    function showBackDialog() {
        var dialogElement = $('#huodongyouhui_dialog');
        
        if (dialogElement.length === 0) {
            console.error('找不到弹窗元素: huodongyouhui_dialog');
            return;
        }
        
        // 使用layer插件显示弹窗
        if (typeof layer !== 'undefined') {
            var dialogContent = dialogElement.html();
            
            layer.open({
                type: 1,
                title: false,
                closeBtn: 0,
                area: ['90%', 'auto'],
                maxWidth: '400px',
                skin: 'layui-layer-nobg',
                shadeClose: false,
                shade: [0.8, '#000'],
                content: dialogContent,
                success: function(layero, index) {
                    console.log('返回拦截弹窗已显示');
                    
                    // 为弹窗内的按钮添加点击事件
                    layero.find('.dialog2_btn, .buy_btn').on('click', function() {
                        console.log('用户点击了购买按钮');
                        layer.close(index);
                        
                        // 触发原有的购买逻辑
                        if ($('.buy_btn').length > 0) {
                            $('.buy_btn').first().trigger('click');
                        }
                    });
                    
                    // 如果有关闭按钮
                    layero.find('.close_btn, .van-icon-cross').on('click', function() {
                        layer.close(index);
                    });
                }
            });
        } else {
            // 如果layer插件未加载，直接显示原有弹窗
            dialogElement.show();
            
            // 添加遮罩
            if ($('.simple-back-overlay').length === 0) {
                $('body').append('<div class="simple-back-overlay" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.8);z-index:9999;"></div>');
            }
            
            // 调整弹窗样式
            dialogElement.css({
                'position': 'fixed',
                'z-index': '10000',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)'
            });
            
            // 绑定关闭事件
            $('.simple-back-overlay, .close_btn').on('click', function() {
                dialogElement.hide();
                $('.simple-back-overlay').remove();
            });
            
            // 绑定购买按钮事件
            dialogElement.find('.dialog2_btn, .buy_btn').on('click', function() {
                dialogElement.hide();
                $('.simple-back-overlay').remove();
                
                // 触发原有的购买逻辑
                if ($('.buy_btn').length > 0) {
                    $('.buy_btn').first().trigger('click');
                }
            });
        }
    }
    
    // 可选：添加ESC键监听
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27) { // ESC键
            e.preventDefault();
            showBackDialog();
        }
    });
    
    // 可选：页面卸载提示
    window.addEventListener('beforeunload', function(e) {
        var message = '确定要离开吗？您可能会错过限时优惠！';
        e.preventDefault();
        e.returnValue = message;
        return message;
    });
    
    // 启动监控
    initBackMonitor();
});
