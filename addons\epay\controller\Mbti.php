<?php

namespace addons\epay\controller;

use addons\epay\library\Service;
use app\common\model\MbtiOrder;
use app\common\service\AdReportService;
use app\common\service\MbtiOrderTwopayService;
use think\addons\Controller;
use Exception;
use think\Log;
use think\Db;

/**
 * 微信支付宝插件首页
 *
 * 此控制器仅用于开发展示说明和体验，建议自行添加一个新的控制器进行处理返回和回调事件，同时删除此控制器文件
 *
 * Class Index
 * @package addons\epay\controller
 */
class Mbti extends Controller
{

    protected $layout = 'default';

    protected $config = [];
    protected $alipay_type_list = ['alipay','alipay_mbti'];

    public function _initialize()
    {
        parent::_initialize();
        if (!config("app_debug")) {
            //$this->error("仅在开发环境下查看");
        }
    }

    public function index()
    {
        $this->view->assign("title", "微信支付宝整合插件");
        return $this->view->fetch();
    }


    /**
     * 支付成功，仅供开发测试
     */
    public function notifyx()
    {
        $paytype = $this->request->param('paytype');
        $pay = Service::checkNotify($paytype);
        if (!$pay) {
            echo '签名错误';
            return;
        }
        $data = $pay->verify();
        try {
            $payamount = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
            $out_trade_no = $data['out_trade_no'];
            //你可以在此编写订单逻辑
        } catch (Exception $e) {
        }
        echo $pay->success();
    }

    /**
     * 支付返回，仅供开发测试
     */
    public function returnx()
    {
        $paytype = $this->request->param('paytype');
        $out_trade_no = $this->request->param('out_trade_no');
        $pay = Service::checkReturn($paytype);
        if (!$pay) {
            $this->error('签名错误', '');
        }

        //你可以在这里通过out_trade_no去验证订单状态
        //但是不可以在此编写订单逻辑！！！

        $this->success("请返回网站查看支付结果", addon_url("epay/index/index"));
    }


    public function submitpay()
    {
        //$amount = config('payset.amount');
        $amount = $this->request->request('amount');
        $type = $this->request->request('type'); //支付方式
        $out_trade_no = $this->request->request('order_sn');
        $is_two_pay = $this->request->request('is_two_pay',0);
        $qt_type = $this->request->request('qt_type',0);
        $iscoupon = $this->request->request('iscoupon',0);


        $qt_type = getti_type($qt_type);//获取真实档位信息
        $orderInfo = MbtiOrder::where(['sn' => $out_trade_no])->field('order_stage,qt_type')->find();

        $returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;
        if ($is_two_pay){
            if ($orderInfo['qt_type'] == 3){
                echo '<script type="text/javascript">';
                echo 'alert("该订单已是PRO版报告,无需支付解锁！");';
                echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
//            echo 'history.go(-1)';
                echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
                return ;
            }
            //二次支付价格
//            $amount = getSecondPayMoney($qt_type,$orderInfo['qt_type']);
            $amount = 10;
        }else{
            $amount = getCurPrice($qt_type,$iscoupon);
        }

        var_dump('--金额--'.$amount);
        var_dump('--二次支付--'.$is_two_pay);
        var_dump('--支付类型--'.$qt_type);
        sleep(10);
        $amount = 0.01;
        //$method = $this->request->request('method');
        /*if ($out_trade_no == 'Z611371930432372') {
            $amount = 0.01;
        }*/

        if (!$amount || $amount < 0) {
            $this->error("支付金额必须大于0");
        }

        if (!$type || !in_array($type, ['alipay', 'wechat'])) {
            $this->error("支付类型不能为空");
        }
        //订单标题
        $title = 'MBTI在线性格测试';

        $orderInfo = MbtiOrder::where(['sn' => $out_trade_no])->field('order_stage,qt_type')->find();
        if ($orderInfo && $orderInfo['order_stage'] == 1 && $is_two_pay==0){
            echo '<script type="text/javascript">';
            echo 'alert("该订单已完成支付");';
            echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
//            echo 'history.go(-1)';
            echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
            return ;
        }
        //回调链接
        //如果是 增量支付
        $twoPayOderSn = '';
        $payOrderSn = $out_trade_no.'_'.time();
        if ($is_two_pay == 1){

            $twopayInfo = MbtiOrderTwopayService::getOrderTwopayInfo(['sn' => $out_trade_no], "sn,two_sn,order_stage");
            if (!$twopayInfo){
//                $twoPayOderSn = $out_trade_no.'_1';
                $twopayData = [
                    'sn'=>$out_trade_no,
                    'order_price'=>$amount,
//                    'two_sn'=>$twoPayOderSn,
                    'add_time'=>date("Y-m-d H:i:s"),
                ];
                $res = MbtiOrderTwopayService::insertData($twopayData);
            }else{
                if ($twopayInfo['order_stage'] == 1){
//                    echo '已支付';
                    echo '<script type="text/javascript">';
                    echo 'alert("该订单已完成支付");';
                    echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
                    echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
                    return ;
                }
                $twoPayOderSn = $twopayInfo['two_sn'];
            }

        }

       /* if($is_two_pay == 0){
            //更新题目类型
            $updata = [
                'qt_type' => $qt_type,
            ];
            db('mbti_order')->where(['sn' => $out_trade_no])->update($updata);
            
        }*/

//        $lastpayordersn = $twoPayOderSn ? $twoPayOderSn : $out_trade_no;
        $lastpayordersn = $payOrderSn;
        $notifyurl = $this->request->root(
                true
            ) . '/addons/epay/mbti/notifyHandle/paytype/' . $type.'/is_two_pay/'.$is_two_pay.'/qt_type/'.$qt_type;
        //$returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;

        $response = Service::submitOrder($amount, $lastpayordersn, $type, $title, $notifyurl, $returnurl);
        return $response;
    }

    /**
     * 支付成功
     */
    public function notifyHandle()
    {
        $paytype = $this->request->param('paytype');
        $is_two_pay = $this->request->param('is_two_pay',0);
        $qt_type = $this->request->param('qt_type',0);
        $pay = Service::checkNotify($paytype);
        if (!$pay) {
            echo '签名错误';
            return;
        }

        $data = $pay->verify();
        Log::record($data, 'notifydata');
        try {
            if (in_array($paytype, $this->alipay_type_list)) {
                $paytype = 'alipay';
            }
            $payamount = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
            //是二次支付
            if ($is_two_pay == 1){
                $out_trade_no = $data['out_trade_no'];
                $true_out_trade_no = $out_trade_no;
                Log::record('second pay:'.$out_trade_no, 'out_trade_no');
                if(strpos($out_trade_no,'_') !== false){
                    $out_trade_no = explode('_',$out_trade_no)[0];
                }
//                $row = db('mbti_order')->where(['sn' => $out_trade_no])->find();
                $mbitTwoInfo = MbtiOrderTwopayService::getOrderTwopayInfo(['sn' => $out_trade_no],"sn,two_sn,order_stage");
                if (!$mbitTwoInfo) {
                    Log::record('second pay:'.$out_trade_no, 'no out_trade_no');
                    return;
                }
                if ($mbitTwoInfo['order_stage'] == 1) {
                    echo "success";
                    return;
                }

                $twoUpdata = [
                    'paymoney' => $payamount,
                    'order_stage' => 1,
                    'paytype' => $paytype,
                    'sub_paytype' => $paytype,
                    'two_sn' => $true_out_trade_no,
                    'pay_time' => date('Y-m-d H:i:s'),
                ];
                MbtiOrderTwopayService::update(['sn'=>$out_trade_no], $twoUpdata);

                $updata = [
                    'is_twopay' => 1,
                    'qt_type' => $qt_type,
                ];

                db('mbti_order')->where(['sn' => $mbitTwoInfo['sn']])->update($updata);

            }else{
                $out_trade_no = $data['out_trade_no'];
                $true_out_trade_no = $out_trade_no;
                Log::record($out_trade_no, 'out_trade_no');
                if(strpos($out_trade_no,'_') !== false){
                    $out_trade_no = explode('_',$out_trade_no)[0];
                }
                $row = db('mbti_order')->where(['sn' => $out_trade_no])->find();
                if (empty($row)) {
                    Log::record($out_trade_no, 'no out_trade_no');
                    return;
                }
                if ($row['order_stage'] == 1) {
                    echo "success";
                    return;
                }
                $updata = [
                    'paymoney' => $payamount,
                    'order_stage' => 1,
                    'paytype' => $paytype,
                    'out_trade_no' => $true_out_trade_no,
                    'sub_paytype' => $paytype,
                    'qt_type' => $qt_type,
                ];
                $updata['pay_time'] = date('Y-m-d H:i:s');
                db('mbti_order')->where(['sn' => $out_trade_no])->update($updata);
                //处理上报逻辑
                AdReportService::updateReportStatus($out_trade_no);
            }


            //你可以在此编写订单逻辑
        } catch (Exception $e) {
            Log::record($e->getMessage(), 'catch_notifydata');
        }
        echo "success";
    }

    public function notifyHandle_test()
    {
        $data = [
            "app_id" => "2021002116692797",
            "auth_app_id" => "2021002116692797",
            "buyer_id" => "2088202843337497",
            "buyer_logon_id" => "pen***@126.com",
            "buyer_pay_amount" => "0.01",
            "charset" => "utf-8",
            "fund_bill_list" => "[{\"amount\":\"0.01\",\"fundChannel\":\"PCREDIT\"}]",
            "gmt_create" => "2022-04-21 22:36:38",
            "gmt_payment" => "2022-04-21 22:36:39",
            "invoice_amount" => "0.01",
            "notify_id" => "2022042100222223639037491417023540",
            "notify_time" => "2022-04-21 22:36:39",
            "notify_type" => "trade_status_sync",
            "out_trade_no" => "Z421515230832587",
            "point_amount" => "0.00",
            "receipt_amount" => "0.01",
            "seller_email" => "<EMAIL>",
            "seller_id" => "2088041264264456",
            "sign" => "i/g/DNppQFH4wgK+17+jc1iscwQ2hQcISerXiL/YAZ4VpbQdPPiouIQQVzpZkeYe1yFIIJGhceLlYe+0USqfQ3hBE98QmF1Wk3+OjYKwmgXidaS+wwcDoQ+gPyIULUpP6S9R+F9SXi9Tav/gD9+xAcs/AwWnAIpr+YHdBwgCH0UWy0iIMum0GdIY8+zwHN3rNeGYF9zXjFvtxDq7QUqgQfi/rGpzWGrjdPvpvNGGHeXYivBRSzUMXj/29AJrpZskJv8NwhVMmbQf2uN6wQ7IbVSNC6JxuxUwmWfpXc+vFP+gm0OjiKiAsmmLbmLZasljYZGiBnCSekFm1wImidpCeA==",
            "sign_type" => "RSA2",
            "subject" => "MBTI在线性格测试",
            "total_amount" => "0.01",
            "trade_no" => "2022042122001437491439813909",
            "trade_status" => "TRADE_SUCCESS",
            "version" => "1.0"
        ];

        $paytype = 'alipay';

        Log::record($data, 'notifydata');
        $payamount = $paytype == 'alipay' ? $data['total_amount'] : $data['total_fee'] / 100;
        $out_trade_no = $data['out_trade_no'];
        Log::record($out_trade_no, 'out_trade_no');
        $row = db('mbti_order')->where(['sn' => $out_trade_no])->find();
        if (empty($row)) {
            Log::record($out_trade_no, 'no  out_trade_no');
            return;
        }
        if ($row['order_stage'] == 1) {
            echo "success";
            return;
        }
        db('mbti_order')->where(['sn' => $out_trade_no])->update(
            [
                'pay_time' => date('Y-m-d H:i:s'),
                'paymoney' => $payamount,
                'order_stage' => 1,
                'paytype' => $paytype,
            ]
        );
        //你可以在此编写订单逻辑
        echo "success";
    }


    /**
     * 支付返回，仅供开发测试
     */
    public function returnHandel()
    {
        $paytype = $this->request->param('paytype');
        $out_trade_no = $this->request->param('out_trade_no');
        $pay = Service::checkReturn($paytype);
        if (!$pay) {
            $this->error('签名错误', '');
        }

        //你可以在这里通过out_trade_no去验证订单状态
        //但是不可以在此编写订单逻辑！！！

        $returnurl = $this->request->root(true) . '/s/' . $out_trade_no;

        $this->success("支付结果检测中", $returnurl);
    }


}
