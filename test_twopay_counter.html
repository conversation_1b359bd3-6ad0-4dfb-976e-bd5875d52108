<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
    <title>TwoPay购买人数计数器测试</title>
    <script src="/Public/layer/jquery.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 30px;
            font-weight: bold;
        }
        
        .counter-display {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        .center {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 5px;
            font-size: 20px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .buynumshow {
            color: #4CAF50;
            font-weight: bold;
            font-size: 22px;
        }
        
        .scroll {
            display: flex;
            align-items: center;
            gap: 2px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 5px 8px;
            margin: 0 5px;
        }
        
        .scroll1, .scroll2 {
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            border-radius: 3px;
            padding: 2px 6px;
            font-weight: bold;
            min-width: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .scroll1.updated, .scroll2.updated {
            background: #4CAF50;
            color: white;
            transform: scale(1.1);
        }
        
        .status-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .status-label {
            font-weight: bold;
        }
        
        .status-value {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .update-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
            display: none;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-item {
            font-size: 12px;
            margin-bottom: 5px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        
        .log-time {
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="update-indicator" id="updateIndicator">
        数据已更新！
    </div>
    
    <div class="test-container">
        <div class="title">TwoPay购买人数计数器</div>
        
        <div class="counter-display">
            <div class="center">
                已有<span class="buynumshow">338997</span>
                <div class="scroll">=
                    <div class="scroll1">0</div>
                    <div class="scroll2">0</div>
                </div>人购买
            </div>
        </div>
        
        <div class="status-info">
            <div class="status-row">
                <span class="status-label">本地存储总数:</span>
                <span class="status-value" id="totalCount">3389970</span>
            </div>
            <div class="status-row">
                <span class="status-label">显示数字:</span>
                <span class="status-value" id="displayNumber">338997</span>
            </div>
            <div class="status-row">
                <span class="status-label">scroll1:</span>
                <span class="status-value" id="scroll1Display">0</span>
            </div>
            <div class="status-row">
                <span class="status-label">scroll2:</span>
                <span class="status-value" id="scroll2Display">1</span>
            </div>
            <div class="status-row">
                <span class="status-label">更新间隔:</span>
                <span class="status-value">1000ms</span>
            </div>
            <div class="status-row">
                <span class="status-label">运行状态:</span>
                <span class="status-value" id="runningStatus">运行中</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="manualUpdate()">手动更新</button>
            <button class="btn" onclick="toggleTimer()">暂停/恢复</button>
            <button class="btn" onclick="resetCounter()">重置计数器</button>
            <button class="btn" onclick="clearLogs()">清空日志</button>
        </div>
        
        <div class="info-box">
            <h4>功能说明：</h4>
            <ul>
                <li>本地存储初始值：3389970</li>
                <li>显示数字：去掉个位数的部分（338997）</li>
                <li>scroll1：显示个位数，scroll2：比scroll1大1（计数器效果）</li>
                <li>每1秒随机增加1或2</li>
                <li>数据持久化保存在localStorage</li>
            </ul>
        </div>
        
        <div class="log-container">
            <h4>更新日志：</h4>
            <div id="logContent"></div>
        </div>
    </div>

    <script>
        // 购买人数本地存储和滚动数字功能
        let buyUserCount = parseInt(localStorage.getItem('buyUserCountTwoPay') || 3389970);
        let scroll1Value = buyUserCount % 10; // 个位数
        let scroll2Value = (scroll1Value + 1) % 10; // 比scroll1大1，模拟计数器滚动效果
        let scrollTimer = null;
        let updateCount = 0;

        // 初始化显示
        function initializeBuyCount() {
            // 显示十位数开始到最大位数（不显示个位数）
            const displayNumber = Math.floor(buyUserCount / 10);
            $('.buynumshow').text(displayNumber);
            
            // 设置scroll1和scroll2的初始值
            $('.scroll1').text(scroll1Value);
            $('.scroll2').text(scroll2Value);
            
            // 更新状态显示
            updateStatusDisplay();
            
            addLog('初始化', `总数: ${buyUserCount}, 显示: ${displayNumber}, scroll1: ${scroll1Value}, scroll2: ${scroll2Value}`);
        }

        // 更新滚动数字
        function updateScrollNumbers() {
            // 随机增加1或2
            const increment = Math.random() < 0.5 ? 1 : 2;
            const oldCount = buyUserCount;
            
            // 更新本地存储的总数
            buyUserCount += increment;
            localStorage.setItem('buyUserCountTwoPay', buyUserCount);
            
            // 更新个位数显示（计数器滚动效果）
            const oldDigit = scroll1Value;
            const oldScroll2 = scroll2Value;
            scroll1Value = buyUserCount % 10;
            scroll2Value = (scroll1Value + 1) % 10; // scroll2始终比scroll1大1
            
            // 更新显示的数字（十位数开始）
            const displayNumber = Math.floor(buyUserCount / 10);
            $('.buynumshow').text(displayNumber);
            
            // 更新滚动数字并添加动画效果
            $('.scroll1').addClass('updated').text(scroll1Value);
            $('.scroll2').addClass('updated').text(scroll2Value);
            
            setTimeout(() => {
                $('.scroll1, .scroll2').removeClass('updated');
            }, 300);
            
            // 更新状态显示
            updateStatusDisplay();
            
            // 显示更新指示器
            showUpdateIndicator();
            
            // 添加日志
            updateCount++;
            addLog('自动更新', `+${increment}: ${oldCount}→${buyUserCount}, scroll1: ${oldDigit}→${scroll1Value}, scroll2: ${oldScroll2}→${scroll2Value}`);
            
            console.log('购买人数更新:', buyUserCount, '显示数字:', displayNumber, '个位数:', scroll1Value);
        }

        // 更新状态显示
        function updateStatusDisplay() {
            const displayNumber = Math.floor(buyUserCount / 10);
            $('#totalCount').text(buyUserCount);
            $('#displayNumber').text(displayNumber);
            $('#scroll1Display').text(scroll1Value);
            $('#scroll2Display').text(scroll2Value);
        }

        // 显示更新指示器
        function showUpdateIndicator() {
            const indicator = $('#updateIndicator');
            indicator.show();
            setTimeout(() => {
                indicator.hide();
            }, 1500);
        }

        // 添加日志
        function addLog(type, message) {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logHtml = `
                <div class="log-item">
                    <span class="log-time">[${timeStr}]</span> 
                    <strong>${type}:</strong> ${message}
                </div>
            `;
            $('#logContent').prepend(logHtml);
            
            // 限制日志数量
            const logs = $('#logContent .log-item');
            if (logs.length > 20) {
                logs.slice(20).remove();
            }
        }

        // 启动定时器
        function startScrollTimer() {
            if (scrollTimer) {
                clearInterval(scrollTimer);
            }
            
            scrollTimer = setInterval(function() {
                updateScrollNumbers();
            }, 1000); // 每1秒更新一次
            
            $('#runningStatus').text('运行中').css('color', '#4CAF50');
        }

        // 停止定时器
        function stopScrollTimer() {
            if (scrollTimer) {
                clearInterval(scrollTimer);
                scrollTimer = null;
            }
            $('#runningStatus').text('已暂停').css('color', '#ff9800');
        }

        // 手动更新
        function manualUpdate() {
            updateScrollNumbers();
        }

        // 切换定时器
        function toggleTimer() {
            if (scrollTimer) {
                stopScrollTimer();
                addLog('操作', '定时器已暂停');
            } else {
                startScrollTimer();
                addLog('操作', '定时器已启动');
            }
        }

        // 重置计数器
        function resetCounter() {
            buyUserCount = 3389970;
            scroll1Value = buyUserCount % 10;
            scroll2Value = (scroll1Value + 1) % 10;
            localStorage.setItem('buyUserCountTwoPay', buyUserCount);
            updateCount = 0;

            initializeBuyCount();
            addLog('操作', '计数器已重置到初始值');
        }

        // 清空日志
        function clearLogs() {
            $('#logContent').empty();
            addLog('操作', '日志已清空');
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            setTimeout(function() {
                initializeBuyCount();
                startScrollTimer();
            }, 500);
        });

        // 页面卸载时停止定时器
        $(window).on('beforeunload', function() {
            stopScrollTimer();
        });
    </script>
</body>
</html>
