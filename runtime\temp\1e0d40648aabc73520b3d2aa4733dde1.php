<?php if (!defined('THINK_PATH')) exit(); /*a:2:{s:97:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\public/../application/mbti/view/default/index\question.html";i:1751284080;s:87:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\application\mbti\view\default\public\baidujs.html";i:1749825702;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN" style="--status-bar-height:0px; --top-window-height:0px; --window-left:0px; --window-right:0px; --window-margin:0px; font-size: 20.7px; --window-top:calc(var(--top-window-height) + 0px); --window-bottom:0px;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>MBTI性格测试题目</title>
    <link rel="icon" type="image/x-icon">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1">
    <script src="/Public/layer/jquery.min.js"></script>
    <script src="/Public/layer/layer.js"></script>
    
    <script>
     
       document.addEventListener('DOMContentLoaded', function () {
        document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px';

    })
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
    document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')</script>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <link rel="stylesheet" href="/Public/mbit/index_files/index.a5c69d49.css">

    <style type="text/css">
        @font-face {
            font-family: YouSheBiaoTiHei;
            src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
            src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"),
            url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"),
            url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
        }

        .footer-text[data-v-9cc6041a] {
            margin-top: 27px
        }

        .footer-text .text-title[data-v-9cc6041a] {
            margin-bottom: 11px;
            font-size: 15px;
            font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
            font-weight: 500;
            color: #745959;
            line-height: 30px
        }

        .footer-text .text-content[data-v-9cc6041a] {
            font-size: 12px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #524354;
            line-height: 24px
        }

        .customerService[data-v-9cc6041a] {
            position: fixed;
            right: 0;
            top: 71px;
            width: 30px;
            height: 80px;
            box-sizing: border-box;
            padding: 15px 9px;
            background: #fff;
            box-shadow: -2px -2px 4px 1px rgba(0, 0, 0, .05), 0 4px 4px 1px rgba(0, 0, 0, .05);
            border-radius: 5px 0 0 5px;
            z-index: 999;
            font-size: 13px;
            font-family: Roboto-Regular, Roboto;
            font-weight: 400;
            color: #5280ff;
            line-height: 15px
        }</style>
    <style type="text/css">
        body {
            background: #f7f8fa;
            font-family: PingFangSC-Regular, PingFang SC
        }

        .layout {
            font-family: PingFangSC-Regular, PingFang SC;
            margin: 0 auto;
            transition: all .5s ease 0s
        }

        /* pc样式
     */
        /* 小程序样式
     */
        @media screen and (min-width: 750px) {
            .layout {
                /*max-width: 750px;
     */
            }
        }

        @-webkit-keyframes shake { /* 水平抖动，核心代码 */
            10%, 90% {
                -webkit-transform: translate3d(-1px, 0, 0);
                transform: translate3d(-1px, 0, 0)
            }
            20%, 80% {
                -webkit-transform: translate3d(2px, 0, 0);
                transform: translate3d(2px, 0, 0)
            }
            30%, 70% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
            40%, 60% {
                -webkit-transform: translate3d(4px, 0, 0);
                transform: translate3d(4px, 0, 0)
            }
            50% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
        }

        @keyframes shake { /* 水平抖动，核心代码 */
            10%, 90% {
                -webkit-transform: translate3d(-1px, 0, 0);
                transform: translate3d(-1px, 0, 0)
            }
            20%, 80% {
                -webkit-transform: translate3d(2px, 0, 0);
                transform: translate3d(2px, 0, 0)
            }
            30%, 70% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
            40%, 60% {
                -webkit-transform: translate3d(4px, 0, 0);
                transform: translate3d(4px, 0, 0)
            }
            50% {
                -webkit-transform: translate3d(-4px, 0, 0);
                transform: translate3d(-4px, 0, 0)
            }
        }

        @-webkit-keyframes fire {
            0% {
                -webkit-transform: scaleX(1);
                transform: scaleX(1)
            }
            100% {
                -webkit-transform: scale3d(.7, .8, 1);
                transform: scale3d(.7, .8, 1)
            }
        }

        @keyframes fire {
            0% {
                -webkit-transform: scaleX(1);
                transform: scaleX(1)
            }
            100% {
                -webkit-transform: scale3d(.7, .8, 1);
                transform: scale3d(.7, .8, 1)
            }
        }

        @-webkit-keyframes shoot {
            0% {
                -webkit-transform: translateY(0);
                transform: translateY(0);
                opacity: 1
            }
            100% {
                -webkit-transform: translateY(-200px);
                transform: translateY(-200px);
                opacity: 0
            }
        }

        @keyframes shoot {
            0% {
                -webkit-transform: translateY(0);
                transform: translateY(0);
                opacity: 1
            }
            100% {
                -webkit-transform: translateY(-200px);
                transform: translateY(-200px);
                opacity: 0
            }
        }

        uni-slider {
            margin: 0 0 0 4px !important
        }

        .uni-slider-handle-wrapper {
            height: 11px !important
        }

        .u-input {
            height: 33px;
            padding: 0px 6px !important
        }

        .uni-input-input {
            text-align: center !important
        }

        .uni-slider-tap-area {
            padding: 3px 0 !important
        }

        @font-face {
            font-family: YouSheBiaoTiHei;
            src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
            src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
        }

        .footer-text {
            margin-top: 27px
        }

        .footer-text .text-title {
            margin-bottom: 11px;
            font-size: 15px;
            font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
            font-weight: 500;
            color: #745959;
            line-height: 30px
        }

        .footer-text .text-content {
            font-size: 12px;
            font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
            font-weight: 400;
            color: #524354;
            line-height: 24px
        }

        /*uView样式*/
        .u-line-1 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical !important
        }

        .u-line-2 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical !important
        }

        .u-line-3 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical !important
        }

        .u-line-4 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 4;
            -webkit-box-orient: vertical !important
        }

        .u-line-5 {


            display: -webkit-box !important;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-line-clamp: 5;
            -webkit-box-orient: vertical !important
        }

        .u-border {
            border-width: .5px !important;
            border-color: #dadbde !important;
            border-style: solid
        }

        .u-border-top {
            border-top-width: .5px !important;
            border-color: #dadbde !important;
            border-top-style: solid
        }

        .u-border-left {
            border-left-width: .5px !important;
            border-color: #dadbde !important;
            border-left-style: solid
        }

        .u-border-right {
            border-right-width: .5px !important;
            border-color: #dadbde !important;
            border-right-style: solid
        }

        .u-border-bottom {
            border-bottom-width: .5px !important;
            border-color: #dadbde !important;
            border-bottom-style: solid
        }

        .u-border-top-bottom {
            border-top-width: .5px !important;
            border-bottom-width: .5px !important;
            border-color: #dadbde !important;
            border-top-style: solid;
            border-bottom-style: solid
        }

        .u-reset-button {
            padding: 0;
            background-color: initial;
            font-size: inherit;
            line-height: inherit;
            color: inherit;
        }

        .u-reset-button::after {
            border: none
        }

        .u-hover-class {
            opacity: .7
        }

        .u-primary-light {
            color: #ecf5ff
        }

        .u-warning-light {
            color: #fdf6ec
        }

        .u-success-light {
            color: #f5fff0
        }

        .u-error-light {
            color: #fef0f0
        }

        .u-info-light {
            color: #f4f4f5
        }

        .u-primary-light-bg {
            background-color: #ecf5ff
        }

        .u-warning-light-bg {
            background-color: #fdf6ec
        }

        .u-success-light-bg {
            background-color: #f5fff0
        }

        .u-error-light-bg {
            background-color: #fef0f0
        }

        .u-info-light-bg {
            background-color: #f4f4f5
        }

        .u-primary-dark {
            color: #398ade
        }

        .u-warning-dark {
            color: #f1a532
        }

        .u-success-dark {
            color: #53c21d
        }

        .u-error-dark {
            color: #e45656
        }

        .u-info-dark {
            color: #767a82
        }

        .u-primary-dark-bg {
            background-color: #398ade
        }

        .u-warning-dark-bg {
            background-color: #f1a532
        }

        .u-success-dark-bg {
            background-color: #53c21d
        }

        .u-error-dark-bg {
            background-color: #e45656
        }

        .u-info-dark-bg {
            background-color: #767a82
        }

        .u-primary-disabled {
            color: #9acafc
        }

        .u-warning-disabled {
            color: #f9d39b
        }

        .u-success-disabled {
            color: #a9e08f
        }

        .u-error-disabled {
            color: #f7b2b2
        }

        .u-info-disabled {
            color: #c4c6c9
        }

        .u-primary {
            color: #3c9cff
        }

        .u-warning {
            color: #f9ae3d
        }

        .u-success {
            color: #5ac725
        }

        .u-error {
            color: #f56c6c
        }

        .u-info {
            color: #909399
        }

        .u-primary-bg {
            background-color: #3c9cff
        }

        .u-warning-bg {
            background-color: #f9ae3d
        }

        .u-success-bg {
            background-color: #5ac725
        }

        .u-error-bg {
            background-color: #f56c6c
        }

        .u-info-bg {
            background-color: #909399
        }

        .u-main-color {
            color: #303133
        }

        .u-content-color {
            color: #606266
        }

        .u-tips-color {
            color: #909193
        }

        .u-light-color {
            color: #c0c4cc
        }

        .u-safe-area-inset-top {
            padding-top: 0;
            padding-top: constant(safe-area-inset-top);
            padding-top: env(safe-area-inset-top)
        }

        .u-safe-area-inset-right {
            padding-right: 0;
            padding-right: constant(safe-area-inset-right);
            padding-right: env(safe-area-inset-right)
        }

        .u-safe-area-inset-bottom {
            padding-bottom: 0;
            padding-bottom: constant(safe-area-inset-bottom);
            padding-bottom: env(safe-area-inset-bottom)
        }

        .u-safe-area-inset-left {
            padding-left: 0;
            padding-left: constant(safe-area-inset-left);
            padding-left: env(safe-area-inset-left)
        }

        uni-toast {
            z-index: 10090
        }

        uni-toast .uni-toast {
            z-index: 10090
        }

        ::-webkit-scrollbar {
            display: none;
            width: 0 !important;
            height: 0 !important;
            -webkit-appearance: none;
            background: transparent
        }

        /*每个页面公共css */</style>
    <style type="text/css">@charset "UTF-8";

    @font-face {
        font-family: YouSheBiaoTiHei;
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
    }

    .footer-text[data-v-a1ac798e] {
        margin-top: 27px
    }

    .footer-text .text-title[data-v-a1ac798e] {
        margin-bottom: 11px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #745959;
        line-height: 30px
    }

    .footer-text .text-content[data-v-a1ac798e] {
        font-size: 12px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #524354;
        line-height: 24px
    }

    .question-counter__wrapper[data-v-a1ac798e] {
        display: flex;
        justify-content: space-between
    }

    .question-counter__wrapper .index[data-v-a1ac798e] {
        display: inline-block;
        font: 600 28px/40px PingFangSC-Semibold, PingFang SC;
        color: #2f3038;
        border-bottom: 4px solid var(--counter-border-color);
        margin-right: 5px
    }

    .question-counter__wrapper .total[data-v-a1ac798e] {
        font: 500 15px/21px PingFangSC-Medium, PingFang SC;
        color: #c5c5c5
    }</style>
    <style type="text/css">@charset "UTF-8";

    @font-face {
        font-family: YouSheBiaoTiHei;
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
    }

    .footer-text[data-v-25ee296f] {
        margin-top: 27px
    }

    .footer-text .text-title[data-v-25ee296f] {
        margin-bottom: 11px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #745959;
        line-height: 30px
    }

    .footer-text .text-content[data-v-25ee296f] {
        font-size: 12px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #524354;
        line-height: 24px
    }

    .question-item[data-v-25ee296f] {
        margin-bottom: 22px;
        padding: 16px 22px 30px 22px;
        box-shadow: 0 0 6px 6px rgba(0, 0, 0, .03);
        border-radius: 11px 11px 11px 11px;
        background: #fff;
        opacity: 1
    }</style>
    <style type="text/css">@charset "UTF-8";

    @font-face {
        font-family: YouSheBiaoTiHei;
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
    }

    .footer-text[data-v-0a67f789] {
        margin-top: 27px
    }

    .footer-text .text-title[data-v-0a67f789] {
        margin-bottom: 11px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #745959;
        line-height: 30px
    }

    .footer-text .text-content[data-v-0a67f789] {
        font-size: 12px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #524354;
        line-height: 24px
    }

    .question-stem__title[data-v-0a67f789] {
        display: flex;
        margin-top: 22px;
        letter-spacing: 0;
        font: 400 16px/26px PingFangSC-Regular, PingFang SC;
        color: #303113
    }

    .question-stem__title img[data-v-0a67f789] {
        margin-right: 1000px
    }

    .stem-wrapper[data-v-0a67f789] {
        display: inline-block;
        flex: 1;
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #070922;
        line-height: 27px
    }

    .stem-wrapper p[data-v-0a67f789] {
        margin: 0
    }

    .stem-wrapper img[data-v-0a67f789] {
        width: 100%;
        max-width: 650px
    }

    .stem-wrapper > *[data-v-0a67f789] {
        width: 100%;
        max-width: 650px
    }

    .stem-wrapper uni-image[data-v-0a67f789] {
        width: 100%;
        max-width: 650px
    }

    .type[data-v-0a67f789] {
        width: -webkit-fit-content;
        width: fit-content
    }</style>
    <style type="text/css">@charset "UTF-8";

    @font-face {
        font-family: YouSheBiaoTiHei;
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
    }

    .footer-text[data-v-33026d7d] {
        margin-top: 27px
    }

    .footer-text .text-title[data-v-33026d7d] {
        margin-bottom: 11px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #745959;
        line-height: 30px
    }

    .footer-text .text-content[data-v-33026d7d] {
        font-size: 12px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #524354;
        line-height: 24px
    }

    .question-step[data-v-33026d7d] {
        display: flex;
        justify-content: space-between;
        align-items: center
    }

    .question-step .question-step__operate[data-v-33026d7d] {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 17px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: #fff;
        line-height: 20px
    }

    .question-step .question-step__done[data-v-33026d7d] {
        height: 37px;
        padding: 4px 13px;
        background: var(--submit-button-background-color);
        color: var(--submit-button-text-color);
        box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, .3);
        border-radius: 19px 19px;
        opacity: 1
    }

    .question-step img[data-v-33026d7d] {
        width: 24px;
        height: 24px
    }</style>
    <style type="text/css">@charset "UTF-8";

    @font-face {
        font-family: YouSheBiaoTiHei;
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
    }

    .footer-text[data-v-593af844] {
        margin-top: 27px
    }

    .footer-text .text-title[data-v-593af844] {
        margin-bottom: 11px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #745959;
        line-height: 30px
    }

    .footer-text .text-content[data-v-593af844] {
        font-size: 12px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #524354;
        line-height: 24px
    }

    .question-type__single[data-v-593af844] {
        margin-top: 25px
    }

    .question-type__single-label[data-v-593af844] {
        position: relative;
        display: flex;
        padding: 15px 15px;
        background: var(--single-label-background-color);
        border-radius: 10px 10px 10px 10px;
        opacity: 1;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all .5s
    }

    .question-type__single-label .char[data-v-593af844], .question-type__single-label .text[data-v-593af844] {
        margin: unset;
        font-size: 15px;
        font-family: PingFang SC-Semibold, PingFang SC;
        font-weight: 600;
        color: var(--single-label-text-color);
        line-height: 25px
    }

    .question-type__single-label .char img[data-v-593af844], .question-type__single-label .text img[data-v-593af844] {
        max-width: 100%
    }

    .question-type__single-label-item[data-v-593af844] {
        display: flex;
        z-index: 9
    }

    .question-type__single-label-item .char img[data-v-593af844], .question-type__single-label-item .text img[data-v-593af844] {
        width: 100%;
        max-width: 100%
    }

    .question-type__single .is-active[data-v-593af844] {
        font: 600 16px/25px PingFangSC-Semibold, PingFang SC;
        color: #353ee8;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .15)
    }

    .question-type__single .is-active .char[data-v-593af844], .question-type__single .is-active .text[data-v-593af844] {
        color: var(--single-label-active-text-color)
    }

    .question-type__single-progress[data-v-593af844] {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 100%;
        background-color: var(--single-label-active-background-color);
        border-radius: 10px 10px 10px 10px
    }

    .question-type__single-progress.is-active[data-v-593af844] {
        right: 0;
        transition: right .8s
    }</style>
    <style type="text/css">@charset "UTF-8";

    @font-face {
        font-family: YouSheBiaoTiHei;
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
    }

    .footer-text[data-v-0ae4b007] {
        margin-top: 27px
    }

    .footer-text .text-title[data-v-0ae4b007] {
        margin-bottom: 11px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #745959;
        line-height: 30px
    }

    .footer-text .text-content[data-v-0ae4b007] {
        font-size: 12px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #524354;
        line-height: 24px
    }

    .layout[data-v-0ae4b007] {
        display: flex;
        justify-content: center;
        align-content: center;
        min-height: 100vh;
        background: var(--layout-background-img);
        background-blend-mode: darken, hue, overlay, color, color-dodge, difference, normal;
        background-repeat: repeat;
        background-size: cover
    }

    @media screen and (max-width: 552px) {
        .layout[data-v-0ae4b007] {
            display: inherit;
            background: var(--layout-background-img-m) !important;
            background-size: cover !important;
            background-repeat: no-repeat !important
        }
    }</style>
    <style type="text/css">@charset "UTF-8";

    @font-face {
        font-family: YouSheBiaoTiHei;
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.eot);
        src: url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff2) format("woff2"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("woff"), url(/Public/mbit/question1_files/396385b48ad7cb2df0fea7b96a899414.woff) format("truetype")
    }

    .footer-text[data-v-4cd5a61b] {
        margin-top: 27px
    }

    .footer-text .text-title[data-v-4cd5a61b] {
        margin-bottom: 11px;
        font-size: 15px;
        font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
        font-weight: 500;
        color: #745959;
        line-height: 30px
    }

    .footer-text .text-content[data-v-4cd5a61b] {
        font-size: 12px;
        font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
        font-weight: 400;
        color: #524354;
        line-height: 24px
    }

    .answer[data-v-4cd5a61b] {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 66px 11px 0 11px
    }

    .answer-header[data-v-4cd5a61b] {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center
    }

    .answer-header__title[data-v-4cd5a61b] {
        font-size: 33px;
        font-family: Alibaba PuHuiTi-Bold, Alibaba PuHuiTi;
        font-weight: 700;
        color: var(--title-text-color);
        line-height: 38px;
        letter-spacing: 1px;
        text-shadow: 0 2px 5px rgba(0, 0, 0, .4)
    }

    .answer-header__tips[data-v-4cd5a61b] {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        /*margin-top: 16px;*/
        font-size: 16px;
        font-family: Alibaba PuHuiTi-Bold, Alibaba PuHuiTi;
        font-weight: 700;
        color: var(--title-text-color);
        text-align: center
    }

    .answer-header__tips > uni-view[data-v-4cd5a61b] {
        line-height: 35px;
        text-align: center;
        text-shadow: 0 2px 5px rgba(0, 0, 0, .4)
    }

    .answer-header .invite-info[data-v-4cd5a61b] {
        position: absolute;
        top: 44px;
        left: 0;
        padding: 4px 27px 4px 11px;
        display: flex;
        align-items: center;
        align-self: flex-start;
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #252525;
        line-height: 26px;
        background: hsla(0, 0%, 100%, .5);
        border-radius: 0px 16px 16px 0
    }

    .answer-header .invite-info .userinfo[data-v-4cd5a61b] {
        display: flex;
        align-items: center;
        justify-content: center
    }

    .answer-header .invite-info .userinfo .avatar[data-v-4cd5a61b] {
        width: 22px;
        height: 22px;
        margin: 0 12px 0 8px;
        border-radius: 50%
    }

    .answer-content[data-v-4cd5a61b] {
        width: 100%
    }

    .answer-footer[data-v-4cd5a61b] {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center
    }

    .answer-footer .mbti-desc[data-v-4cd5a61b] {
        font-size: 13px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        /*color: #000;*/
        color: #525252;
        line-height: 19px;
        margin: 16px 0 33px 0
    }

    .answer-footer .mbti-icon[data-v-4cd5a61b] {
        width: 62px;
        height: 46px
    }

    .answer .custom-question-item__class[data-v-4cd5a61b] {
        margin-top: 35px;
        margin-left: auto;
        margin-right: auto
    }

    .answer .rocket-fire[data-v-4cd5a61b] {
        -webkit-transform-origin: 50% bottom;
        transform-origin: 50% bottom;
        -webkit-animation: shake .8s forwards ease-in-out, shoot .2s forwards ease-in-out 1s;
        animation: shake .8s forwards ease-in-out, shoot .2s forwards ease-in-out 1s
    }

    .record[data-v-4cd5a61b] {
        display: none
    }

    .custom-question-item[data-v-4cd5a61b] {
        min-height: 342px
    }

    @media screen and (min-width: 794px) {
        .answer[data-v-4cd5a61b] {
            width: 100%;
            padding: 22px 298px 0 298px
        }

        .record[data-v-4cd5a61b] {
            display: block;
            margin-bottom: 33px;
            font-size: 13px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #fff;
            line-height: 22px;
            text-align: center
        }
    }

    @media screen and (min-width: 790px) {
        .answer[data-v-4cd5a61b] {
            padding-top: 0 !important
        }

        .custom-question-item__class[data-v-4cd5a61b] {
            width: 702px
        }

        .question-step[data-v-4cd5a61b] {
            width: 702px;
            margin-left: auto !important;
            margin-right: auto !important
        }
    }

    @media screen and (max-width: 910px) {
        .answer-header__tips_scl[data-v-4cd5a61b] {
            font-size: 13px
        }
    }

    .customerService[data-v-4cd5a61b] {
        position: fixed;
        right: 0;
        top: 71px;
        width: 30px;
        height: 80px;
        box-sizing: border-box;
        padding: 15px 9px;
        background: #fff;
        box-shadow: -2px -2px 4px 1px rgba(0, 0, 0, .05), 0 4px 4px 1px rgba(0, 0, 0, .05);
        border-radius: 5px 0 0 5px;
        z-index: 999;
        font-size: 13px;
        font-family: Roboto-Regular, Roboto;
        font-weight: 400;
        color: #5280ff;
        line-height: 15px
    }

    @media screen and (min-width: 993px) {
        .customerService[data-v-4cd5a61b] {
            display: none
        }
    }
    
    /* 调整弹出层内容字体大小 */
    .layui-layer-msg .layui-layer-content {
        font-size: 14px; /* 可根据需求调整 */
    }

    /* 调整弹出层按钮字体大小 */
    .layui-layer-btn .layui-layer-btn0,
    .layui-layer-btn .layui-layer-btn1 {
        font-size: 13px; /* 可根据需求调整 */
    }
    .layui-layer-btn .layui-layer-btn0{
        background-color: #ffffff;
        color: #5c5c5c;
        border-color:#ffffff;
    }

    .layui-layer-btn .layui-layer-btn1{
        background-color: #FFB6E1;
        color: #ffffff;
        border-color: #FFB6E1;
    }
    
    </style>
    <style type="text/css">
        #_copy{align-items:center;background:#4494d5;border-radius:3px;color:#fff;cursor:pointer;display:flex;font-size:13px;height:30px;justify-content:center;position:absolute;width:60px;z-index:1000}#select-tooltip,#sfModal,.modal-backdrop,div[id^=reader-helper]{display:none!important}.modal-open{overflow:auto!important}._sf_adjust_body{padding-right:0!important}.super_copy_btns_div{position:fixed;width:154px;left:10px;top:45%;background:#e7f1ff;border:2px solid #4595d5;font-weight:600;border-radius:2px;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;z-index:5000}.super_copy_btns_logo{width:100%;background:#4595d5;text-align:center;font-size:12px;color:#e7f1ff;line-height:30px;height:30px}.super_copy_btns_btn{display:block;width:128px;height:28px;background:#7f5711;border-radius:4px;color:#fff;font-size:12px;border:0;outline:0;margin:8px auto;font-weight:700;cursor:pointer;opacity:.9}.super_copy_btns_btn:hover{opacity:.8}.super_copy_btns_btn:active{opacity:1}
    </style>
</head>
<body class="uni-body pages-answer-answer"
      style="--layout-background:linear-gradient(121.28deg, rgb(220, 132, 0) 0%, rgb(255, 255, 255) 40.08%), linear-gradient(140.54deg, rgb(255, 0, 0) 0%, rgb(0, 71, 255) 72.37%), linear-gradient(121.28deg, rgb(0, 227, 132) 0%, rgb(255, 0, 0) 100%), linear-gradient(121.28deg, rgb(250, 0, 255) 0%, rgb(0, 255, 56) 100%), linear-gradient(127.43deg, rgb(0, 240, 255) 0%, rgb(168, 0, 0) 100%), radial-gradient(100.47% 100% at 50% 100%, rgb(112, 255, 0) 0%, rgb(104, 1, 153) 104%), linear-gradient(127.43deg, rgb(183, 213, 0) 0%, rgb(34, 0, 170) 140%); --layout-background-img:url(/Public/mbti_result_files/<EMAIL>); --layout-background-img-m:url(/Public/mbti_result_files/<EMAIL>); --title-text-color:#ffffff; --select-item-background:#FDA9DA; --select-item-title-color:#FF9D9D; --select-start-button-background:linear-gradient(180deg, #FFB6E1 0%, #EF7BC3 100%); --select-start-button:#ffffff; --code__h5:RGBA(0, 0, 0, 1); --counter-border-color:#FF99EB; --single-label-text-color:#767676; --single-label-active-text-color:#EB56AD; --single-label-background-color:#FAFAFA; --single-label-active-background-color:#FFE2F3; --submit-button-background-color:linear-gradient(180deg,#FFB6E1,#EF7BC3); --submit-button-text-color:#ffffff; --to-mini-program-button-background:linear-gradient(180deg,#b389e9,#9479e1); --follow-text-color:rgb(255, 184, 184); --entry-title-color:#202020; --pay-sheet-bg:#F9F8FD; --pay-sheet-border:#836ED1;">
<noscript><strong>Please enable JavaScript to continue.</strong></noscript>
<uni-app class="uni-app--maxwidth">
    <uni-page data-page="pages/answer/answer"><!----><!---->
        <uni-page-wrapper>
            <uni-page-body>
                <uni-view data-v-4cd5a61b="" style="position: relative;">
                    <uni-view data-v-0ae4b007="" data-v-4cd5a61b="" class="layout"
                              style="background-image: var(--layout-background-img); background-position-x: ; background-position-y: ; background-size: cover; background-repeat-x: ; background-repeat-y: ; background-attachment: ; background-origin: ; background-clip: ; background-color: ; background-blend-mode: darken, hue, overlay, color, color-dodge, difference, normal;">
                        <uni-view data-v-4cd5a61b="" class="answer">
                            <uni-view data-v-4cd5a61b="" class="answer-header"><!---->
                                <uni-view data-v-4cd5a61b="" class="answer-header__title" style="margin-top: 0px;">
                                    2025国际版MBTI题库
                                </uni-view>
                                <uni-view data-v-4cd5a61b="" class="answer-header__tips">
                                    <!--<uni-view data-v-4cd5a61b="">国内中大型企业在用的测评工具</uni-view>-->
                                    <!--<uni-view data-v-4cd5a61b="">被广泛运用于职业发展和情感沟通等领域</uni-view>-->
                                    <uni-view data-v-4cd5a61b="">目前使用人数第一的MBTI测试</uni-view>
                                    <uni-view data-v-4cd5a61b="">被广泛用于职业发展及恋爱社交等领域</uni-view>
                                </uni-view>
                            </uni-view>
                            <uni-view data-v-4cd5a61b="" id="content-wrapper" class="answer-content">
                                <uni-view data-v-25ee296f="" data-v-4cd5a61b=""
                                          class="question-item custom-question-item__class" style="min-height: 342px;">
                                    <uni-view data-v-a1ac798e="" data-v-4cd5a61b="" class="question-counter__wrapper">
                                        <uni-view data-v-a1ac798e="" style="display: flex; align-items: center;">
                                            <uni-text data-v-a1ac798e="" class="index"><span id="index_span">01</span>
                                            </uni-text>
                                            <uni-text data-v-a1ac798e="" class="total">
                                                <span>/<?php echo count($questionList); ?></span></uni-text>
                                        </uni-view>
                                       
                                        
                                        <uni-view data-v-2ba2ad79="" data-v-4cd5a61b="" class="question-tip"
                                                  style="display: none;">
                                            <uni-image data-v-2ba2ad79="" class="info">
                                                <img src="/Public/mbit/question1_files/info-circle-fill.png"
                                                     draggable="false">
                                                <!----></uni-image><!----><!----></uni-view>
                                    </uni-view>
                                    <uni-view data-v-a1ac798e="" class="progress-bar" style="width: 100%;margin-top:8px;height:4px;background:#eee;border-radius:2px;">
                                        <uni-view data-v-a1ac798e="" class="progress-fill" style="width:0%;height:100%;background:#FFB6E1;border-radius:2px;transition:width 0.3s ease;"></uni-view>
                                    </uni-view>
                                    <?php if(is_array($questionList) || $questionList instanceof \think\Collection || $questionList instanceof \think\Paginator): $i = 0; $__LIST__ = $questionList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <div id="index_box_<?php echo $key+1; ?>" <?php if($key > '0'): ?>style="display:none;" <?php endif; ?> >
                                        <uni-view data-v-0a67f789="" data-v-4cd5a61b="" class="question-stem__title">
                                        <uni-view data-v-0a67f789="" class="stem-wrapper">
                                            <uni-rich-text data-v-0a67f789="" type="node">
                                                <div style="position: relative;"><?php echo $v; ?>
                                                    <uni-resize-sensor>
                                                        <div>
                                                            <div></div>
                                                        </div>
                                                        <div>
                                                            <div></div>
                                                        </div>
                                                    </uni-resize-sensor>
                                                </div>
                                            </uni-rich-text>
                                        </uni-view>
                                    </uni-view>
                                    <uni-view data-v-593af844="" data-v-4cd5a61b="" class="question-type__single">
                                    <uni-view data-v-593af844="" class="question-type__single-label" data-index="<?php echo $key+1; ?>" data-answer="A">
                                        <uni-view data-v-593af844="" class="question-type__single-label-item">
                                            <!--<uni-text data-v-593af844="" class="char"><span>A:</span></uni-text>-->
                                            <uni-rich-text data-v-593af844="" type="node" class="text">
                                                <div style="position: relative;"><?php echo $aList[$key]; ?>
                                                    <uni-resize-sensor>
                                                        <div>
                                                            <div></div>
                                                        </div>
                                                        <div>
                                                            <div></div>
                                                        </div>
                                                    </uni-resize-sensor>
                                                </div>
                                            </uni-rich-text>
                                        </uni-view>
                                        <uni-view data-v-593af844=""
                                                  class="question-type__single-progress"></uni-view>
                                    </uni-view>
                                    <uni-view data-v-593af844="" class="question-type__single-label" data-index="<?php echo $key+1; ?>" data-answer="B">
                                        <uni-view data-v-593af844="" class="question-type__single-label-item">
                                            <!--<uni-text data-v-593af844="" class="char"><span>B:</span></uni-text>-->
                                            <uni-rich-text data-v-593af844="" type="node" class="text">
                                                <div style="position: relative;"><?php echo $bList[$key]; ?>
                                                    <uni-resize-sensor>
                                                        <div>
                                                            <div></div>
                                                        </div>
                                                        <div>
                                                            <div></div>
                                                        </div>
                                                    </uni-resize-sensor>
                                                </div>
                                            </uni-rich-text>
                                        </uni-view>
                                        <uni-view data-v-593af844=""
                                                  class="question-type__single-progress"></uni-view>
                                    </uni-view>
                                </uni-view>
                                </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </uni-view>
                        <uni-view data-v-4cd5a61b="" class="custom-question-item__class"
                                  style="margin-top: 0px; margin-bottom: 0px;">
                            <uni-view data-v-33026d7d="" data-v-4cd5a61b="" class="question-step"
                                      style="margin: 33px 0px 15px;">
                                <uni-view data-v-33026d7d="" class="question-step__operate question-step__prev"
                                          style="cursor: pointer;"><img data-v-33026d7d=""
                                                                        src="/Public/mbit/question1_files/<EMAIL>">上一题
                                </uni-view>
                                <!--<uni-view data-v-33026d7d="" class="question-step__operate question-step__next"
                                          id="next_btn"
                                          style="cursor: pointer;">下一题<img data-v-33026d7d="" src="/Public/mbit/question1_files/<EMAIL>">
                                </uni-view>-->

                                <uni-view data-v-33026d7d=""
                                          class="question-step__operate question-step__next question-step__done"
                                          id="result_btn" style="cursor: pointer;display: none;">查看报告</uni-view>
                            </uni-view>
                        </uni-view>
                    </uni-view>
                    <!--<uni-view data-v-4cd5a61b="" class="answer-footer custom-question-item__class"
                              style="margin-top: 0px; margin-bottom: 0px;">
                        <uni-view data-v-4cd5a61b="" class="mbti-desc">
                            <uni-view data-v-4cd5a61b="">[1] 测评基于瑞士心理学家荣格(Carl Jung)的《人格分类》理论，及美国心理学家迈尔斯(Isabel
                                Briggs Myers)与其母亲凯瑟琳·库克·布里格斯（Katharine Cook Briggs）的实证研究，是国际十分流行的性格测试模型。
                            </uni-view>
                            <uni-view data-v-4cd5a61b="">[2] 测评时间预计<?php echo $type==1?'8-16':'5-10'; ?>分钟，请在心态平和及时间充足的情况下开始答题。</uni-view>
                            <uni-view data-v-4cd5a61b="">[3] 选项间无对错好坏之分，请选择与你实际做法相符的，而不是你认为怎样做是对的。</uni-view>
                        </uni-view>
                        <uni-view data-v-4cd5a61b="" class="record"><?php echo $gsetinfo['copyright']; ?></uni-view>
                    </uni-view>-->
                            <div class="custom_bottom_container">
                                <div class="custom_bottom_container_title">
                                    完成测试后，您将获得
                                </div>
                                <div class="custom_bottom_container_mingxi">
                                    <div class="custom_bottom_container_mingxi_left">
                                        <div class="custom_bottom_container_mingxi_item">获取您的4字母类型测试结果</div>
                                        <div class="custom_bottom_container_mingxi_item">知悉您的偏好优势和类型描述</div>
                                        <div class="custom_bottom_container_mingxi_item">了解您的沟通风格和学习风格</div>
                                    </div>
                                    <div class="custom_bottom_container_mingxi_right">
                                        <div class="custom_bottom_container_mingxi_item">发现适合您性格类型的职业</div>
                                        <div class="custom_bottom_container_mingxi_item">评估您与恋人的长期相处情况</div>
                                        <div class="custom_bottom_container_mingxi_item">查看与您分享同一性格的名人</div>
                                    </div>
                                </div>
                               <!-- <div class="custom_bottom_container_bottom">
                                    所有内容基于卡尔·荣格（Carl Jung）和伊莎贝尔·布里格斯·迈尔斯（Isabel Briggs Myers）的MBTI理论实证
                                </div>
                                <div class="custom_bottom_container_title" style="margin-top:20px;">
                                    MBTI十六人格测试须知
                                </div>
                                <div class="custom_bottom_container_mingxi">
                                    <div class="custom_bottom_container_mingxi_item">1、参加测试的人员请务必诚实、独立地回答问题，只有如此，才能得到有效的结果。</div>
                                    <div class="custom_bottom_container_mingxi_item">2、性格分析展示的是你的性格倾向，而不是你的知识、技能、经验。</div>
                                    <div class="custom_bottom_container_mingxi_item">3、MBTI 提供的性格类型描述仅供测试者确定自己的性格类型之用，性格类型没有好坏，只有不同，每一种性格特征都有其价值和优点。</div>
                                    <div class="custom_bottom_container_mingxi_item">4、题目无对错之分，请根据实际情况选择。测试需付费后方可查看结果，结果纯属娱乐仅供参考。</div>
                                </div>-->
                                <uni-view style="margin-bottom:20px;font-size: 12px; text-align: center"><?php echo $site['beian']; ?></uni-view>
                                <uni-view style="margin-bottom:80px;font-size: 12px; text-align: center">青岛字符跳动技术服务有限公司</uni-view>

<!--                                <div style="text-align: center;line-height: 22px;"><?php echo $gsetinfo['copyright']; ?></div>-->
                            </div>
                </uni-view>
                </uni-view>
                <uni-view data-v-0d0d463d="" data-v-4cd5a61b="" class="u-toast"><!----></uni-view><!----></uni-view>
            </uni-page-body>
        </uni-page-wrapper>
    </uni-page>
    <script>(function() {var _53code = document.createElement("script");_53code.src = "https://tb.53kf.com/code/code/d01e4433b000b027d698e4349017e8260/1";var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(_53code, s);})();</script>
<script>
    var _hmt = _hmt || [];
    (function() {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?cb34373021da829d3228023d871f0661";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script>
   
</body>
<style>
    .custom_bottom_container{font-size: 12px;color: #525252;}
    .custom_bottom_container_title{text-align: center;}
    .custom_bottom_container_mingxi{display: flex;flex-wrap: wrap;width: 100%;box-sizing: border-box;justify-content: space-between; margin: 10px 0;}
    .custom_bottom_container_mingxi_item{box-sizing: border-box; line-height: 22px;}
    .custom_bottom_container_mingxi_item:before{content: "·"}
    .custom_bottom_container_bottom{margin:0 0 20px 0;}
</style>
<script>
   


    //history.pushState(null, null, location.href);
    if (window.history.state === null) {
        history.pushState(null, null, location.href);
        // history.pushState(null, null, location.pathname+location.search+"#"+new Date().getTime());
    }
    
    function popstateHandleCustormer(event) {
      

        // history.go(1);
        layer.confirm('是继续测试还是重新选题', {
            btn: ['重新选题', '继续测试'],
            btnStyle: ['background-color: white; color: #333;', 'background-color: blue; color: white;']
        }, function() {
            // 重新选题，跳转到相应页面
            // window.removeEventListener('popstate', popstateHandleCustormer);
            // window.history.replaceState(null, '', window.location.href);
            // const state = { page: 'page-b' };
            // history.replaceState(null, null, "<?php echo url('/'); ?>");
            // history.go(0)
            window.location.href = "<?php echo url('/'); ?>";
            // window.location.replace("<?php echo url('/'); ?>");
            // window.history.go(-1)


        }, function() {
            // 继续测试，关闭弹框
            layer.closeAll();
            // 阻止默认的返回操作

            history.pushState(null, null, location.href);
            // history.pushState({state:2}, null, location.pathname+location.search+"#"+new Date().getTime());
        });
        // 阻止默认的后退行为
        // event.preventDefault();
    }
    window.addEventListener('popstate',popstateHandleCustormer );
    // window.addEventListener('hashchange',popstateHandleCustormer,false );
    localStorage.removeItem('hasVisitedPayPage');

</script>
<script>
    var index = 1;
    var tcount = '<?php echo count($questionList); ?>';
    var select_answer = {};
    var type = '<?php echo $type; ?>';
    var isAnswering = false; // 添加答题状态标志
    var clickCooldown = 1000; // 点击冷却时间（毫秒）

    $(function () {

        localStorage.setItem('discountApplied', 'false');

        $(".question-type__single-label").click(function () {
            // 检查是否在答题渲染期间
            if (isAnswering) {
                return false; // 阻止连续点击
            }

            // 设置答题状态为true，防止连续点击
            isAnswering = true;

            $(this).addClass('is-active').siblings().removeClass('is-active');
            $(this).find('.question-type__single-progress').addClass('is-active').end().siblings().find('.question-type__single-progress').removeClass('is-active');
            index = $(this).data('index') * 1;
            select_answer[index] = $(this).data('answer');

            if (index == tcount) {
                $("#next_btn").hide();
                $("#result_btn").show();
                // 最后一题渲染完成后自动提交
                setTimeout(function() {
                    $("#result_btn").click();
                }, 1000);
                return;
            }

            console.log($(this).data())
            select_answer[index] = $(this).data('answer');
            console.log(select_answer)
            index++;
            if (index < 10) {
                $("#index_span").html('0'+index);
            } else {
                $("#index_span").html(index);
            }

            // 更新进度条渲染
            const progress = ((index - 1) / tcount * 100).toFixed(2);
            $('.progress-fill').css('width', progress + '%');

            // 进度渲染完成后显示下一题
            setTimeout(function (){
                $("#index_box_" + index).show();
                if (index > 0) {
                    $("#index_box_" + (index - 1)).hide();
                }

                // 下一题显示完成后重新允许点击
                isAnswering = false;

            }, 1000) // 1秒渲染时间

        })
        $(".question-step__prev").click(function (){
            if(index == 1) {
                return;
            }
            index--;
            $("#index_box_" + index).show();
            $("#index_box_" + (index + 1)).hide();
            if (index < 10) {
                $("#index_span").html('0'+index);
            } else {
                $("#index_span").html(index);
            }
            $("#next_btn").show();
            $("#result_btn").hide();
            // 新增进度更新
            const progress = ((index - 1) / tcount * 100).toFixed(2);
            $('.progress-fill').css('width', progress + '%');
        })
        $("#result_btn").click(function () {
            // var loadindex = layer.load();
            var loadindex = layer.msg('报告生成中', {icon: 6,shade: [0.8, '#393D49'],time: 20000});
            $.post('handel_order', {select_answer, type}, function (res) {
                if(res['code'] === 0) {
                    window.location.href = res.url;
                    layer.close(loadindex);
                    /*setTimeout(function (){

                    },3000)*/
                }
                else {
                    layer.close(loadindex);
                    layer.msg(res['msg']);
                }
            })
        })
 
    })
</script>
</html>