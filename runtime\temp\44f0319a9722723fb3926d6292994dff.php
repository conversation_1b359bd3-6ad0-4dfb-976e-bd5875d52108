<?php if (!defined('THINK_PATH')) exit(); /*a:3:{s:94:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\public/../application/mbti/view/default/result\ISTP.html";i:1750855451;s:90:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\application\mbti\view\default\public\twopay_btn.html";i:1745892302;s:86:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\application\mbti\view\default\public\twopay.html";i:1751376328;}*/ ?>
<!DOCTYPE html>
<html style="font-size: 25.875px;" class="hydrated"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><style data-styles="">taro-checkbox-core,taro-checkbox-group-core,taro-radio-core,taro-radio-group-core,taro-swiper-core,taro-swiper-item-core,taro-audio-core,taro-block-core,taro-button-core,taro-camera-core,taro-canvas-core,taro-cover-image-core,taro-cover-view-core,taro-custom-wrapper-core,taro-form-core,taro-icon-core,taro-image-core,taro-input-core,taro-label-core,taro-movable-area-core,taro-movable-view-core,taro-navigator-core,taro-open-data-core,taro-picker-view-column-core,taro-picker-view-core,taro-progress-core,taro-pull-to-refresh,taro-rich-text-core,taro-scroll-view-core,taro-slider-core,taro-switch-core,taro-tabbar,taro-text-core,taro-textarea-core,taro-view-core,taro-web-view-core,taro-picker-core,taro-picker-group,taro-video-core,taro-video-control,taro-video-danmu{visibility:hidden}.hydrated{visibility:inherit}</style><style>/*!
html{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{line-height:1.6;font-family:-apple-system-font,Helvetica Neue,sans-serif}*{margin:0;padding:0}a img{border:0}a{text-decoration:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}@font-face{font-weight:400;font-style:normal;font-family:weui;src:url('data:application/octet-stream;base64,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') format('truetype')}[class*=" weui-icon-"],[class^=weui-icon-]{display:inline-block;vertical-align:middle;font:normal normal normal 14px/1 weui;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased}[class*=" weui-icon-"]:before,[class^=weui-icon-]:before{display:inline-block;margin-left:.2em;margin-right:.2em}.weui-icon-circle:before{content:"\EA01"}.weui-icon-download:before{content:"\EA02"}.weui-icon-info:before{content:"\EA03"}.weui-icon-safe-success:before{content:"\EA04"}.weui-icon-safe-warn:before{content:"\EA05"}.weui-icon-success:before{content:"\EA06"}.weui-icon-success-circle:before{content:"\EA07"}.weui-icon-success-no-circle:before{content:"\EA08"}.weui-icon-waiting:before{content:"\EA09"}.weui-icon-waiting-circle:before{content:"\EA0A"}.weui-icon-warn:before{content:"\EA0B"}.weui-icon-info-circle:before{content:"\EA0C"}.weui-icon-cancel:before{content:"\EA0D"}.weui-icon-search:before{content:"\EA0E"}.weui-icon-clear:before{content:"\EA0F"}.weui-icon-back:before{content:"\EA10"}.weui-icon-delete:before{content:"\EA11"}[class*=" weui-icon_"]:before,[class^=weui-icon_]:before{margin:0}.weui-icon-success{font-size:23px;color:#09bb07}.weui-icon-waiting{font-size:23px;color:#10aeff}.weui-icon-warn{font-size:23px;color:#f43530}.weui-icon-info{font-size:23px;color:#10aeff}.weui-icon-success-circle,.weui-icon-success-no-circle{font-size:23px;color:#09bb07}.weui-icon-waiting-circle{font-size:23px;color:#10aeff}.weui-icon-circle{font-size:23px;color:#c9c9c9}.weui-icon-download,.weui-icon-info-circle{font-size:23px;color:#09bb07}.weui-icon-safe-success{color:#09bb07}.weui-icon-safe-warn{color:#ffbe00}.weui-icon-cancel{color:#f43530;font-size:22px}.weui-icon-clear,.weui-icon-search{color:#b2b2b2;font-size:14px}.weui-icon-delete.weui-icon_gallery-delete{color:#fff;font-size:22px}.weui-icon_msg{font-size:93px}.weui-icon_msg.weui-icon-warn{color:#f76260}.weui-icon_msg-primary{font-size:93px}.weui-icon_msg-primary.weui-icon-warn{color:#ffbe00}.weui-btn{position:relative;display:block;margin-left:auto;margin-right:auto;padding-left:14px;padding-right:14px;-webkit-box-sizing:border-box;box-sizing:border-box;font-size:18px;text-align:center;text-decoration:none;color:#fff;line-height:2.55555556;border-radius:5px;-webkit-tap-highlight-color:rgba(0,0,0,0);overflow:hidden}.weui-btn:after{content:" ";width:200%;height:200%;position:absolute;top:0;left:0;border:1px solid rgba(0,0,0,.2);-webkit-transform:scale(.5);transform:scale(.5);-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-box-sizing:border-box;box-sizing:border-box;border-radius:10px}.weui-btn_inline{display:inline-block}.weui-btn_default{color:#000;background-color:#f8f8f8}.weui-btn_default:not(.weui-btn_disabled):visited{color:#000}.weui-btn_default:not(.weui-btn_disabled):active{color:rgba(0,0,0,.6);background-color:#dedede}.weui-btn_primary{background-color:#1aad19}.weui-btn_primary:not(.weui-btn_disabled):visited{color:#fff}.weui-btn_primary:not(.weui-btn_disabled):active{color:hsla(0,0%,100%,.6);background-color:#179b16}.weui-btn_warn{background-color:#e64340}.weui-btn_warn:not(.weui-btn_disabled):visited{color:#fff}.weui-btn_warn:not(.weui-btn_disabled):active{color:hsla(0,0%,100%,.6);background-color:#ce3c39}.weui-btn_disabled{color:hsla(0,0%,100%,.6)}.weui-btn_disabled.weui-btn_default{color:rgba(0,0,0,.3);background-color:#f7f7f7}.weui-btn_disabled.weui-btn_primary{background-color:#9ed99d}.weui-btn_disabled.weui-btn_warn{background-color:#ec8b89}.weui-btn_loading .weui-loading{margin:-.2em .34em 0 0}.weui-btn_loading.weui-btn_primary,.weui-btn_loading.weui-btn_warn{color:hsla(0,0%,100%,.6)}.weui-btn_loading.weui-btn_primary{background-color:#179b16}.weui-btn_loading.weui-btn_warn{background-color:#ce3c39}.weui-btn_plain-primary{color:#1aad19;border:1px solid #1aad19}.weui-btn_plain-primary:not(.weui-btn_plain-disabled):active{color:rgba(26,173,25,.6);border-color:rgba(26,173,25,.6)}.weui-btn_plain-primary:after{border-width:0}.weui-btn_plain-default{color:#353535;border:1px solid #353535}.weui-btn_plain-default:not(.weui-btn_plain-disabled):active{color:rgba(53,53,53,.6);border-color:rgba(53,53,53,.6)}.weui-btn_plain-default:after{border-width:0}.weui-btn_plain-disabled{color:rgba(0,0,0,.2);border-color:rgba(0,0,0,.2)}button.weui-btn,input.weui-btn{width:100%;border-width:0;outline:0;-webkit-appearance:none}button.weui-btn:focus,input.weui-btn:focus{outline:0}button.weui-btn_inline,button.weui-btn_mini,input.weui-btn_inline,input.weui-btn_mini{width:auto}button.weui-btn_plain-default,button.weui-btn_plain-primary,input.weui-btn_plain-default,input.weui-btn_plain-primary{border-width:1px;background-color:transparent}.weui-btn_mini{display:inline-block;padding:0 1.32em;line-height:2.3;font-size:13px}.weui-btn+.weui-btn{margin-top:15px}.weui-btn.weui-btn_inline+.weui-btn.weui-btn_inline{margin-top:auto;margin-left:15px}.weui-btn-area{margin:1.17647059em 15px .3em}.weui-btn-area_inline{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.weui-btn-area_inline .weui-btn{margin-top:auto;margin-right:15px;width:100%;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.weui-btn-area_inline .weui-btn:last-child{margin-right:0}.weui-cells{margin-top:1.17647059em;background-color:#fff;line-height:1.47058824;font-size:17px;overflow:hidden;position:relative}.weui-cells:before{top:0;border-top:1px solid #e5e5e5;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-cells:after,.weui-cells:before{content:" ";position:absolute;left:0;right:0;height:1px;color:#e5e5e5;z-index:2}.weui-cells:after{bottom:0;border-bottom:1px solid #e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-cells__title{margin-top:.77em;margin-bottom:.3em;padding-left:15px;padding-right:15px;color:#999;font-size:14px}.weui-cells__title+.weui-cells{margin-top:0}.weui-cells__tips{margin-top:.3em;color:#999;padding-left:15px;padding-right:15px;font-size:14px}.weui-cell{padding:10px 15px;position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.weui-cell:before{content:" ";position:absolute;left:0;top:0;right:0;height:1px;border-top:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5);left:15px;z-index:2}.weui-cell:first-child:before{display:none}.weui-cell_primary{-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start}.weui-cell__bd{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.weui-cell__ft{text-align:right;color:#999}.weui-cell_swiped{display:block;padding:0}.weui-cell_swiped>.weui-cell__bd{position:relative;z-index:1;background-color:#fff}.weui-cell_swiped>.weui-cell__ft{position:absolute;right:0;top:0;bottom:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;color:#fff}.weui-swiped-btn{display:block;padding:10px 1em;line-height:1.47058824;color:inherit}.weui-swiped-btn_default{background-color:#c7c7cc}.weui-swiped-btn_warn{background-color:#ff3b30}.weui-cell_access{-webkit-tap-highlight-color:rgba(0,0,0,0);color:inherit}.weui-cell_access:active{background-color:#ececec}.weui-cell_access .weui-cell__ft{padding-right:13px;position:relative}.weui-cell_access .weui-cell__ft:after{content:" ";display:inline-block;height:6px;width:6px;border-width:2px 2px 0 0;border-color:#c8c8cd;border-style:solid;-webkit-transform:matrix(.71,.71,-.71,.71,0,0);transform:matrix(.71,.71,-.71,.71,0,0);position:relative;top:-2px;position:absolute;top:50%;margin-top:-4px;right:2px}.weui-cell_link{color:#586c94;font-size:14px}.weui-cell_link:first-child:before{display:block}.weui-check__label{-webkit-tap-highlight-color:rgba(0,0,0,0)}.weui-check__label:active{background-color:#ececec}.weui-check{position:absolute;left:-9999em}.weui-cells_radio .weui-cell__ft{padding-left:.35em}.weui-cells_radio .weui-check+.weui-icon-checked{min-width:16px}.weui-cells_radio .weui-check:checked+.weui-icon-checked:before{display:block;content:'\EA08';color:#09bb07;font-size:16px}.weui-cells_checkbox .weui-cell__hd{padding-right:.35em}.weui-cells_checkbox .weui-icon-checked:before{content:'\EA01';color:#c9c9c9;font-size:23px;display:block}.weui-cells_checkbox .weui-check:checked+.weui-icon-checked:before{content:'\EA06';color:#09bb07}.weui-label{display:block;width:105px;word-wrap:break-word;word-break:break-all}.weui-input{width:100%;border:0;outline:0;-webkit-appearance:none;background-color:transparent;font-size:inherit;color:inherit;height:1.47058824em;line-height:1.47058824}.weui-input::-webkit-inner-spin-button,.weui-input::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.weui-textarea{display:block;border:0;resize:none;width:100%;color:inherit;font-size:1em;line-height:inherit;outline:0}.weui-textarea-counter{color:#b2b2b2;text-align:right}.weui-cell_warn .weui-textarea-counter{color:#e64340}.weui-toptips{display:none;position:fixed;-webkit-transform:translateZ(0);transform:translateZ(0);top:0;left:0;right:0;padding:5px;font-size:14px;text-align:center;color:#fff;z-index:5000;word-wrap:break-word;word-break:break-all}.weui-toptips_warn{background-color:#e64340}.weui-cells_form .weui-cell__ft{font-size:0}.weui-cells_form .weui-icon-warn{display:none}.weui-cells_form input,.weui-cells_form label[for],.weui-cells_form textarea{-webkit-tap-highlight-color:rgba(0,0,0,0)}.weui-cell_warn{color:#e64340}.weui-cell_warn .weui-icon-warn{display:inline-block}.weui-form-preview{position:relative;background-color:#fff}.weui-form-preview:before{top:0;border-top:1px solid #e5e5e5;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-form-preview:after,.weui-form-preview:before{content:" ";position:absolute;left:0;right:0;height:1px;color:#e5e5e5}.weui-form-preview:after{bottom:0;border-bottom:1px solid #e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-form-preview__hd{position:relative;padding:10px 15px;text-align:right;line-height:2.5em}.weui-form-preview__hd:after{content:" ";position:absolute;left:0;bottom:0;right:0;height:1px;border-bottom:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5);left:15px}.weui-form-preview__hd .weui-form-preview__value{font-style:normal;font-size:1.6em}.weui-form-preview__bd{padding:10px 15px;font-size:.9em;text-align:right;color:#999;line-height:2}.weui-form-preview__ft{position:relative;line-height:50px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.weui-form-preview__ft:before{content:" ";position:absolute;left:0;top:0;right:0;height:1px;border-top:1px solid #d5d5d6;color:#d5d5d6;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-form-preview__item{overflow:hidden}.weui-form-preview__label{float:left;margin-right:1em;min-width:4em;color:#999;text-align:justify;-moz-text-align-last:justify;text-align-last:justify}.weui-form-preview__value{display:block;overflow:hidden;word-break:normal;word-wrap:break-word}.weui-form-preview__btn{position:relative;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;color:#3cc51f;text-align:center;-webkit-tap-highlight-color:rgba(0,0,0,0)}button.weui-form-preview__btn{background-color:transparent;border:0;outline:0;line-height:inherit;font-size:inherit}.weui-form-preview__btn:active{background-color:#eee}.weui-form-preview__btn:after{content:" ";position:absolute;left:0;top:0;width:1px;bottom:0;border-left:1px solid #d5d5d6;color:#d5d5d6;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleX(.5);transform:scaleX(.5)}.weui-form-preview__btn:first-child:after{display:none}.weui-form-preview__btn_default{color:#999}.weui-form-preview__btn_primary{color:#0bb20c}.weui-cell_select{padding:0}.weui-cell_select .weui-select{padding-right:30px}.weui-cell_select .weui-cell__bd:after{content:" ";display:inline-block;height:6px;width:6px;border-width:2px 2px 0 0;border-color:#c8c8cd;border-style:solid;-webkit-transform:matrix(.71,.71,-.71,.71,0,0);transform:matrix(.71,.71,-.71,.71,0,0);position:relative;top:-2px;position:absolute;top:50%;right:15px;margin-top:-4px}.weui-select{-webkit-appearance:none;border:0;outline:0;background-color:transparent;width:100%;font-size:inherit;height:45px;line-height:45px;position:relative;z-index:1;padding-left:15px}.weui-cell_select-before{padding-right:15px}.weui-cell_select-before .weui-select{width:105px;-webkit-box-sizing:border-box;box-sizing:border-box}.weui-cell_select-before .weui-cell__hd{position:relative}.weui-cell_select-before .weui-cell__hd:after{content:" ";position:absolute;right:0;top:0;width:1px;bottom:0;border-right:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:100% 0;transform-origin:100% 0;-webkit-transform:scaleX(.5);transform:scaleX(.5)}.weui-cell_select-before .weui-cell__hd:before{content:" ";display:inline-block;height:6px;width:6px;border-width:2px 2px 0 0;border-color:#c8c8cd;border-style:solid;-webkit-transform:matrix(.71,.71,-.71,.71,0,0);transform:matrix(.71,.71,-.71,.71,0,0);position:relative;top:-2px;position:absolute;top:50%;right:15px;margin-top:-4px}.weui-cell_select-before .weui-cell__bd{padding-left:15px}.weui-cell_select-before .weui-cell__bd:after{display:none}.weui-cell_select-after{padding-left:15px}.weui-cell_select-after .weui-select{padding-left:0}.weui-cell_vcode{padding-top:0;padding-right:0;padding-bottom:0}.weui-vcode-btn,.weui-vcode-img{margin-left:5px;height:45px;vertical-align:middle}.weui-vcode-btn{display:inline-block;padding:0 .6em 0 .7em;border-left:1px solid #e5e5e5;line-height:45px;font-size:17px;color:#3cc51f}button.weui-vcode-btn{background-color:transparent;border-top:0;border-right:0;border-bottom:0;outline:0}.weui-vcode-btn:active{color:#52a341}.weui-gallery{display:none;position:fixed;top:0;right:0;bottom:0;left:0;background-color:#000;z-index:1000}.weui-gallery__img{position:absolute;top:0;right:0;bottom:60px;left:0;background:50% no-repeat;background-size:contain}.weui-gallery__opr{position:absolute;right:0;bottom:0;left:0;background-color:#0d0d0d;color:#fff;line-height:60px;text-align:center}.weui-gallery__del{display:block}.weui-cell_switch{padding-top:6.5px;padding-bottom:6.5px}.weui-switch{-webkit-appearance:none;-moz-appearance:none;appearance:none}.weui-switch,.weui-switch-cp__box{position:relative;width:52px;height:32px;border:1px solid #dfdfdf;outline:0;border-radius:16px;-webkit-box-sizing:border-box;box-sizing:border-box;background-color:#dfdfdf;-webkit-transition:background-color .1s,border .1s;transition:background-color .1s,border .1s}.weui-switch-cp__box:before,.weui-switch:before{content:" ";position:absolute;top:0;left:0;width:50px;height:30px;border-radius:15px;background-color:#fdfdfd;-webkit-transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:-webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1), -webkit-transform .35s cubic-bezier(.45,1,.4,1);transition:transform .35s cubic-bezier(.45,1,.4,1),-webkit-transform .35s cubic-bezier(.45,1,.4,1)}.weui-switch-cp__box:after,.weui-switch:after{content:" ";position:absolute;top:0;left:0;width:30px;height:30px;border-radius:15px;background-color:#fff;-webkit-box-shadow:0 1px 3px rgba(0,0,0,.4);box-shadow:0 1px 3px rgba(0,0,0,.4);-webkit-transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35), -webkit-transform .35s cubic-bezier(.4,.4,.25,1.35);transition:transform .35s cubic-bezier(.4,.4,.25,1.35),-webkit-transform .35s cubic-bezier(.4,.4,.25,1.35)}.weui-switch-cp__input:checked~.weui-switch-cp__box,.weui-switch:checked{border-color:#04be02;background-color:#04be02}.weui-switch-cp__input:checked~.weui-switch-cp__box:before,.weui-switch:checked:before{-webkit-transform:scale(0);transform:scale(0)}.weui-switch-cp__input:checked~.weui-switch-cp__box:after,.weui-switch:checked:after{-webkit-transform:translateX(20px);transform:translateX(20px)}.weui-switch-cp__input{position:absolute;left:-9999px}.weui-switch-cp__box{display:block}.weui-uploader__hd{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding-bottom:10px;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.weui-uploader__title{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.weui-uploader__info{color:#b2b2b2}.weui-uploader__bd{margin-bottom:-4px;margin-right:-9px;overflow:hidden}.weui-uploader__files{list-style:none}.weui-uploader__file{float:left;margin-right:9px;margin-bottom:9px;width:79px;height:79px;background:no-repeat 50%;background-size:cover}.weui-uploader__file_status{position:relative}.weui-uploader__file_status:before{content:" ";position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,.5)}.weui-uploader__file_status .weui-uploader__file-content{display:block}.weui-uploader__file-content{display:none;position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);color:#fff}.weui-uploader__file-content .weui-icon-warn{display:inline-block}.weui-uploader__input-box{float:left;position:relative;margin-right:9px;margin-bottom:9px;width:77px;height:77px;border:1px solid #d9d9d9}.weui-uploader__input-box:after,.weui-uploader__input-box:before{content:" ";position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background-color:#d9d9d9}.weui-uploader__input-box:before{width:2px;height:39.5px}.weui-uploader__input-box:after{width:39.5px;height:2px}.weui-uploader__input-box:active{border-color:#999}.weui-uploader__input-box:active:after,.weui-uploader__input-box:active:before{background-color:#999}.weui-uploader__input{position:absolute;z-index:1;top:0;left:0;width:100%;height:100%;opacity:0;-webkit-tap-highlight-color:rgba(0,0,0,0)}.weui-msg{padding-top:36px;text-align:center}.weui-msg__icon-area{margin-bottom:30px}.weui-msg__text-area{margin-bottom:25px;padding:0 20px}.weui-msg__text-area a{color:#586c94}.weui-msg__title{margin-bottom:5px;font-weight:400;font-size:20px}.weui-msg__desc,.weui-msg__title{word-wrap:break-word;word-break:break-all}.weui-msg__desc{font-size:14px;color:#999}.weui-msg__opr-area{margin-bottom:25px}.weui-msg__extra-area{margin-bottom:15px;font-size:14px;color:#999}.weui-msg__extra-area a{color:#586c94}@media screen and (min-height:438px){.weui-msg__extra-area{position:fixed;left:0;bottom:0;width:100%;text-align:center}}@media only screen and (device-width:375px) and (device-height:812px) and (-webkit-device-pixel-ratio:3){.weui-msg__extra-area{margin-bottom:49px}}.weui-article{padding:20px 15px;font-size:15px}.weui-article section{margin-bottom:1.5em}.weui-article h1{font-size:18px;font-weight:400;margin-bottom:.9em}.weui-article h2{font-size:16px}.weui-article h2,.weui-article h3{font-weight:400;margin-bottom:.34em}.weui-article h3{font-size:15px}.weui-article *{max-width:100%;-webkit-box-sizing:border-box;box-sizing:border-box;word-wrap:break-word}.weui-article p{margin:0 0 .8em}.weui-tabbar{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;position:absolute;z-index:500;bottom:0;width:100%;background-color:#f7f7fa}.weui-tabbar:before{content:" ";position:absolute;left:0;top:0;right:0;height:1px;border-top:1px solid #c0bfc4;color:#c0bfc4;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-tabbar__item{display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;padding:5px 0 0;font-size:0;color:#999;text-align:center;-webkit-tap-highlight-color:rgba(0,0,0,0)}.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon,.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon>i,.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label{color:#09bb07}.weui-tabbar__icon{display:inline-block;width:27px;height:27px}.weui-tabbar__icon>i,i.weui-tabbar__icon{font-size:24px;color:#999}.weui-tabbar__icon img{width:100%;height:100%}.weui-tabbar__label{text-align:center;color:#999;font-size:10px;line-height:1.8}.weui-navbar{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;position:absolute;z-index:500;top:0;width:100%;background-color:#fafafa}.weui-navbar:after{content:" ";position:absolute;left:0;bottom:0;right:0;height:1px;border-bottom:1px solid #ccc;color:#ccc;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-navbar+.weui-tab__panel{padding-top:50px;padding-bottom:0}.weui-navbar__item{position:relative;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;padding:13px 0;text-align:center;font-size:15px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.weui-navbar__item:active{background-color:#ededed}.weui-navbar__item.weui-bar__item_on{background-color:#eaeaea}.weui-navbar__item:after{content:" ";position:absolute;right:0;top:0;width:1px;bottom:0;border-right:1px solid #ccc;color:#ccc;-webkit-transform-origin:100% 0;transform-origin:100% 0;-webkit-transform:scaleX(.5);transform:scaleX(.5)}.weui-navbar__item:last-child:after{display:none}.weui-tab{position:relative;height:100%}.weui-tab__panel{-webkit-box-sizing:border-box;box-sizing:border-box;height:100%;padding-bottom:50px;overflow:auto;-webkit-overflow-scrolling:touch}.weui-tab__content{display:none}.weui-progress{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.weui-progress__bar{background-color:#ebebeb;height:3px;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.weui-progress__inner-bar{width:0;height:100%;background-color:#09bb07}.weui-progress__opr{display:block;margin-left:15px;font-size:0}.weui-panel{background-color:#fff;margin-top:10px;position:relative;overflow:hidden}.weui-panel:first-child{margin-top:0}.weui-panel:before{top:0;border-top:1px solid #e5e5e5;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-panel:after,.weui-panel:before{content:" ";position:absolute;left:0;right:0;height:1px;color:#e5e5e5}.weui-panel:after{bottom:0;border-bottom:1px solid #e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-panel__hd{padding:14px 15px 10px;color:#999;font-size:13px;position:relative}.weui-panel__hd:after{content:" ";position:absolute;left:0;bottom:0;right:0;height:1px;border-bottom:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5);left:15px}.weui-media-box{padding:15px;position:relative}.weui-media-box:before{content:" ";position:absolute;left:0;top:0;right:0;height:1px;border-top:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5);left:15px}.weui-media-box:first-child:before{display:none}a.weui-media-box{color:#000;-webkit-tap-highlight-color:rgba(0,0,0,0)}a.weui-media-box:active{background-color:#ececec}.weui-media-box__title{font-weight:400;font-size:17px;width:auto;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-wrap:normal;word-wrap:break-word;word-break:break-all}.weui-media-box__desc{color:#999;font-size:13px;line-height:1.2;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.weui-media-box__info{margin-top:15px;padding-bottom:5px;font-size:13px;color:#cecece;line-height:1em;list-style:none;overflow:hidden}.weui-media-box__info__meta{float:left;padding-right:1em}.weui-media-box__info__meta_extra{padding-left:1em;border-left:1px solid #cecece}.weui-media-box_text .weui-media-box__title{margin-bottom:8px}.weui-media-box_appmsg{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.weui-media-box_appmsg .weui-media-box__hd{margin-right:.8em;width:60px;height:60px;line-height:60px;text-align:center}.weui-media-box_appmsg .weui-media-box__thumb{width:100%;max-height:100%;vertical-align:top}.weui-media-box_appmsg .weui-media-box__bd{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;min-width:0}.weui-media-box_small-appmsg{padding:0}.weui-media-box_small-appmsg .weui-cells{margin-top:0}.weui-media-box_small-appmsg .weui-cells:before{display:none}.weui-grids{position:relative;overflow:hidden}.weui-grids:before{right:0;height:1px;border-top:1px solid #d9d9d9;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-grids:after,.weui-grids:before{content:" ";position:absolute;left:0;top:0;color:#d9d9d9}.weui-grids:after{width:1px;bottom:0;border-left:1px solid #d9d9d9;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleX(.5);transform:scaleX(.5)}.weui-grid{position:relative;float:left;padding:20px 10px;width:33.33333333%;-webkit-box-sizing:border-box;box-sizing:border-box}.weui-grid:before{top:0;width:1px;border-right:1px solid #d9d9d9;-webkit-transform-origin:100% 0;transform-origin:100% 0;-webkit-transform:scaleX(.5);transform:scaleX(.5)}.weui-grid:after,.weui-grid:before{content:" ";position:absolute;right:0;bottom:0;color:#d9d9d9}.weui-grid:after{left:0;height:1px;border-bottom:1px solid #d9d9d9;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-grid:active{background-color:#ececec}.weui-grid__icon{width:28px;height:28px;margin:0 auto}.weui-grid__icon img{display:block;width:100%;height:100%}.weui-grid__icon+.weui-grid__label{margin-top:5px}.weui-grid__label{display:block;color:#000;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.weui-footer,.weui-grid__label{text-align:center;font-size:14px}.weui-footer{color:#999}.weui-footer a{color:#586c94}.weui-footer_fixed-bottom{position:fixed;bottom:.52em;left:0;right:0}.weui-footer__links{font-size:0}.weui-footer__link{display:inline-block;vertical-align:top;margin:0 .62em;position:relative;font-size:14px}.weui-footer__link:before{content:" ";position:absolute;left:0;top:0;width:1px;bottom:0;border-left:1px solid #c7c7c7;color:#c7c7c7;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleX(.5);transform:scaleX(.5);left:-.65em;top:.36em;bottom:.36em}.weui-footer__link:first-child:before{display:none}.weui-footer__text{padding:0 .34em;font-size:12px}.weui-flex{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.weui-flex__item{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.weui-dialog{position:fixed;z-index:5000;width:80%;max-width:300px;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);background-color:#fff;text-align:center;border-radius:3px;overflow:hidden}.weui-dialog__hd{padding:1.3em 1.6em .5em}.weui-dialog__title{font-weight:400;font-size:18px}.weui-dialog__bd{padding:0 1.6em .8em;min-height:40px;font-size:15px;line-height:1.3;word-wrap:break-word;word-break:break-all;color:#999}.weui-dialog__bd:first-child{padding:2.7em 20px 1.7em;color:#353535}.weui-dialog__ft{position:relative;line-height:48px;font-size:18px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.weui-dialog__ft:after{content:" ";position:absolute;left:0;top:0;right:0;height:1px;border-top:1px solid #d5d5d6;color:#d5d5d6;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-dialog__btn{display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;color:#3cc51f;text-decoration:none;-webkit-tap-highlight-color:rgba(0,0,0,0);position:relative}.weui-dialog__btn:active{background-color:#eee}.weui-dialog__btn:after{content:" ";position:absolute;left:0;top:0;width:1px;bottom:0;border-left:1px solid #d5d5d6;color:#d5d5d6;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleX(.5);transform:scaleX(.5)}.weui-dialog__btn:first-child:after{display:none}.weui-dialog__btn_default{color:#353535}.weui-dialog__btn_primary{color:#0bb20c}.weui-skin_android .weui-dialog{text-align:left;-webkit-box-shadow:0 6px 30px 0 rgba(0,0,0,.1);box-shadow:0 6px 30px 0 rgba(0,0,0,.1)}.weui-skin_android .weui-dialog__title{font-size:21px}.weui-skin_android .weui-dialog__hd{text-align:left}.weui-skin_android .weui-dialog__bd{color:#999;padding:.25em 1.6em 2em;font-size:17px;text-align:left}.weui-skin_android .weui-dialog__bd:first-child{padding:1.6em 1.6em 2em;color:#353535}.weui-skin_android .weui-dialog__ft{display:block;text-align:right;line-height:42px;font-size:16px;padding:0 1.6em .7em}.weui-skin_android .weui-dialog__ft:after{display:none}.weui-skin_android .weui-dialog__btn{display:inline-block;vertical-align:top;padding:0 .8em}.weui-skin_android .weui-dialog__btn:after{display:none}.weui-skin_android .weui-dialog__btn:active,.weui-skin_android .weui-dialog__btn:visited{background-color:rgba(0,0,0,.06)}.weui-skin_android .weui-dialog__btn:last-child{margin-right:-.8em}.weui-skin_android .weui-dialog__btn_default{color:gray}@media screen and (min-width:1024px){.weui-dialog{width:35%}}.weui-toast{position:fixed;z-index:5000;width:7.6em;min-height:7.6em;top:180px;left:50%;margin-left:-3.8em;background:hsla(0,0%,7%,.7);text-align:center;border-radius:5px;color:#fff}.weui-icon_toast{margin:22px 0 0;display:block}.weui-icon_toast.weui-icon-success-no-circle:before{color:#fff;font-size:55px}.weui-icon_toast.weui-loading{margin:30px 0 0;width:38px;height:38px;vertical-align:baseline}.weui-toast__content{margin:0 0 15px}.weui-mask{background:rgba(0,0,0,.6)}.weui-mask,.weui-mask_transparent{position:fixed;z-index:1000;top:0;right:0;left:0;bottom:0}.weui-actionsheet{position:fixed;left:0;bottom:0;-webkit-transform:translateY(100%);transform:translateY(100%);-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:5000;width:100%;background-color:#efeff4;-webkit-transition:-webkit-transform .3s;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s, -webkit-transform .3s;transition:transform .3s,-webkit-transform .3s}.weui-actionsheet__title{position:relative;height:65px;padding:0 20px;line-height:1.4;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;text-align:center;font-size:14px;color:#888;background:#fcfcfd}.weui-actionsheet__title:before{content:" ";position:absolute;left:0;bottom:0;right:0;height:1px;border-bottom:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-actionsheet__title .weui-actionsheet__title-text{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.weui-actionsheet__menu{background-color:#fcfcfd}.weui-actionsheet__action{margin-top:6px;background-color:#fcfcfd}.weui-actionsheet__cell{position:relative;padding:10px 0;text-align:center;font-size:18px}.weui-actionsheet__cell:before{content:" ";position:absolute;left:0;top:0;right:0;height:1px;border-top:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-actionsheet__cell:active{background-color:#ececec}.weui-actionsheet__cell:first-child:before{display:none}.weui-skin_android .weui-actionsheet{position:fixed;left:50%;top:50%;bottom:auto;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);width:274px;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-backface-visibility:hidden;backface-visibility:hidden;background:transparent;-webkit-transition:-webkit-transform .3s;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s, -webkit-transform .3s;transition:transform .3s,-webkit-transform .3s}.weui-skin_android .weui-actionsheet__action{display:none}.weui-skin_android .weui-actionsheet__menu{border-radius:2px;-webkit-box-shadow:0 6px 30px 0 rgba(0,0,0,.1);box-shadow:0 6px 30px 0 rgba(0,0,0,.1)}.weui-skin_android .weui-actionsheet__cell{padding:13px 24px;font-size:16px;line-height:1.4;text-align:left}.weui-skin_android .weui-actionsheet__cell:first-child{border-top-left-radius:2px;border-top-right-radius:2px}.weui-skin_android .weui-actionsheet__cell:last-child{border-bottom-left-radius:2px;border-bottom-right-radius:2px}.weui-actionsheet_toggle{-webkit-transform:translate(0);transform:translate(0)}.weui-loadmore{width:65%;margin:1.5em auto;line-height:1.6em;font-size:14px;text-align:center}.weui-loadmore__tips{display:inline-block;vertical-align:middle}.weui-loadmore_line{border-top:1px solid #e5e5e5;margin-top:2.4em}.weui-loadmore_line .weui-loadmore__tips{position:relative;top:-.9em;padding:0 .55em;background-color:#fff;color:#999}.weui-loadmore_dot .weui-loadmore__tips{padding:0 .16em}.weui-loadmore_dot .weui-loadmore__tips:before{content:" ";width:4px;height:4px;border-radius:50%;background-color:#e5e5e5;display:inline-block;position:relative;vertical-align:0;top:-.16em}.weui-badge{display:inline-block;padding:.15em .4em;min-width:8px;border-radius:18px;background-color:#f43530;color:#fff;line-height:1.2;text-align:center;font-size:12px;vertical-align:middle}.weui-badge_dot{padding:.4em;min-width:0}.weui-search-bar{position:relative;padding:8px 10px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;background-color:#efeff4;-webkit-text-size-adjust:100%;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.weui-search-bar:before{top:0;border-top:1px solid #d7d6dc;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-search-bar:after,.weui-search-bar:before{content:" ";position:absolute;left:0;right:0;height:1px;color:#d7d6dc}.weui-search-bar:after{bottom:0;border-bottom:1px solid #d7d6dc;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-search-bar.weui-search-bar_focusing .weui-search-bar__cancel-btn{display:block}.weui-search-bar.weui-search-bar_focusing .weui-search-bar__label{display:none}.weui-search-bar__form{position:relative;-webkit-box-flex:1;-webkit-flex:auto;-ms-flex:auto;flex:auto;background-color:#efeff4}.weui-search-bar__form:after{content:'';position:absolute;left:0;top:0;width:200%;height:200%;-webkit-transform:scale(.5);transform:scale(.5);-webkit-transform-origin:0 0;transform-origin:0 0;border-radius:10px;border:1px solid #e6e6ea;-webkit-box-sizing:border-box;box-sizing:border-box;background:#fff}.weui-search-bar__box{position:relative;padding-left:30px;padding-right:30px;height:100%;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box;z-index:1}.weui-search-bar__box .weui-search-bar__input{padding:4px 0;width:100%;height:1.42857143em;border:0;font-size:14px;line-height:1.42857143em;-webkit-box-sizing:content-box;box-sizing:content-box;background:transparent}.weui-search-bar__box .weui-search-bar__input:focus{outline:none}.weui-search-bar__box .weui-icon-search{position:absolute;top:50%;left:10px;margin-top:-14px;line-height:28px}.weui-search-bar__box .weui-icon-clear{position:absolute;top:50%;right:0;margin-top:-14px;padding:0 10px;line-height:28px}.weui-search-bar__label{position:absolute;top:1px;right:1px;bottom:1px;left:1px;z-index:2;border-radius:3px;text-align:center;color:#9b9b9b;background:#fff}.weui-search-bar__label span{display:inline-block;font-size:14px;vertical-align:middle}.weui-search-bar__label .weui-icon-search{margin-right:5px}.weui-search-bar__cancel-btn{display:none;margin-left:10px;line-height:28px;color:#09bb07;white-space:nowrap}.weui-search-bar__input:not(:valid)~.weui-icon-clear{display:none}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration,input[type=search]::-webkit-search-results-button,input[type=search]::-webkit-search-results-decoration{display:none}.weui-picker{position:fixed;width:100%;left:0;bottom:0;z-index:5000;-webkit-backface-visibility:hidden;backface-visibility:hidden;-webkit-transform:translateY(100%);transform:translateY(100%);-webkit-transition:-webkit-transform .3s;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s, -webkit-transform .3s;transition:transform .3s,-webkit-transform .3s}.weui-picker__hd{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;padding:9px 15px;background-color:#fff;position:relative;text-align:center;font-size:17px}.weui-picker__hd:after{content:" ";position:absolute;left:0;bottom:0;right:0;height:1px;border-bottom:1px solid #e5e5e5;color:#e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-picker__action{display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;color:#1aad19}.weui-picker__action:first-child{text-align:left;color:#888}.weui-picker__action:last-child{text-align:right}.weui-picker__bd{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;position:relative;background-color:#fff;height:238px;overflow:hidden}.weui-picker__group{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;position:relative;height:100%}.weui-picker__mask{top:0;height:100%;margin:0 auto;background:-webkit-linear-gradient(top,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6)),-webkit-linear-gradient(bottom,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6));background:-webkit-gradient(linear,left top, left bottom,from(hsla(0,0%,100%,.95)),to(hsla(0,0%,100%,.6))),-webkit-gradient(linear,left bottom, left top,from(hsla(0,0%,100%,.95)),to(hsla(0,0%,100%,.6)));background:linear-gradient(180deg,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6)),linear-gradient(0deg,hsla(0,0%,100%,.95),hsla(0,0%,100%,.6));background-position:top,bottom;background-size:100% 102px;background-repeat:no-repeat;-webkit-transform:translateZ(0);transform:translateZ(0)}.weui-picker__indicator,.weui-picker__mask{position:absolute;left:0;width:100%;z-index:3}.weui-picker__indicator{height:34px;top:102px}.weui-picker__indicator:before{top:0;border-top:1px solid #e5e5e5;-webkit-transform-origin:0 0;transform-origin:0 0;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-picker__indicator:after,.weui-picker__indicator:before{content:" ";position:absolute;left:0;right:0;height:1px;color:#e5e5e5}.weui-picker__indicator:after{bottom:0;border-bottom:1px solid #e5e5e5;-webkit-transform-origin:0 100%;transform-origin:0 100%;-webkit-transform:scaleY(.5);transform:scaleY(.5)}.weui-picker__content{position:absolute;top:0;left:0;width:100%}.weui-picker__item{padding:0;height:34px;line-height:34px;text-align:center;color:#000;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.weui-picker__item_disabled{color:#999}@-webkit-keyframes a{0%{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}@keyframes a{0%{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}to{-webkit-transform:translateZ(0);transform:translateZ(0)}}.weui-animate-slide-up{-webkit-animation:a ease .3s forwards;animation:a ease .3s forwards}@-webkit-keyframes b{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}@keyframes b{0%{-webkit-transform:translateZ(0);transform:translateZ(0)}to{-webkit-transform:translate3d(0,100%,0);transform:translate3d(0,100%,0)}}.weui-animate-slide-down{-webkit-animation:b ease .3s forwards;animation:b ease .3s forwards}@-webkit-keyframes c{0%{opacity:0}to{opacity:1}}@keyframes c{0%{opacity:0}to{opacity:1}}.weui-animate-fade-in{-webkit-animation:c ease .3s forwards;animation:c ease .3s forwards}@-webkit-keyframes d{0%{opacity:1}to{opacity:0}}@keyframes d{0%{opacity:1}to{opacity:0}}.weui-animate-fade-out{-webkit-animation:d ease .3s forwards;animation:d ease .3s forwards}.weui-agree{display:block;padding:.5em 15px;font-size:13px}.weui-agree a{color:#586c94}.weui-agree__text{color:#999}.weui-agree__checkbox{-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:0;font-size:0;border:1px solid #d1d1d1;background-color:#fff;border-radius:3px;width:13px;height:13px;position:relative;vertical-align:0;top:2px}.weui-agree__checkbox:checked:before{font-family:weui;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;text-align:center;speak:none;display:inline-block;vertical-align:middle;text-decoration:inherit;content:"\EA08";color:#09bb07;font-size:13px;position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-48%) scale(.73);transform:translate(-50%,-48%) scale(.73)}.weui-agree__checkbox:disabled{background-color:#e1e1e1}.weui-agree__checkbox:disabled:before{color:#adadad}.weui-loading{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:e 1s steps(12) infinite;animation:e 1s steps(12) infinite;background:transparent url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E9E9E9' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23989697' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%239B999A' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23A3A1A2' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23ABA9AA' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23B2B2B2' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23BAB8B9' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23C2C0C1' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23CBCBCB' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23D2D2D2' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23DADADA' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E2E2E2' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E") no-repeat;background-size:100%}.weui-btn_loading.weui-btn_primary .weui-loading,.weui-btn_loading.weui-btn_warn .weui-loading,.weui-loading.weui-loading_transparent{background-image:url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect xmlns='http://www.w3.org/2000/svg' width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.56)' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.5)' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.43)' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.38)' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.32)' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.28)' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.25)' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.2)' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.17)' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.14)' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.1)' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.03)' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E")}@-webkit-keyframes e{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes e{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}.weui-slider{padding:15px 18px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.weui-slider__inner{position:relative;height:2px;background-color:#e9e9e9}.weui-slider__track{height:2px;background-color:#1aad19;width:0}.weui-slider__handler{position:absolute;left:0;top:50%;width:28px;height:28px;margin-left:-14px;margin-top:-14px;border-radius:50%;background-color:#fff;-webkit-box-shadow:0 0 4px rgba(0,0,0,.2);box-shadow:0 0 4px rgba(0,0,0,.2)}.weui-slider-box{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.weui-slider-box .weui-slider{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.weui-slider-box__value{margin-left:.5em;min-width:24px;color:#888;text-align:center;font-size:14px}</style>
  
  <meta content="width=device-width,initial-scale=1,user-scalable=no" name="viewport">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-touch-fullscreen" content="yes">
  <meta name="format-detection" content="telephone=no,address=no">
  <meta name="apple-mobile-web-app-status-bar-style" content="white">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="keywords" content="MBTI，测试性格，免费的性格测试，职业推荐，工作规划，职业发展，恋爱，三观，人格，内向，外向，INFP，INTJ，INTP，ISTJ，INFJ，ENTP，ENFP，ISFJ，ISFP，ENTJ，ISTP，ESFP，ESTJ，ESTP，ESFJ，ENFJ">
  <meta name="description" content="超过200万用户选择的职业性格及人才测评平台，提供免费、权威、准确的MBTI十六型人格性格测试及海量相关内容，帮助您找到最合适的职业和人生伴侣。">
<!--  <title>简则MBTI</title>-->
  <link href="/Public/mbti_result_files/app.357cc648a21e648a3c21.css" rel="stylesheet">



  <link rel="stylesheet" type="text/css" href="/Public/mbti_result_files/5.925fb17f39531e5325e3.css?ver=<?php echo time(); ?>">

  <link rel="stylesheet" type="text/css" href="/Public/mbti_result_files/22.86b085c712146ae7c750.css">
  <script src="/Public/layer/jquery.min.js"></script>
  <script src="/Public/mbti_result_files/fixtab.js"></script>
  <style>body, html {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none
  }

  taro-view-core {
    display: block
  }</style>
  <style>taro-button-core {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 14px;
    padding-right: 14px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 18px;
    text-align: center;
    text-decoration: none;
    color: #000000;
    background-color: #F8F8F8;
    line-height: 2.55555556;
    border-radius: 5px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    overflow: hidden;
    width: 100%;
    border-width: 0;
    outline: 0;
    -webkit-appearance: none
  }

  taro-button-core:not([disabled]):active {
    color: rgba(0, 0, 0, 0.6);
    background-color: #DEDEDE
  }

  taro-button-core:focus {
    outline: 0
  }

  taro-button-core:after {
    content: " ";
    width: 200%;
    height: 200%;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(0, 0, 0, 0.2);
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 10px
  }

  taro-button-core + taro-button-core {
    margin-top: 15px
  }

  taro-button-core[type=default] {
    color: #000000;
    background-color: #F8F8F8
  }

  taro-button-core[type=default]:not([disabled]):visited {
    color: #000000
  }

  taro-button-core[type=default]:not([disabled]):active {
    color: rgba(0, 0, 0, 0.6);
    background-color: #DEDEDE
  }

  taro-button-core[size=mini] {
    width: auto;
    display: inline-block;
    padding: 0 1.32em;
    line-height: 2.3;
    font-size: 13px
  }

  taro-button-core[plain], taro-button-core[plain][type=default], taro-button-core[plain][type=primary] {
    border-width: 1px;
    background-color: transparent
  }

  taro-button-core[disabled] {
    color: rgba(255, 255, 255, 0.6)
  }

  taro-button-core[disabled][type=default] {
    color: rgba(0, 0, 0, 0.3);
    background-color: #F7F7F7
  }

  taro-button-core[disabled][type=primary] {
    background-color: #9ED99D
  }

  taro-button-core[disabled][type=warn] {
    background-color: #EC8B89
  }

  taro-button-core[loading] .weui-loading {
    margin: -0.2em 0.34em 0 0
  }

  taro-button-core[loading][type=primary], taro-button-core[loading][type=warn] {
    color: rgba(255, 255, 255, 0.6)
  }

  taro-button-core[loading][type=primary] {
    background-color: #179B16
  }

  taro-button-core[loading][type=warn] {
    background-color: #CE3C39
  }

  taro-button-core[plain][type=primary] {
    color: #1aad19;
    border: 1px solid #1aad19
  }

  taro-button-core[plain][type=primary]:not([disabled]):active {
    color: rgba(26, 173, 25, 0.6);
    border-color: rgba(26, 173, 25, 0.6);
    background-color: transparent
  }

  taro-button-core[plain][type=primary]:after {
    border-width: 0
  }

  taro-button-core[plain], taro-button-core[plain][type=default] {
    color: #353535;
    border: 1px solid #353535
  }

  taro-button-core[plain]:not([disabled]):active, taro-button-core[plain][type=default]:not([disabled]):active {
    color: rgba(53, 53, 53, 0.6);
    border-color: rgba(53, 53, 53, 0.6);
    background-color: transparent
  }

  taro-button-core[plain]:after, taro-button-core[plain][type=default]:after {
    border-width: 0
  }

  taro-button-core[type=primary] {
    color: #fff;
    background-color: #1AAD19
  }

  taro-button-core[type=primary]:not([disabled]):visited {
    color: #FFFFFF
  }

  taro-button-core[type=primary]:not([disabled]):active {
    color: rgba(255, 255, 255, 0.6);
    background-color: #179B16
  }

  taro-button-core[type=warn] {
    color: #fff;
    background-color: #E64340
  }

  taro-button-core[type=warn]:not([disabled]):visited {
    color: #FFFFFF
  }

  taro-button-core[type=warn]:not([disabled]):active {
    color: rgba(255, 255, 255, 0.6);
    background-color: #CE3C39
  }

  taro-button-core[plain][disabled] {
    color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.2);
    background-color: #F7F7F7
  }

  taro-button-core[plain][disabled][type=primary] {
    color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.2);
    background-color: #F7F7F7
  }</style>
  <style>taro-text-core {
    display: inline;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: pre-wrap
  }

  .taro-text__selectable {
    -moz-user-select: text;
    -webkit-user-select: text;
    -ms-user-select: text;
    user-select: text
  }</style>
  <style>img[src=""] {
    opacity: 0
  }

  taro-image-core {
    display: inline-block;
    overflow: hidden;
    position: relative;
    font-size: 0;
    width: 320px;
    height: 240px
  }

  .taro-img.taro-img__widthfix {
    height: 100%
  }

  .taro-img__mode-scaletofill {
    width: 100%;
    height: 100%
  }

  .taro-img__mode-aspectfit {
    max-width: 100%;
    max-height: 100%
  }

  .taro-img__mode-aspectfill {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
  }

  .taro-img__mode-aspectfill--width {
    min-width: 100%;
    height: 100%
  }

  .taro-img__mode-aspectfill--height {
    width: 100%;
    min-height: 100%
  }

  .taro-img__mode-widthfix {
    width: 100%
  }

  .taro-img__mode-top {
    width: 100%
  }

  .taro-img__mode-bottom {
    width: 100%;
    position: absolute;
    bottom: 0
  }

  .taro-img__mode-left {
    height: 100%
  }

  .taro-img__mode-right {
    position: absolute;
    height: 100%;
    right: 0
  }

  .taro-img__mode-topright {
    position: absolute;
    right: 0
  }

  .taro-img__mode-bottomleft {
    position: absolute;
    bottom: 0
  }

  .taro-img__mode-bottomright {
    position: absolute;
    right: 0;
    bottom: 0
  }</style>
  <link href="/Public/mbti_result_files/app.357cc648a21e648a3c21.css" rel="stylesheet">
  
  <style>hcfy-result.__hcfy__result__loaded__.__hcfy__result__both__ {
    border: 1px dotted
  }</style>
  <title>我的报告</title>
  
  
  
  
  <style type="text/css">
  @keyframes caretBlink {
    from { opacity: 1.0; }
    to { opacity: 0.0; }
}

@keyframes rotateSpinner {
    from {
        transform:rotate(0deg);
    }
    to {
        transform:rotate(360deg);
    }
}

#text-tool-caret {
    animation-name: caretBlink;
    animation-iteration-count: infinite;
    animation-timing-function: cubic-bezier(1.0,0,0,1.0);
    animation-duration: 1s;
}

#en-markup-loading-spinner {
    position: absolute;
    top: calc(50% - 16px);
    left: calc(50% - 16px);
    width: 32px;
    height: 32px;
}

#en-markup-loading-spinner img {
    position: relative;
    top: 0px;
    left: 0px;
    animation-name: rotateSpinner;
    animation-duration: 0.6s;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}
</style><style type="text/css">
.skitchToastBoxContainer {
    position: absolute;
    width: 100%;
    text-align: center;
    top: 30px;
    -webkit-user-select: none;
    -moz-user-select: none;
    pointer-events: none;
}

.skitchToastBox {
    width: 200px;
    height: 16px;
    padding: 12px;
    background-color: rgba(47, 55, 61, 0.95);
    border-radius: 4px;
    color: white;
    cursor: default;
    font-size: 10pt;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.32);
    font-family: 'Soleil', Helvetica, Arial, sans-serif;
    border: 2px rgba(255, 255, 255, 0.38) solid;
}

.lang-zh-cn .skitchToastBox {
    font-family: '微软雅黑', 'Microsoft YaHei', SimSun,
        '&#x30E1;&#x30A4;&#x30EA;&#x30AA;', Meiryo, 'MS PGothic', 'Soleil',
        Helvetica, Arial, sans-serif;
}

.lang-ja-jp .skitchToastBox {
    font-family: '&#x30E1;&#x30A4;&#x30EA;&#x30AA;', Meiryo, 'MS PGothic',
        '微软雅黑', 'Microsoft YaHei', SimSun, 'Soleil', Helvetica, Arial,
        sans-serif;
}

.skitchToast {
    padding-left: 20px;
    padding-right: 20px;
    display: inline-block;
    height: 10px;
    color: #f1f5f8;
    text-align: center;
}

.skitchVisible {
    /* Don't remove this class it's a hack used by the Evernote Clipper */
}
</style><style type="text/css">

@font-face {
	font-family: 'Soleil';
	src: url(data:application/font-woff2;base64,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);
	font-weight: normal;
	font-style: normal;
}
</style><style type="text/css">

#en-markup-disabled {
    position: fixed;
    z-index: 9999;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
    cursor: default;
    -webkit-user-select: none;
}

#en-markup-alert-container {
    position: absolute;
    z-index: 9999;
    width: 450px;
    left: calc(50% - 225px);
    top: calc(50% - 85px);
    background-color: white;
    box-shadow: 0 2px 7px 1px rgba(0,0,0,0.35);
    -webkit-user-select: none;
}

#en-markup-alert-container .cell-1 {
    position: relative;
    height: 110px;
    width: 105px;
    float: left;
    text-align: center;
    background-image: url(data:image/png;base64,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);
    background-position: 65% 50%;
    background-repeat: no-repeat;
}

#en-markup-alert-container .cell-2 {
    position: relative;
    float: left;
    width: 345px;
    margin-top: 29px;
    margin-bottom: 20px;
}

#en-markup-alert-container .cell-2 .cell-2-title {
    margin-bottom: 5px;
    padding-right: 30px;
    font-size: 12pt;
    font-family: Tahoma, Arial;
}

#en-markup-alert-container .cell-2 .cell-2-message {
    padding-right: 30px;
    font-size: 9.5pt;
    font-family: Tahoma, Arial;
}

#en-markup-alert-container .cell-3 {
    position: relative;
    width: 450px;
    height: 60px;
    float: left;
    background-color: rgb(240,240,240);
}

#en-markup-alert-container .cell-3 button {
    position: absolute;
    top: 12px;
    right: 15px;
    width: 110px;
    height: 36px;
}

#en-markup-alert-container .cell-3 button.alt-button {
    position: absolute;
    top: 12px;
    right: 140px;
    width: 110px;
    height: 36px;
}
</style></head>
<body>

<div class="taro-tabbar__container" id="container">
  <div class="taro-tabbar__panel">
    <div id="app" class="taro_router">
      <div>
        <div class="taro_page">
          <taro-view-core style="background-color: rgb(241, 240, 246);" class="hydrated">
            <taro-view-core class="at-modal hydrated">
              <taro-view-core class="at-modal__overlay hydrated"></taro-view-core>
              <taro-view-core class="at-modal__container hydrated">
                <taro-scroll-view-core scrolly="true" class="at-modal__content taro-scroll-view__scroll-y hydrated">
                  <taro-view-core class="main hydrated">
                    <taro-view-core class="invite-label hydrated"></taro-view-core>
                    <taro-view-core class="invite-label hydrated">想知道我的MBTI性格类型么？成为我的好友吧！</taro-view-core>
                  </taro-view-core>
                </taro-scroll-view-core>
                <taro-view-core class="at-modal__footer hydrated">
                  <taro-view-core class="at-modal__action hydrated">
                    <taro-button-core class="ac-button hydrated" type="">取消</taro-button-core>
                    <taro-button-core class="ac-button hydrated" type="">接受邀请，去看看TA</taro-button-core>
                  </taro-view-core>
                </taro-view-core>
              </taro-view-core>
            </taro-view-core>
            <taro-view-core class="hydrated">
              <taro-scroll-view-core scrolly="true" scrollwithanimation="true" style="height: 100vh;"
                                     class="taro-scroll-view__scroll-y hydrated">
                <taro-view-core class="hydrated">
                  <taro-view-core class="mobile-header hydrated">
                    <taro-view-core class="reback hydrated"></taro-view-core>
                    <taro-view-core class="mobile-header-title hydrated">历史报告</taro-view-core>
                  </taro-view-core>
                </taro-view-core>
                <taro-view-core class="contentMain hydrated">
                  <taro-view-core class="hydrated">
                    <taro-scroll-view-core class="hydrated">
                      <taro-view-core class="inter_nav inter_nva_fixed hydrated">
                        <taro-view-core class="inter_base active_nav hydrated">
                          <taro-view-core
                                  class="iconfont icon-wodebaogao inter_base_img inter_base_img1 hydrated"></taro-view-core>
                          <taro-view-core class="inter_base_title1 hydrated">基本画像</taro-view-core>
                        </taro-view-core>
                        <taro-view-core class="inter_base hydrated">
                          <taro-view-core
                                  class="iconfont icon-zhexiantu-xianxing inter_base_img inter_base_img2 hydrated"></taro-view-core>
                          <taro-view-core class="inter_base_title2 hydrated">个人成长</taro-view-core>
                        </taro-view-core>
                        <taro-view-core class="inter_base hydrated">
                          <taro-view-core
                                  class="iconfont icon-zhiye inter_base_img inter_base_img3 hydrated"></taro-view-core>
                          <taro-view-core class="inter_base_title3 hydrated">职场小结</taro-view-core>
                        </taro-view-core>
                        <taro-view-core class="inter_base hydrated">
                          <taro-view-core
                                  class="iconfont icon-guanxindeTA inter_base_img inter_base_img4 hydrated"></taro-view-core>
                          <taro-view-core class="inter_base_title4 hydrated">恋爱锦囊</taro-view-core>
                        </taro-view-core>
                      </taro-view-core>
                      <taro-scroll-view-core class="main scroll_inter_h5 taro-scroll-view__scroll-y hydrated"
                                             scrolly="true" scrollwithanimation="true">
                        <taro-view-core class="history-inter hydrated"
                                        style="background: linear-gradient(0deg, rgb(255, 233, 233) 9%, rgb(225, 186, 185));">
                          <taro-view-core class="history-inter-content hydrated">
                            <taro-view-core class="history-inter-left hydrated" style="color: rgb(160, 54, 63);">
                              <taro-view-core class="history-inter-type hydrated">ISTP</taro-view-core>
                              <taro-view-core class="history-inter-info hydrated">手艺人，专家气质</taro-view-core>
                              <taro-view-core class="history-inter-info hydrated">最擅长机械和实操相关技能的类型</taro-view-core>
                              <taro-view-core class="history-inter-time hydrated">测试的时间：<?php echo $row['add_time']; ?>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-image-core class="history-inter-right hydrated"><img
                                    class="taro-img__mode-scaletofill" src="/Public/mbti_result_files/ISTP-1.png">
                            </taro-image-core>
                          </taro-view-core>
                        </taro-view-core>
                        <taro-view-core class="main article-content hydrated">
                          <taro-view-core class="content-area color_base person_bar_h5 hydrated">
                            <taro-view-core class="bar_left hydrated">
                              <taro-view-core class="bar_item1 hydrated">
                                <taro-view-core class="bar_item_0 hydrated">外向(E)</taro-view-core>
                                <taro-view-core class="bar_item_1 hydrated">
                                  <taro-view-core class="bar_img1 hydrated"></taro-view-core>
                                  <taro-view-core class="bar_img2 hydrated"
                                                  style="right:0rem !important;width:4.3rem !important; left:auto !important;"></taro-view-core>
                                  <taro-view-core class="bar_img3 hydrated"></taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="bar_item_3 hydrated">内向(I)</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="bar_item2 hydrated">
                                <taro-view-core class="bar_item_0 hydrated">实感(S)</taro-view-core>
                                <taro-view-core class="bar_item_1 hydrated">
                                  <taro-view-core class="bar_img1 hydrated"></taro-view-core>
                                  <taro-view-core class="bar_img2 hydrated"
                                                  style="left:1.7200000000000002rem !important;width:2.5799999999999996rem !important"></taro-view-core>
                                  <taro-view-core class="bar_img3 hydrated"></taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="bar_item_3 hydrated">直觉(N)</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="bar_item3 hydrated">
                                <taro-view-core class="bar_item_0 hydrated">理性(T)</taro-view-core>
                                <taro-view-core class="bar_item_1 hydrated">
                                  <taro-view-core class="bar_img1 hydrated"></taro-view-core>
                                  <taro-view-core class="bar_img2 hydrated"
                                                  style="left:0rem !important;width:4.3rem !important"></taro-view-core>
                                  <taro-view-core class="bar_img3 hydrated"></taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="bar_item_3 hydrated">感性(F)</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="bar_item4 hydrated">
                                <taro-view-core class="bar_item_0 hydrated">判断(J)</taro-view-core>
                                <taro-view-core class="bar_item_1 hydrated">
                                  <taro-view-core class="bar_img1 hydrated"></taro-view-core>
                                  <taro-view-core class="bar_img2 hydrated"
                                                  style="right:0.8599999999999999rem !important;width:3.44rem !important; left:auto !important;"></taro-view-core>
                                  <taro-view-core class="bar_img3 hydrated"></taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="bar_item_3 hydrated">知觉(P)</taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                          </taro-view-core>
                          <taro-view-core class="explainResultBox hydrated">
                            <taro-view-core class="explainResult hydrated">
                              <taro-text-core class="iconfont icon-question hydrated"></taro-text-core>
                              <taro-text-core class="explainLabel hydrated">解释MBTI测试的结果 &gt;&gt;&gt;</taro-text-core>
                            </taro-view-core>
                          </taro-view-core>
                          <taro-image-core class="section-img-header-second hydrated"><img
                                  class="taro-img__mode-aspectfit" src="/Public/mbti_result_files/section-header2.png">
                          </taro-image-core>
                          <taro-view-core class="header-tag hydrated">最擅长机械和实操相关技能的类型</taro-view-core>
                          <taro-view-core class="article-paragraph pre-wrap hydrated"><taro-view-core class="pre-wrap hydrated">
你是善于观察的工匠，理解机械，对故障排除有一定兴趣，采用灵活的逻辑来处理环境，寻找解决手边问题的实际方案。你具有独立性和适应性，通常以自我指导、自发的方式与周围的世界进行互动。

你注重细节，并对周围世界的需求作出反应。由于你对环境的敏锐感觉，你善于快速行动和对紧急情况作出反应。你是冷静的，但不退缩：你喜欢采取行动，并凭借自身身体和感官的敏锐感接触世界。
                            </taro-view-core>
                            <taro-image-core class="header-tag-bg hydrated"><img class="taro-img__mode-aspectfit" src="/Public/mbti_result_files/header-tag-bg.png">
                            </taro-image-core>
                          </taro-view-core>
                          <taro-view-core class="content-area color_base hydrated">
                            <taro-image-core class="img-tri-shape hydrated"><img class="taro-img__mode-aspectfit" src="/Public/mbti_result_files/tri-shape.png">
                            </taro-image-core>
                            <taro-view-core class="mbti-words hydrated">ISTP</taro-view-core>
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">这四个字母代表什么</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="rec-content hydrated">
                              <taro-view-core class="line-content hydrated">
                                <taro-view-core class="code hydrated">I</taro-view-core>
                                <taro-image-core class="img-code-name-bg hydrated"><img
                                        class="taro-img__mode-scaletofill"
                                        src="/Public/mbti_result_files/code-name-bg.png"></taro-image-core>
                                <taro-view-core class="name hydrated">内向</taro-view-core>
                                <taro-view-core class="explain hydrated">在独处时精力充沛</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="line-content hydrated">
                                <taro-view-core class="code hydrated">S</taro-view-core>
                                <taro-image-core class="img-code-name-bg hydrated"><img
                                        class="taro-img__mode-scaletofill"
                                        src="/Public/mbti_result_files/code-name-bg.png"></taro-image-core>
                                <taro-view-core class="name hydrated">实感</taro-view-core>
                                <taro-view-core class="explain hydrated">专注于事实和细节，而不是想法和概念</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="line-content hydrated">
                                <taro-view-core class="code hydrated">T</taro-view-core>
                                <taro-image-core class="img-code-name-bg hydrated"><img
                                        class="taro-img__mode-scaletofill"
                                        src="/Public/mbti_result_files/code-name-bg.png"></taro-image-core>
                                <taro-view-core class="name hydrated">理性</taro-view-core>
                                <taro-view-core class="explain hydrated">基于逻辑和理性做出决定</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="line-content hydrated">
                                <taro-view-core class="code hydrated">P</taro-view-core>
                                <taro-image-core class="img-code-name-bg hydrated"><img
                                        class="taro-img__mode-scaletofill"
                                        src="/Public/mbti_result_files/code-name-bg.png"></taro-image-core>
                                <taro-view-core class="name hydrated">知觉</taro-view-core>
                                <taro-view-core class="explain hydrated">更喜欢自发和灵活，而不是计划和组织</taro-view-core>
                              </taro-view-core>
                              <taro-image-core class="img-tri-shape-bottom hydrated"><img
                                      class="taro-img__mode-scaletofill"
                                      src="/Public/mbti_result_files/tri-shape-bottom.png"></taro-image-core>
                            </taro-view-core>
                          </taro-view-core>
                          <taro-view-core class="content-area color_base hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">THE PROPORTION OF THE POPULATION
                              </taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">人口比例</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="population-title hydrated">
                              ISTP人格类型在男性中比女性更常见，在男性中，它是第三种最常见的类型。在女性中，它是第四罕见的
                            </taro-view-core>
                            <taro-view-core class="score hydrated">
                              <taro-view-core class="label hydrated">占总人口</taro-view-core>
                              <taro-view-core class="data hydrated">5%</taro-view-core>
                              <taro-view-core class="score-circle hydrated">
                                <taro-view-core class="left hydrated"></taro-view-core>
                                <taro-view-core class="right hydrated"
                                                style="transform: rotate(22.5deg);"></taro-view-core>
                                <taro-image-core class="icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                            src="/Public/mbti_result_files/population-icon.png">
                                </taro-image-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="score hydrated">
                              <taro-view-core class="label hydrated">占男性</taro-view-core>
                              <taro-view-core class="data hydrated">9%</taro-view-core>
                              <taro-view-core class="score-circle hydrated" style="border-color: rgb(77, 95, 247);">
                                <taro-view-core class="left hydrated"></taro-view-core>
                                <taro-view-core class="right hydrated"
                                                style="transform: rotate(40.5deg);"></taro-view-core>
                                <taro-image-core class="icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                            src="/Public/mbti_result_files/real-male-icon.png">
                                </taro-image-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="score hydrated">
                              <taro-view-core class="label hydrated">占女性</taro-view-core>
                              <taro-view-core class="data hydrated">2%</taro-view-core>
                              <taro-view-core class="score-circle hydrated" style="border-color: rgb(234, 143, 107);">
                                <taro-view-core class="left hydrated"></taro-view-core>
                                <taro-view-core class="right hydrated"
                                                style="transform: rotate(9deg);"></taro-view-core>
                                <taro-image-core class="icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                            src="/Public/mbti_result_files/real-female-icon.png">
                                </taro-image-core>
                              </taro-view-core>
                            </taro-view-core>
                          </taro-view-core>
                          <taro-view-core class="content-area color_base hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">FAMOUS CELEBRITIES</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">名家名人</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-image-core class="img-famous-one hydrated"><img
                                    class="taro-img__mode-aspectfill taro-img__mode-aspectfill--height"
                                    src="/Public/mbti_result_files/ISTP_1.jpeg"></taro-image-core>
                            <taro-view-core class="rec-famous-one hydrated">兰斯阿姆斯特朗</taro-view-core>
                            <taro-image-core class="img-famous-two hydrated"><img
                                    class="taro-img__mode-aspectfill taro-img__mode-aspectfill--width"
                                    src="/Public/mbti_result_files/ISTP_2.jpeg"></taro-image-core>
                            <taro-view-core class="rec-famous-two hydrated">凯瑟琳·赫本</taro-view-core>
                            <taro-image-core class="img-other-words hydrated"><img class="taro-img__mode-aspectfit"
                                                                                   src="/Public/mbti_result_files/other-words.png">
                            </taro-image-core>
                            <taro-view-core class="names hydrated">
                              <taro-view-core class="name-label hydrated">兰斯阿姆斯特朗</taro-view-core>
                              <taro-view-core class="name-label hydrated">凯瑟琳·赫本</taro-view-core>
                              <taro-view-core class="name-label hydrated">泰格·伍兹</taro-view-core>
                              <taro-view-core class="name-label hydrated">阿米莉亚·埃尔哈特</taro-view-core>
                              <taro-view-core class="name-label hydrated">李小龙</taro-view-core>
                              <taro-view-core class="name-label hydrated">迈尔斯·戴维斯</taro-view-core>
                              <taro-view-core class="name-label hydrated">查克·耶格尔</taro-view-core>
                              <taro-view-core class="name-label hydrated">克林特·伊斯特伍德</taro-view-core>
                            </taro-view-core>
                          </taro-view-core>
                          <taro-view-core class="content-area color_person hydrated">
                            <taro-image-core class="dongji_png hydrated"><img class="taro-img__mode-aspectfit"
                                                                              src="/Public/mbti_result_files/dongji.png">
                            </taro-image-core>
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">VALUES AND MOTIVATION</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">价值观和动机</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="article-paragraph pre-wrap hydrated">
你对你周围世界的机制很好奇，通常具有在其身处环境中操纵工具的独特能力。你倾向于研究事物是如何工作的，并且经常精通机器、仪器和设备的使用和操作。你试图理解原理，但实际上，你喜欢能够立即使用你的技术知识，并很快厌倦了理论。

与人类复杂的情感相比，你倾向于独立，更喜欢机械事物的逻辑。你是独立和保守的，你珍惜自己的个人空间，希望自发而动和追随内心。你对自己的关系是有选择性的，你欣赏那些给你足够的自由做你自己的事情的人。
                            </taro-view-core>
                          </taro-view-core>
                          <taro-view-core class="content-area color_person hydrated">
                            <taro-image-core class="left_bg_png hydrated"><img class="taro-img__mode-aspectfit"
                                                                               src="/Public/mbti_result_files/left_bg.png">
                            </taro-image-core>
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">YOUR CHARACTER</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">你的性格特点</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2): ?> class="myshadow" <?php endif; ?>  >
                            <taro-view-core class="advantage-container hydrated">
                              <taro-image-core class="img-advantage hydrated"><img class="taro-img__mode-aspectfit"
                                                                                   src="/Public/mbti_result_files/advantage.png">
                              </taro-image-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">实际而有创意</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你的优势体现在你的技术和机械技能上，从修理自行车，到射击，到修理一级方程式赛车等。你精通工具的使用，善用你的修理能力，使工具和机器为你工作。你可以修理大多数东西，用手把想法付诸行动。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">解决问题</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你擅长解决问题，因为你能够采集大量的感官信息，并有效地进行分类，以便在需要的时候随时使用，即使是在最紧张的时刻，也行之有效。你对自己的知识和能力充满信心，在任何紧急情况下都是实干家，随时准备投入行动。你不会因为压力而袖手旁观，会主动接受压力。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">富于常识和悟性</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你有一种敏锐的能力，能非常可靠准确地判断他人的性格和动机。人们不太可能去欺骗你，但如果真有人这么做了，你将会非常心烦意乱，不清楚这一切是为什么。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">灵活</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你在各种类型的身体活动中都表现得特别好，因为你能够有足够的洞察力评估你的竞争力，并适当地定位自己，以确保胜利。你灵活、适应能力强，为了及时对情况做出反应，你会迅速即兴发挥，相信自己的身体和本能。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="advantage-container hydrated">
                              <taro-image-core class="img-advantage hydrated"><img class="taro-img__mode-aspectfit"
                                                                                   src="/Public/mbti_result_files/disadvantage.png">
                              </taro-image-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">不敏感</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你习惯于直率的交流，在大多数时候这可能是一种优势，因为这意味着你诚实、直接、有逻辑。但如果使用过度，尤其是当人们不符合你关于构成逻辑性、理性或实用性的标准时，这一切就会变得相当苛刻，且带有批判性。当被要求使用一种对你来说非本能的思维方式时，你可能会不悦，而不会简单地去接受不同的思考方式。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">行为冒险</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你的不安分和不断追求刺激的性格会导致你追求冒险、甚至鲁莽的行为，不能充分权衡行为的代价。虽然享乐主义是你的一种自然倾向，但有一部分你在追求快乐、刺激、肾上腺素和即时满足时不太明智，甚至有些偏激，你声称“人生只有一次”。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">缺乏耐心</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你无法很好地处理行动停滞，决策停滞的问题。等待和审议是对你更大的挑战。你没有耐心，对无聊充满厌恶感，这会以不健康的方式助长你的冲动和享乐主义，因为你寻求肾上腺素的快感。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="character-section hydrated">
                                <taro-view-core class="character-title pre-wrap hydrated">保守封闭</taro-view-core>
                                <taro-view-core class="article-paragraph character-content pre-wrap hydrated">
你对私人的空间和独处有强烈的需求。就其本身而言，这种偏好不需要被归类为弱点，但你疏远他人或避免亲密时，问题就会出现。你会过分保护自己的隐私空间、自己的独处、日程安排和做事的方式，对那些未经允许擅自进入这个空间的人会变得非常戒备。
                                </taro-view-core>
                                <taro-view-core class="character-line hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <?php if($row['qt_type'] == 2): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?>
    
    
                          </div>
                          </taro-view-core>
                          <taro-view-core class="content-area color_person hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">POTENTIAL EXPLORATION GUIDE</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">成长建议</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>

                            <div <?php if($row['qt_type'] == 2): ?> class="myshadow" <?php endif; ?>  >
                              <taro-view-core class="potential-container hydrated">
                                <taro-view-core class="potential-section hydrated">
                                  <taro-view-core class="potential-title false hydrated">
                                    <taro-view-core class="pre-wrap hydrated">制定计划</taro-view-core>
                                    <taro-image-core class="img-potential-icon hydrated"><img
                                            class="taro-img__mode-aspectfit"
                                            src="/Public/mbti_result_files/potential-icon.png"></taro-image-core>
                                  </taro-view-core>
                                  <taro-view-core class="pre-article-paragraph potential-content pre-wrap hydrated">
你习惯于避免长期计划，你更喜欢在当下做决定，活在当下。这意味着你经常不采取有助于长远进步的措施。虽然你不需要保持你的计划一成不变，但计划有助于制定一个总体框架，以便做出正确的决策并采取相应的措施，更好地促进职业道路的成功。你可以试着努力设定目标，做出承诺和制定长期计划。
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="potential-section hydrated">
                                  <taro-view-core class="potential-title another-color hydrated">
                                    <taro-view-core class="pre-wrap hydrated">平衡好机会主义倾向</taro-view-core>
                                    <taro-image-core class="img-potential-icon hydrated"><img
                                            class="taro-img__mode-aspectfit"
                                            src="/Public/mbti_result_files/potential-icon.png"></taro-image-core>
                                  </taro-view-core>
                                  <taro-view-core class="pre-article-paragraph potential-content pre-wrap hydrated">
你天生就知道如何定位自己，以及如何成功熟练地利用自己的资源和优势。因此，你可能是具有剥削主义和机会主义倾向的。你需要注意自己的目标和动机，尽量确保不利用别人。
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="potential-section hydrated">
                                  <taro-view-core class="potential-title false hydrated">
                                    <taro-view-core class="pre-wrap hydrated">学习时间管理技巧</taro-view-core>
                                    <taro-image-core class="img-potential-icon hydrated"><img
                                            class="taro-img__mode-aspectfit"
                                            src="/Public/mbti_result_files/potential-icon.png"></taro-image-core>
                                  </taro-view-core>
                                  <taro-view-core class="pre-article-paragraph potential-content pre-wrap hydrated">
作为感知类型，你更喜欢让生活来决定一天的进程，而不是主动决定时间将如何度过和做什么事。努力培养时间管理技能可以帮助你更好地掌控自己的时间，以便积极地优先安排利用时间，而不是被随机支配。更好的时间管理策略还可以帮助你克服拖延症的天性。
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="potential-section hydrated">
                                  <taro-view-core class="potential-title another-color hydrated">
                                    <taro-view-core class="pre-wrap hydrated">停下和反思</taro-view-core>
                                    <taro-image-core class="img-potential-icon hydrated"><img
                                            class="taro-img__mode-aspectfit"
                                            src="/Public/mbti_result_files/potential-icon.png"></taro-image-core>
                                  </taro-view-core>
                                  <taro-view-core class="pre-article-paragraph potential-content pre-wrap hydrated">
你很容易被眼前的情况所吸引，一旦一件事结束了，你很快就会转向下一件事。你的生活过于专注于外部，以至于经常没有时间来反省自己的行为和感受，或者从错误中吸取教训。当你在行动中休息片刻，花一点时间思考生活、感受，要去哪里、需要怎样成长，下一步目标将是什么时，你就会发展得更加完整。
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="potential-section hydrated">
                                  <taro-view-core class="potential-title false hydrated">
                                    <taro-view-core class="pre-wrap hydrated">重新定义承诺</taro-view-core>
                                    <taro-image-core class="img-potential-icon hydrated"><img
                                            class="taro-img__mode-aspectfit"
                                            src="/Public/mbti_result_files/potential-icon.png"></taro-image-core>
                                  </taro-view-core>
                                  <taro-view-core class="pre-article-paragraph potential-content pre-wrap hydrated">
在生活、爱情和工作中，你通常对做出承诺犹豫不决。你活在当下，把精力集中在当下。因此，你很难想象自己会在哪里，或者想在未来做什么。为了成为成功的人，你可以每天更新承诺，不需要宣布十年后会是什么样子，只需要从许多小的、日常的承诺中重新定义承诺，随着时间的推移，这些承诺会形成类似长期成就的东西。
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <?php if($row['qt_type'] == 2): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?>
                            </div>


                          </taro-view-core>
                          <taro-view-core class="content-area person_eight_h5 color_person hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">JUNG'S EIGHT-DIMENSIONAL INTERPRETATION
                              </taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">荣格八维解读性格优劣势</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2): ?> class="myshadow" <?php endif; ?> >
                              <taro-view-core class="person_eight_h5_card hydrated">
                                <taro-view-core class="card_header hydrated">
                                  <taro-view-core class="card_title pre-wrap hydrated">内向思考 (Ti)</taro-view-core>
                                  <taro-image-core class="card_title_img hydrated"><img class="taro-img__mode-scaletofill"
                                                                                        src="/Public/mbti_result_files/eight_bg.png">
                                  </taro-image-core>
                                </taro-view-core>
                                <taro-view-core class="card_content hydrated">
                                  <taro-view-core class="card_content_title pre-wrap hydrated">根据原则进行分析、分类和评估
                                  </taro-view-core>
                                  <taro-view-core class="card_content_text pre-wrap hydrated">
此功能为你的优势功能，该功能是天赋型功能，在需要运用该功能的领域里你会有明显优于平均水平的表现。

Ti功能提供两大优势：找出逻辑上的不一致性及正直与诚实

找出逻辑上的不一致性
Ti功能是一种主观功能，专注于发现逻辑谬误和偏见。你的脑袋运作时犹如电脑，能够组织资料，冷静和客观地找出事实背后的脉络。你小心观察你身边所发生的事情。然后，在需要之时，你能迅速地抓住问题的核心，运用最有效率、最省工夫的方法去解决。

正直和诚实
Ti功能帮助你诚实地表达真相，虽然真相可能会对其他人造成伤害，但它不一定是无情或残忍的。你在说出严酷的事实时会表现出善意，而当其他人为你提供真诚的服务，你也会心存感激。对于你来说，培养对自己彻底的诚实也很重要，这会确保你忠于自己的价值观。
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="person_eight_h5_card hydrated">
                                <taro-view-core class="card_header hydrated">
                                  <taro-view-core class="card_title pre-wrap hydrated">外向感觉 (Se)</taro-view-core>
                                  <taro-image-core class="card_title_img hydrated"><img class="taro-img__mode-scaletofill"
                                                                                        src="/Public/mbti_result_files/eight_bg.png">
                                  </taro-image-core>
                                </taro-view-core>
                                <taro-view-core class="card_content hydrated">
                                  <taro-view-core class="card_content_title pre-wrap hydrated">在直接环境中体验和行动
                                  </taro-view-core>
                                  <taro-view-core class="card_content_text pre-wrap hydrated">
此功能为你的辅助功能，该功能是成长型功能，在需要运用该功能的领域里你会有大量优异表现，但也许不够稳定。要想将该功能发展为优势功能需要一定的刻意练习。

没有发展Se功能，你是不完整的。如果无法获得来自外部世界的反馈，也就无法注意到现实。Se功能使你与各行各业的人产生交流，接受不同意见并允许自己的失败。

注重现实
你是注重事实的人，把注意力集中于现状和该做的事情，而不去想理论上的可能性。你常常能够有创意地去解决当务之急，善于处理需要亲力亲为的工作。

从实践中学
你能异常敏锐地注意到眼前的具体细节和事实及你周围的人和世界。你从实践中学习多于通过阅读或聆听来学习。你爱用权宜之计，信奉省力原则。你只做必须做的事，尽量避免讨论和麻烦。
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="person_eight_h5_card hydrated">
                                <taro-view-core class="card_header hydrated">
                                  <taro-view-core class="card_title pre-wrap hydrated">内向直觉 (Ni)</taro-view-core>
                                  <taro-image-core class="card_title_img hydrated"><img class="taro-img__mode-scaletofill"
                                                                                        src="/Public/mbti_result_files/eight_bg.png">
                                  </taro-image-core>
                                </taro-view-core>
                                <taro-view-core class="card_content hydrated">
                                  <taro-view-core class="card_content_title pre-wrap hydrated">预见影响、转变和可能的影响
                                  </taro-view-core>
                                  <taro-view-core class="card_content_text pre-wrap hydrated">
此功能为你的中立功能，该功能是成长型功能，在需要运用该功能的领域里，你的表现会低于平均水平或者与平均水平持平。随着年龄增长，该功能会逐渐开始开发。但如果想提前发展该功能，需要一定的刻意练习。

Ni功能旨在寻求意义并展望未来，它帮助人们转换视角，和其他观点产生共鸣。如果Ni功能发展不充分，你会过分看重即时成效，而未能跟进你的决定或行动给的长远影响；或无法认真衡量自己的决定对别人的影响。
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="person_eight_h5_card hydrated">
                                <taro-view-core class="card_header hydrated">
                                  <taro-view-core class="card_title pre-wrap hydrated">外向情感 (Fe)</taro-view-core>
                                  <taro-image-core class="card_title_img hydrated"><img class="taro-img__mode-scaletofill"
                                                                                        src="/Public/mbti_result_files/eight_bg.png">
                                  </taro-image-core>
                                </taro-view-core>
                                <taro-view-core class="card_content hydrated">
                                  <taro-view-core class="card_content_title pre-wrap hydrated">连接并考虑他人和团队</taro-view-core>
                                  <taro-view-core class="card_content_text pre-wrap hydrated">
此功能是你的劣势功能，在需要运用该功能的领域里你会有明显低于平均水平的表现。要发展该功能，需要大量的刻意练习。

Fe功能旨在与他人建立联系、满足他人需求并建立人际关系。Fe是你的劣势功能，也是你的一部分。Fe的弱发展使得你有时看不到别人的感性需要和价值。在巨大的压力下，你会受到Fe功能的反噬。你可能向外做出不适当的情绪发泄，而所表现出来的暴躁愤怒或者伤心哭泣，不但使别人吃惊，甚至平常很冷静和有自制力的你本身，也会对自己的表现感到尴尬。
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <?php if($row['qt_type'] == 2): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?>
                            </div>
                          </taro-view-core>
                          <taro-view-core class="content-area color_work person_work_h5 hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">ISTP AT WORK</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">工作中的ISTP</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> class="myshadow" <?php endif; ?> >
                            <taro-image-core class="work_bg_png hydrated"><img class="taro-img__mode-scaletofill"
                                                                               src="/Public/mbti_result_files/work_bg.png">
                            </taro-image-core>
                            <taro-view-core class="person_work_card hydrated">
                              <taro-view-core class="card_content pre-wrap hydrated">
在团队的情况下，你会用你的事实知识和经验，用实证数据和证据、假设和理性思维，把事情发生的方式和原因解释清楚。你可能不是最爱说话的人，但当讨论进入你感兴趣的领域时，你会变得相当直言不讳，思维非常清晰。你在团队的情况下可能是一个难题。你的兴趣水平将决定你的参与度，而这将是二元对立的：要么退居幕后，要么起主导作用——你的参与或贡献很少是适度的。你的工程型大脑存储了所有的数据，准备在需要的时候立即使用，因此你能够迅速地从被动转为主导，并将所有的细节、知识和经验发挥出来，帮助团队摆脱惯性或困难的局面。你的无聊阈值很低，这意味着你就会很高兴地放弃需求，因为你不需要荣誉，也不喜欢闲聊或任何不感兴趣或没有挑战的事情。

因为你在危机中的表现非常出色，在不艰难的时候，你就不太擅长，也不太擅长更敏感的一面。你的独立性极强，你会需要自由地在短时间内精力充沛地解决困难的行动导向的实际问题，这些问题能引起你的兴趣。常规和稳定的细节会让你感到厌倦，你需要自己的空间。如果变得枯燥无味，那么你就会回到自己的居住空间中去。你本质上是实用的，你更喜欢事实和数据，而不是任何你认为的毛毛雨或不切实际的东西，但当你专注于一个问题时，你是坚贞的。
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="person_work_card hydrated">
                              <taro-view-core class="card_title hydrated">团队中的ISTP</taro-view-core>
                              <taro-view-core class="card_content pre-wrap hydrated">
你是务实和以任务为导向的团队成员，你通常更关注问题本身，而不是与问题相关的人。你会去寻找通过即时行动就能获得产出的方法，你也是才华横溢、直截了当的问题解决者。你很少需要团队的关注，更愿意在观察后，选择能提供机会完成任务的团队。

你为团队带来了高效率，你往往会在你认为需要的地方采取实际行动。你的理性分析直击问题的核心，可以帮助其他人了解如何进一步发展。然而，你往往对抽象的讨论没有太多耐心，你可能会忽视合作上的细节。事实上，许多
ISTP 更喜欢只做必须要做的事情，而不是花时间通知或联合其他人。
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="person_work_card hydrated">
                              <taro-view-core class="card_title hydrated">作为领导的ISTP</taro-view-core>
                              <taro-view-core class="card_content pre-wrap hydrated">
你是灵活且不干涉他人的领导者，你希望自己做出的报告也具有独立性。你使用的方法实用、具体，你更愿意领导团队去做能立即看到具体结果的项目。你在危机中表现出色，你在需要立即采取行动的情况下斗志满满，并会立即站出完成工作。

你有很强的行动导向，你对任务的理解会落实在行动中而不只是口头上说说。你发现自己做某事会比向其他人解释这件事要容易，这会让ISTP领导在分配任务时为难。在团队里，你会更自然地以身作则，而非向员工解释他要做的事。你通常最擅长领导的团队由经验丰富、自给自足的员工组成。
                              </taro-view-core>
                            </taro-view-core>
                            <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?>
  </div>
                          </taro-view-core>
                          <taro-view-core class="content-area color_work person_satisfy_h5 hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">CORE SATISFACTION AT WORK</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">工作中的核心满足感</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="person_satisfy_bg hydrated">WORK CORE</taro-view-core>
                            <div <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> class="myshadow" <?php endif; ?> >
                            <taro-view-core class="person_satisfy_content pre-wrap hydrated">因为你想要理解世界，ISTP需要工作，并使你以最合乎逻辑的方式分析一个抽象的想法或一个具体的项目。内向思维型的人经常被计算机相关的工作所吸引，ISTP 更有可能对设置、维护和修理计算机系统感兴趣。
                            </taro-view-core>
                            <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?>
    </div>
                          </taro-view-core>
                          <taro-view-core class="content-area color_work person_environment_h5 hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">WORKING ENVIRONMENT</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">工作环境</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> class="myshadow" <?php endif; ?> >
                            <taro-view-core class="person_environment_card hydrated">
                              <taro-view-core class="card_title hydrated">最佳工作环境
                                <taro-view-core class="card_logo hydrated">BEST</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="card_content pre-wrap hydrated">
你在记忆和储存知识方面的能力超强，你可以应用这些知识来解决复杂的、需要立即应用的实际问题。然后，你可以沉浸其中，独自快乐地工作，并提出激进而非常快速的解决方案。然后，你会感到厌倦，因为正常的、"例行公事 "的工作对你没有吸引力。所以，你需要多样化的活动，需要有机会在各种活动中浸入和退出。你在危机中的表现是最好的，你对规则、权威和结构的天然无视使你能够专注于并以最有效的方式解决手头的紧急情况。然而，这确实意味着你经常需要一个挑战，事实上是一个危机，以使你的能量移动，因为停滞和稳定状态不适合你。你需要一个更实际的环境，在这里你可以掌握技能，做事情，并看到具体的结果。你虽然不反感人，但就是不以人为中心，你只是不需要人，也不真正 "理解 "情感。你更擅长解决问题，而不是处理 "人的问题"，这并没有真正发挥你的优势，主要是稳健。
                              </taro-view-core>
                            </taro-view-core>
                            <taro-view-core class="person_environment_card bad_work hydrated">
                              <taro-view-core class="card_title hydrated">最差工作环境
                                <taro-view-core class="card_logo hydrated">WORST</taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="card_content pre-wrap hydrated">
有条不紊、按部就班的环境，在这种环境中，遵守规则和条例是最重要的，并得到奖励，而且没有冒险的余地，这不会使你发挥出最好的作用。缓慢的节奏和无休止的细节会让你失去兴趣，因为你需要完全沉浸在解决困难的问题中，或者站在后面增加你的知识储备。你会在实际的环境中茁壮成长，在那里你有自由和灵活性，可以按照自己的方式去做，而不会陷入传统或规则中。你强烈的独立倾向意味着你不希望被微观管理，并需要感觉到你有跳进跳出的自由。缺乏挑战和没有快速反应的设施对你来说不是好事，因为你在困难和压力中茁壮成长，如果你被要求遵循路线和有条不紊地走向结论，你会失去能量。你不愿意花时间在会议上或处理人们的情绪方面，因为你是强烈的事实和实际的，并不真正理解微妙的问题。
                              </taro-view-core>
                            </taro-view-core>
                            <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?>
  
                          </div>
                          </taro-view-core>
                          <taro-view-core class="content-area color_work hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">CAREER REFERENCE BOOK</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">职业参考宝典</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> class="myshadow" <?php endif; ?> >
                              <taro-view-core class="career-container hydrated">
                                <taro-view-core class="career-section false hydrated">
                                  <taro-image-core class="img-clip-icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                                       src="/Public/mbti_result_files/clip-icon-0.png">
                                  </taro-image-core>
                                  <taro-view-core class="career-title pre-wrap hydrated">销售/服务</taro-view-core>
                                  <taro-view-core class="article-paragraph career-content pre-wrap hydrated">
你在工作中不希望被框架和管制束缚，你能够依靠天赋取得优秀的工作成果，你喜欢自然随性的环境，能够发挥自己的能力、找到应对策略，然后采取恰当的行动。你善于独立工作，但是必要时也会加入团队进行合作。通常你善于熟练运用某一类型的工具，也喜欢参加户外体育活动。
                                  </taro-view-core>
                                  <taro-view-core class="jobs hydrated">
                                    <taro-view-core class="job-title pre-wrap hydrated">警察</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">赛车手</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">飞行员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">武器专家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">特工</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">典礼官</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">消防员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">运动器材/商品销售</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">医药销售人员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">私人侦探</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">高中和大学体育教练</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">摄影师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">犯罪学家/弹道学专家</taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="career-section another-career-position hydrated">
                                  <taro-image-core class="img-clip-icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                                       src="/Public/mbti_result_files/clip-icon-1.png">
                                  </taro-image-core>
                                  <taro-view-core class="career-title pre-wrap hydrated">技术</taro-view-core>
                                  <taro-view-core class="article-paragraph career-content pre-wrap hydrated">
你在专业技术性领域能获得职业成就，因为你对事物的本质和原理十分感兴趣，一般在机械领域表现十分出色。你拥有超强的观察力、记忆力，以及结合重要事实和细节的能力。你喜欢动手操作，也善于充分利用逻辑分析能力，通过感官获得具体信息。
                                  </taro-view-core>
                                  <taro-view-core class="jobs hydrated">
                                    <taro-view-core class="job-title pre-wrap hydrated">电子/土木/机械工程师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">技术培训师(一对一形式)</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">信息服务开发者</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">软件开发:应用程序、系统</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">新能源系统工程师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">后勤管理及供应商</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">网络集成专家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">计算机程序员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">海洋生物学家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">质量保证技术员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">可靠性工程师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">系统支持操作员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">网络系统/通信分析师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">家庭网络安装程序/答疑员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">信息处理专家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">软件工程师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">地质学家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">产品安全工程师</taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="career-section false hydrated">
                                  <taro-image-core class="img-clip-icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                                       src="/Public/mbti_result_files/clip-icon-0.png">
                                  </taro-image-core>
                                  <taro-view-core class="career-title pre-wrap hydrated">健康护理</taro-view-core>
                                  <taro-view-core class="article-paragraph career-content pre-wrap hydrated">
健康护理领域的工作对你有吸引力的原因在于，这样的工作对技术性要求极高，需要保证准确度、兼备成熟的实践能力和对操作工具的使用，并且在操作和维护敏感诊断设备时非常耐心。
                                  </taro-view-core>
                                  <taro-view-core class="jobs hydrated">
                                    <taro-view-core class="job-title pre-wrap hydrated">脑电图技术专家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">放射技师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">急诊医生</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">运动生理学家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">牙科助理/保健师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">外科技师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">交通协调员</taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="career-section another-career-position hydrated">
                                  <taro-image-core class="img-clip-icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                                       src="/Public/mbti_result_files/clip-icon-1.png">
                                  </taro-image-core>
                                  <taro-view-core class="career-title pre-wrap hydrated">商业/金融</taro-view-core>
                                  <taro-view-core class="article-paragraph career-content pre-wrap hydrated">
你追求实际和准确性，你会在商业和金融业中得到极大的享受。工作环境也是极为重要的，你需要个人自由度，没有频繁的会议、没有复杂的办公室政治、独立自主的工作环境，对你来说是最棒的。你通常能把混乱的数据和事实整理清晰。你可以很容易看出隐藏在经济环境中的实际情况，准备并且能够对未来变动做出反应。
                                  </taro-view-core>
                                  <taro-view-core class="jobs hydrated">
                                    <taro-view-core class="job-title pre-wrap hydrated">证券分析师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">采购代理和买方</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">银行家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">经济学家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">律师秘书</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">管理顾问(业务运营)</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">律师助理</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">成本估计师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">保险理赔员/考官</taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="career-section false hydrated">
                                  <taro-image-core class="img-clip-icon hydrated"><img class="taro-img__mode-aspectfit"
                                                                                       src="/Public/mbti_result_files/clip-icon-0.png">
                                  </taro-image-core>
                                  <taro-view-core class="career-title pre-wrap hydrated">贸易/操作生产</taro-view-core>
                                  <taro-view-core class="article-paragraph career-content pre-wrap hydrated">
贸易工作吸引你的地方在于具有独立性和实践性。你喜欢真实具体并且有机会实践的任务。你对于完成你感兴趣的工作会全力以赴。所以如果你的兴趣是体育，你会更喜欢做教练。把爱好发展成事业对你来说是一个很好的策略。
                                  </taro-view-core>
                                  <taro-view-core class="jobs hydrated">
                                    <taro-view-core class="job-title pre-wrap hydrated">计算机修理师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">飞机机械师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">机器人和制造工程师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">救护车司机</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">教练</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">木匠</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">汽车修理者</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">电气/电子设备安装师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">汽车配件零售商</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">商业艺术家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">园艺设计师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">公园自然学者</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">视听专家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">电视摄像师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">保险评估师/鉴定师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">刑事调查员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">船长</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">民航飞行员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">飞行教官</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">飞行工程师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">商用直升机飞行员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">机车工程师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">军官</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">有机农场主</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">飞机调度员和空中交通管制员</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">工作室/舞台和特效专家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">车辆和移动设备机械师</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">建筑工人</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">重型和牵引挂车司机</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">银匠</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">动物标本制作者</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">木工</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">乐器制造者</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">素描画家</taro-view-core>
                                    <taro-view-core class="job-title pre-wrap hydrated">模型制造商</taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?>
                            </div>
                          </taro-view-core>
                          <taro-view-core class="content-area color_work hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">WORKPLACE LIGHTNING AVOIDANCE TIPS
                              </taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">职场避雷锦囊</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> class="myshadow" <?php endif; ?> >
                            <taro-image-core class="img-tips-bg hydrated"><img class="taro-img__mode-aspectfit"
                                                                               src="/Public/mbti_result_files/tips-bg.png">
                            </taro-image-core>
                            <taro-view-core class="tips_content pre-wrap hydrated">
尽管我们知道每一个人都是独一无二的个体，但有时你都存在一些相似的潜在陷阱。我们强调陷阱的“潜在性”，因为下面的一些描述可能会与某些人非常符合，而对另一些人则不一定适用。
在考虑这些问题时，你可能会注意到这些倾向性不仅与找工作有关，可能还描述了你在生活中各个方面的体验和感受。你可能会注意到，克服陷阱的关键是有意识地、深思熟虑地发展你的第三和第四（次弱和最弱的）功能。
当中许多的建议执行起来都有一定困难，但是你使用和练习这些功能的次数越多，将来它们会带来的问题和麻烦就越少。
                            </taro-view-core>
                            <taro-view-core class="tips-container hydrated">
                              <taro-view-core class="tips-section hydrated">
                                <taro-view-core class="number-label hydrated">01</taro-view-core>
                                <taro-image-core class="img-number-bg hydrated"><img class="taro-img__mode-aspectfit"
                                                                                     src="/Public/mbti_result_files/number-bg.png">
                                </taro-image-core>
                                <taro-view-core class="tips-title pre-wrap hydrated">试着提前计划，有计划地找工作</taro-view-core>
                                <taro-view-core class="tip-list hydrated">
                                  <taro-view-core class="tips-content-container hydrated">
                                    <taro-view-core class="article-paragraph tips-content pre-wrap hydrated">在真正观察到努力是否有回报之前，不要转向其他看似更令人兴奋的挑战。
                                    </taro-view-core>
                                  </taro-view-core><taro-view-core class="tips-content-container hydrated"><taro-view-core class="article-paragraph tips-content pre-wrap hydrated">有意识地培养自己的毅力，努力工作，并坚持相信行动会带来真正想要的结果。
                                    </taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="tips-section hydrated">
                                <taro-view-core class="number-label hydrated">02</taro-view-core>
                                <taro-image-core class="img-number-bg hydrated"><img class="taro-img__mode-aspectfit"
                                                                                     src="/Public/mbti_result_files/number-bg.png">
                                </taro-image-core>
                                <taro-view-core class="tips-title pre-wrap hydrated">寻找当下可能性之外的其它可能性</taro-view-core>
                                <taro-view-core class="tip-list hydrated">
                                  <taro-view-core class="tips-content-container hydrated">
                                    <taro-view-core class="article-paragraph tips-content pre-wrap hydrated">不要做临时工作。
                                    </taro-view-core>
                                  </taro-view-core>
                                  <taro-view-core class="tips-content-container hydrated">
                                    <taro-view-core class="article-paragraph tips-content pre-wrap hydrated">为自己和事业设定长期目标。问问自己，你希望从现在开始的五年和十年里完成什么。评估一下你正在考虑的工作是否能帮助你实现这些目标。
                                    </taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="tips-section hydrated">
                                <taro-view-core class="number-label hydrated">03</taro-view-core>
                                <taro-image-core class="img-number-bg hydrated"><img class="taro-img__mode-aspectfit"
                                                                                     src="/Public/mbti_result_files/number-bg.png">
                                </taro-image-core>
                                <taro-view-core class="tips-title pre-wrap hydrated">要注意不要在非必要的情况下做出多余的努力
                                </taro-view-core>
                                <taro-view-core class="tip-list hydrated">
                                  <taro-view-core class="tips-content-container hydrated">
                                    <taro-view-core class="article-paragraph tips-content pre-wrap hydrated">避免冒险抄近路，即使你很容易看到这些捷径。密切关注求职的各个阶段，并以同样的精力和努力对待每一个阶段。
                                    </taro-view-core>
                                  </taro-view-core>
                                  <taro-view-core class="tips-content-container hydrated">
                                    <taro-view-core class="article-paragraph tips-content pre-wrap hydrated">请记住，雇主希望员工有责任心。所以，展示你愿意付出努力来把工作做好的态度。
                                    </taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="tips-section hydrated">
                                <taro-view-core class="number-label hydrated">04</taro-view-core>
                                <taro-image-core class="img-number-bg hydrated"><img class="taro-img__mode-aspectfit"
                                                                                     src="/Public/mbti_result_files/number-bg.png">
                                </taro-image-core>
                                <taro-view-core class="tips-title pre-wrap hydrated">做决定不要拖得太久</taro-view-core>
                                <taro-view-core class="tip-list hydrated">
                                  <taro-view-core class="tips-content-container hydrated">
                                    <taro-view-core class="article-paragraph tips-content pre-wrap hydrated">做决定，然后继续前进。排除掉不佳的选择，继续寻找真正想要的工作。
                                    </taro-view-core>
                                  </taro-view-core>
                                  <taro-view-core class="tips-content-container hydrated"><taro-view-core class="article-paragraph tips-content pre-wrap hydrated">不要因为拖延太久而让自己显得不可靠和缺乏方向。</taro-view-core>
                                  </taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                          </taro-view-core>
                         <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> <div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?> 
                        </div>
                          <taro-view-core class="content-area color_love person_LoveState_h5 hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">IN LOVE</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">恋爱状态</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> class="myshadow" <?php endif; ?> >
                            <taro-view-core class="person_bottomPart hydrated">
                              <taro-view-core class="loveState_card_header pre-wrap hydrated">
恋爱中的你独立又冷静。你通常善于打理房屋，并通过迅速有效地解决问题享受能帮助到伴侣的感觉。

你喜欢有趣和冒险的活动，你经常鼓励伴侣学习新奇的和带有刺激感的身体技能。你善于直接回应伴侣的生理需求，但可能不太擅长处理情绪。你是天生的问题解决者，你会为问题找到符合逻辑又易于落实的解决方案。但面对更复杂的个人问题时，你可能就会感到为难了。

你通常很注重隐私，你很有可能不告诉他人自己的感受和反应。对你来说，这不是隐瞒：你只是更想继续下一项活动，而不是沉迷于情绪。你知道你情绪的暂时性，你也不会是谈话中有趣的那个人。

你不太可能做出华丽的演讲或浪漫的提议，你更可能通过为伴侣提供实际服务的方式来表达爱意。你希望伴侣既能欣赏你为完成工作展现出的能力，又能留给你足够的自由做自己的事情。
                              </taro-view-core>
                              <taro-view-core class="loveState_card hydrated">
                                <taro-view-core class="loveState_card_title hydrated">单身时期</taro-view-core>
                                <taro-view-core class="loveState_card_content pre-wrap hydrated">
你追寻刺激的生活和任何围绕着你的兴趣的事，不管是赛车、跳伞、飙车、冲浪或任何其他对以满足你对刺激欲望的活动。独立是你的代名词，而寻找冒险是你的游戏规则。
当尝试任何新经验时，你是完全无畏无惧 。总是愿意尝试一项新的冒险。当然，大部分时间里，你都是自己一人参与冒险，因为其他人可能不敢参与那些莽撞的举动。
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="loveState_card hydrated">
                                <taro-view-core class="loveState_card_title hydrated">恋爱前中期</taro-view-core>
                                <taro-view-core class="loveState_card_content pre-wrap hydrated">
恋爱中的你独立又冷静。你通常善于打理房屋，并通过迅速有效地解决问题享受能帮助到伴侣的感觉。

你喜欢有趣和冒险的活动，你经常鼓励伴侣学习新奇的和带有刺激感的身体技能。你善于直接回应伴侣的生理需求，但可能不太擅长处理情绪。你是天生的问题解决者，你会为问题找到符合逻辑又易于落实的解决方案。但面对更复杂的个人问题时，你可能就会感到为难了。

你通常很注重隐私，你很有可能不告诉他人自己的感受和反应。对你来说，这不是隐瞒：你只是更想继续下一项活动，而不是沉迷于情绪。你知道你情绪的暂时性，你也不会是谈话中有趣的那个人。

你不太可能做出华丽的演讲或浪漫的提议，你更可能通过为伴侣提供实际服务的方式来表达爱意。你希望伴侣既能欣赏你为完成工作展现出的能力，又能留给你足够的自由做自己的事情。
                                </taro-view-core>
                              </taro-view-core>
                              <taro-view-core class="loveState_card hydrated">
                                <taro-view-core class="loveState_card_title hydrated">恋爱后期</taro-view-core>
                                <taro-view-core class="loveState_card_content pre-wrap hydrated">
你为伴侣提供了你所需的大量自由，因为你发现任何限制伴侣的做法都是不可忍受的。你能给恋爱带去动力和热情。你习惯性地关注当下，很少花时间去思考未来可能会发生什么。但这并不是说你不能一辈子只有一段感情。你对事物的看法不是长期的，对你来说，每一天都是新的一页。考虑到这是你通常的思维模式，一想到要立下“直到死亡将我们分开”的誓言，你可能就会充满恐惧。

你天生沉默寡言，很少表达自己的观点和感受。你在恋爱中面临的最大挑战是：你无法识别伴侣的感受和情感需求，这常常被错误地认为是你对伴侣缺乏兴趣。你可能深爱着伴侣，但同时，你却完全不理解你的情绪和你的感受和经历。出于同样的原因，你可能根本无法理解伴侣需要赞美和感情。因为你自己没有这样的情感需求，其他人有这样的需求的事实可能会让你感到震惊、困惑。因为你不知道如何满足他们。

当你的恋爱关系出现危机，你通常会努力挽救危机。但是，如果你的努力没有结果，你可能会放弃，屈服于情况已经超出控制的事实，或者认为伴侣的要求太过分。总的来说，你能从对你造成伤害的关系里抽身。
                                </taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?><div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?> 
    </div>
                          </taro-view-core>
                          <taro-view-core class="content-area color_love person_loveMatching_h5 hydrated">
                            <taro-view-core class="article-content-header hydrated">
                              <taro-view-core class="en-title hydrated">BEST LOVE MATCH TYPES</taro-view-core>
                              <taro-view-core class="title hydrated">
                                <taro-view-core class="card_title hydrated">最佳恋爱匹配类型</taro-view-core>
                                <taro-view-core class="rec-for-header hydrated"></taro-view-core>
                                <taro-view-core class="line-for-header hydrated"></taro-view-core>
                              </taro-view-core>
                            </taro-view-core>
                            <div <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?> class="myshadow" <?php endif; ?> >
                              <taro-view-core class="person_bottomPart hydrated">
                                <taro-view-core class="loveMatching_card hydrated">
                                  <taro-image-core class="loveMatching_card_img hydrated"><img
                                          class="taro-img__mode-aspectfit"
                                          src="/Public/mbti_result_files/ENTJ-2.png">
                                  </taro-image-core>
                                  <taro-view-core class="loveMatching_card_title hydrated">领导者ENTJ</taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="loveMatching_card hydrated">
                                  <taro-image-core class="loveMatching_card_img hydrated"><img
                                          class="taro-img__mode-aspectfit"
                                          src="/Public/mbti_result_files/ESTJ-2.png">
                                  </taro-image-core>
                                  <taro-view-core class="loveMatching_card_title hydrated">管理者ESTJ</taro-view-core>
                                </taro-view-core>
                                <taro-view-core class="loveMatching_card hydrated">
                                  <taro-image-core class="loveMatching_card_img hydrated"><img
                                          class="taro-img__mode-aspectfit"
                                          src="/Public/mbti_result_files/ISTJ-2.png">
                                  </taro-image-core>
                                  <taro-view-core class="loveMatching_card_title hydrated">检查者ISTJ</taro-view-core>
                                </taro-view-core>
                              </taro-view-core>
                              <?php if($row['qt_type'] == 2 or $row['qt_type'] == 1): ?><div ><button class="unlock_full_btn">解锁完整报告</button></div> <?php endif; ?> 
                            </div>
                          </taro-view-core>
                        </taro-view-core>


                      </taro-scroll-view-core>
                    </taro-scroll-view-core>
                  </taro-view-core>
                </taro-view-core>
              </taro-scroll-view-core>
            </taro-view-core>
          </taro-view-core>
        </div>
      </div>
    </div>
  </div>
</div>
<div id="my_pop_shadow"></div>
<uni-view id="my_pop_box">
    <div data-v-6d27db42="" data-v-c3c1a80a="" class="pay_wrap_box none">

        <div data-v-6d27db42="" class="pay_type pay_type2">
            <ul data-v-6d27db42="" class="ul">
                <?php if($row['qt_type'] == 2): ?>
                <li data-v-6d27db42="" class="active" data-qt-type="2">
                    <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整报告</p>
                    <p data-v-6d27db42=""> <span class="full_price">¥10</span> <del data-v-6d27db42="">¥99</del></p>
                    <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">在简要报告的基础上，增加了性格优劣势详细分析，成长建议与成就以及荣格八维的解读，真正有深度的性格剖析</p>
                    <div data-v-6d27db42="" class="tuijian">92%的人选择</div>
                   
                </li>
                <?php endif; ?>

                <li data-v-6d27db42="" class="" data-qt-type="3">
                    <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整解读Pro</p>
                    <p data-v-6d27db42=""> <span class="pro_price">¥<?php echo $pro_diff_price; ?></span> <del data-v-6d27db42="">¥699</del></p>
                    <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">MBTI十六型人格完整解读报告，解锁12个大类，45个子类全部报告内容，自我认知，人际交往，职业发展，恋爱交友等维度深度剖析，详实呈现</p>
                    <!---->
                    <div data-v-6d27db42="" class="tuijian-today">仅限今日</div>
                </li>
            </ul>
            <div data-v-6d27db42="" class="btn">
                <div data-v-6d27db42="" class="pay-btn-wrapper width-100">
                    <button data-v-6d27db42="" onclick="updatOrder(0)" class="pay-button van-button van-button--primary van-button--small van-button--block van-button--round">
                        <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-wechat-pay van-button__icon">
                                <!---->
                            </i><span data-v-6d27db42="" class="van-button__text">微信支付</span></div>
                    </button>
                    <?php if(!$is_weixin_browser): ?>
                    <button data-v-6d27db42="" onclick="updatOldOrder(1)" class="pay-button van-button van-button--info van-button--small van-button--block van-button--round">
                        <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-alipay van-button__icon">
                                <!---->
                            </i><span data-v-6d27db42="" class="van-button__text">支付宝支付</span></div>
                    </button>
                    <?php endif; ?>
                </div>
                <div data-v-6d27db42="" class="center"> 已有<span class="buynumshow">238997</span>
                  <div data-v-6d27db42="" class="scroll">=<div class="scroll-item">0</div></div>人购买
                  </div>

                <div data-v-6d27db42="" class="center quanli">付费解锁后，您可以享受以下权利</div>
                <div data-v-6d27db42="" class="quanli_item">
                    <ul data-v-6d27db42="" class="item item2">
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI类型</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">基础解读</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI字母详解</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">报告永久保存</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">完整解读</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">同类型占比</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">价值观分析</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">性格解析-优劣势</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">成长建议</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">荣格八维性格解读</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">不同阶段的你</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职场避雷锦囊</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">团队中的你</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职业参考宝典</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">恋爱状态</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">最佳恋爱匹配类型</span></li>
                    </ul>
                </div>
                <div data-v-6d27db42="" class="renzheng"><img data-v-6d27db42="" src="https://ggbondtech.com/rz.png" alt=""></div>
                <div data-v-6d27db42="" class="lv">支付系统已经过安全联盟认证请放心使用</div>
            </div>
        </div>
    </div>
</uni-view>
<link rel="stylesheet" href="/Public/mbit/result2_files/mbtijg.css">
<script>
    <?php if($row['qt_type'] == 2): ?>
        var currentReportType = 2; // 默认选中第二个报告
    <?php else: ?>
    var currentReportType = 3; // 默认选中第二个报告
    <?php endif; ?>
    
    // 为报告选项添加点击事件
  $('.pay_type2 ul li').on('click', function() {
    // 移除所有active类
    $('.pay_type2 ul li').removeClass('active');
    // 为当前点击项添加active类
    let cindex = $(this).index();
    console.log(cindex);
    // 为当前点击项和对应位置的另一项添加active类
 
    $('.pay_type2 ul li').eq(cindex).addClass('active');
    
    // 获取当前选中的报告类型(1,2,3)
    currentReportType = $(this).data('qt-type');
   
    
    
    // 根据选中的报告类型更新权限显示
    updateReportPermissions();
  });

  // 更新报告权限显示的函数
  function updateReportPermissions() {
    // 分别获取两个.item容器下的权限项
    const $permissionItems2 = $('.item2 li');
 
    
    // 处理第一个.item容器
    applyPermissionLogic($permissionItems2);
    

    
    function applyPermissionLogic($items) {
      // 根据当前选中的报告类型设置权限显示
      if (currentReportType === 1) {
        // 简要报告 - 从第5个开始变为del
        $items.each(function(index) {
          const $span = $(this).find('span');
          const $del = $(this).find('del');
          
          if (index >= 4) {
            if ($span.length) {
              const attrs = $span.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $span.replaceWith(`<del${attrStr}>` + $span.text() + '</del>');
            }
          } else {
            if ($del.length) {
              const attrs = $del.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
            }
          }
        });
      } else if (currentReportType === 2) {
        // 完整报告 - 只有最后两个变为del
        $items.each(function(index) {
          const $span = $(this).find('span');
          const $del = $(this).find('del');
          
          if (index >= $items.length - 2) {
            if ($span.length) {
              const attrs = $span.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $span.replaceWith(`<del${attrStr}>` + $span.text() + '</del>');
            } else if ($del.length === 0) {
              // 如果没有span但有内容，直接创建del标签
              const text = $(this).text().trim();
              if (text) {
                const $prevSpan = $(this).find('span');
                const attrs = $prevSpan.length ? $prevSpan.get(0).attributes : {};
                let attrStr = '';
                for (let i = 0; i < attrs.length; i++) {
                  attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
                }
                $(this).html(`<del${attrStr}>${text}</del>`);
              }
            }
          } else {
            if ($del.length) {
              const attrs = $del.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
            }
          }
        });
      } else if (currentReportType === 3) {
        // 完整解读Pro - 所有都是span
        $items.each(function() {
          const $del = $(this).find('del');
          if ($del.length) {
            const attrs = $del.get(0).attributes;
            let attrStr = '';
            for (let i = 0; i < attrs.length; i++) {
              attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
            }
            $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
          }
        });
      }
    }
  }

  // 初始化权限显示
  updateReportPermissions();

  // 购买人数本地存储和滚动数字功能
  let buyUserCount = parseInt(localStorage.getItem('buyUserCountTwoPay') || 3389970);
  let currentScroll1 = buyUserCount % 10; // 当前显示的个位数
  let currentScroll2 = (currentScroll1 + 1) % 10; // 当前显示的下一个数字
  let scrollTimer = null;
  let isAnimating = false; // 动画状态标志

  // 初始化显示
  function initializeBuyCount() {
    // 显示十位数开始到最大位数（不显示个位数）
    const displayNumber = Math.floor(buyUserCount / 10);
    $('.buynumshow').text(displayNumber);

    // 初始化滚动数字的HTML结构
    initializeScrollStructure();
  }

  // 初始化滚动数字的HTML结构
  function initializeScrollStructure() {
    // 在scroll容器中创建垂直轮播结构，保持原有的"="符号
    const scrollHtml = `
      =<div class="scroll-wrapper">
        <div class="scroll-item current">${currentScroll1}</div>
        <div class="scroll-item next">${currentScroll2}</div>
      </div>
    `;

    // 替换scroll容器的内容，保持"人购买"文字不变
    $('.scroll').html(scrollHtml);
  }

  // 自然的滚动数字更新
  function updateScrollNumbers() {
    if (isAnimating) {
      return; // 如果正在动画中，跳过这次更新
    }

    isAnimating = true;

    // 随机增加1或2
    const increment = Math.random() < 0.5 ? 1 : 2;

    // 计算新的数值
    const newBuyUserCount = buyUserCount + increment;
    const newScroll1 = newBuyUserCount % 10;
    const newScroll2 = (newScroll1 + 1) % 10;

    // 更新显示的总数（十位数开始）
    const newDisplayNumber = Math.floor(newBuyUserCount / 10);
    $('.buynumshow').text(newDisplayNumber);

    // 准备新的HTML结构用于垂直轮播动画
    const scrollNewHtml = `
      =<div class="scroll-wrapper">
        <div class="scroll-item current">${currentScroll1}</div>
        <div class="scroll-item next">${newScroll1}</div>
      </div>
    `;

    // 更新HTML结构
    $('.scroll').html(scrollNewHtml);

    // 强制重排，确保DOM更新
    $('.scroll')[0].offsetHeight;

    // 使用双重requestAnimationFrame确保动画流畅
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        // 触发滚动动画
        $('.scroll .scroll-wrapper').addClass('scrolling');
      });
    });

    // 动画完成后更新状态
    setTimeout(() => {
      // 更新本地存储和内部状态
      buyUserCount = newBuyUserCount;
      currentScroll1 = newScroll1;
      currentScroll2 = newScroll2;
      localStorage.setItem('buyUserCountTwoPay', buyUserCount);

      // 清理HTML，只保留最终数字，保持"="符号
      $('.scroll').html(`=<div class="scroll-item">${currentScroll1}</div>`);

      // 重置动画状态
      isAnimating = false;

      console.log('购买人数更新完成:', buyUserCount, '显示数字:', newDisplayNumber, 'scroll1:', currentScroll1, 'scroll2:', currentScroll2);
    }, 800); // 动画持续时间
  }

  // 启动定时器
  function startScrollTimer() {
    if (scrollTimer) {
      clearInterval(scrollTimer);
    }

    scrollTimer = setInterval(function() {
      updateScrollNumbers();
    }, 2200); // 每1秒更新一次
  }

  // 停止定时器
  function stopScrollTimer() {
    if (scrollTimer) {
      clearInterval(scrollTimer);
      scrollTimer = null;
    }
  }

  // 页面加载完成后初始化
  $(document).ready(function() {
    // 立即初始化显示
    initializeBuyCount();

    // 延迟2秒后启动计数器
    setTimeout(function() {
      startScrollTimer();
    }, 2000);
  });

  // 页面卸载时停止定时器
 /*  $(window).on('beforeunload', function() {
    stopScrollTimer();
  }); */

</script>
<style>
    .van-button {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: 44px;
    margin: 0;
    padding: 0;
    font-size: 16px;
    line-height: 1.2;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
    -webkit-transition: opacity .2s;
    transition: opacity .2s;
    -webkit-appearance: none
}

.van-button:before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    opacity: 0;
    content: " "
}

.van-button:active:before {
    opacity: .1
}

.van-button--disabled:before,.van-button--loading:before {
    display: none
}

.van-button--default {
    color: #323233;
    background-color: #fff;
    border: 1px solid #ebedf0
}

.van-button--primary {
    color: #fff;
    background-color: #07c160;
    border: 1px solid #07c160
}

.van-button--info {
    color: #fff;
    background-color: #1989fa;
    border: 1px solid #1989fa
}

.van-button--danger {
    color: #fff;
    background-color: #ee0a24;
    border: 1px solid #ee0a24
}

.van-button--warning {
    color: #fff;
    background-color: #ff976a;
    border: 1px solid #ff976a
}

.van-button--plain {
    background-color: #fff
}

.van-button--plain.van-button--primary {
    color: #07c160
}

.van-button--plain.van-button--info {
    color: #1989fa
}

.van-button--plain.van-button--danger {
    color: #ee0a24
}

.van-button--plain.van-button--warning {
    color: #ff976a
}

.van-button--large {
    width: 100%;
    height: 50px
}

.van-button--normal {
    padding: 0 15px;
    font-size: 14px
}

.van-button--small {
    height: 32px;
    padding: 0 8px;
    font-size: 12px
}

.van-button__loading {
    color: inherit;
    font-size: inherit
}

.van-button--mini {
    height: 24px;
    padding: 0 4px;
    font-size: 10px
}

.van-button--mini+.van-button--mini {
    margin-left: 4px
}

.van-button--block {
    display: block;
    width: 100%
}

.van-button--disabled {
    cursor: not-allowed;
    opacity: .5
}

.van-button--loading {
    cursor: default
}

.van-button--round {
    border-radius: 999px
}

.van-button--square {
    border-radius: 0
}

.van-button__content {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    height: 100%
}

.van-button__content:before {
    content: " "
}

.van-button__icon {
    font-size: 1.2em;
    line-height: inherit
}

.van-button__icon+.van-button__text,.van-button__loading+.van-button__text,.van-button__text+.van-button__icon,.van-button__text+.van-button__loading {
    margin-left: 4px
}

.van-button--hairline {
    border-width: 0
}

.van-button--hairline:after {
    border-color: inherit;
    border-radius: 4px
}

.van-button--hairline.van-button--round:after {
    border-radius: 999px
}

.van-button--hairline.van-button--square:after {
    border-radius: 0
}

.van-submit-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
    -webkit-user-select: none;
    user-select: none
}

.van-submit-bar__tip {
    padding: 8px 12px;
    color: #f56723;
    font-size: 12px;
    line-height: 1.5;
    background-color: #fff7cc
}

.van-submit-bar__tip-icon {
    min-width: 18px;
    font-size: 12px;
    vertical-align: middle
}

.van-submit-bar__tip-text {
    vertical-align: middle
}

.van-submit-bar__bar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    height: 50px;
    padding: 0 16px;
    font-size: 14px
}

.van-submit-bar__text {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding-right: 12px;
    color: #323233;
    text-align: right
}

.van-submit-bar__text span {
    display: inline-block
}

.van-submit-bar__suffix-label {
    margin-left: 5px;
    font-weight: 500
}

.van-submit-bar__price {
    color: #ee0a24;
    font-weight: 500;
    font-size: 12px
}

.van-submit-bar__price--integer {
    font-size: 20px;
    font-family: Avenir-Heavy,PingFang SC,Helvetica Neue,Arial,sans-serif
}

.van-submit-bar__button {
    width: 110px;
    height: 40px;
    font-weight: 500;
    border: none
}

.van-submit-bar__button--danger {
    background: -webkit-linear-gradient(left,#ff6034,#ee0a24);
    background: linear-gradient(to right,#ff6034,#ee0a24)
}

.van-submit-bar--unfit {
    padding-bottom: 0
}

.van-goods-action-button {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    height: 40px;
    font-weight: 500;
    font-size: 14px;
    border: none;
    border-radius: 0
}

.van-goods-action-button--first {
    margin-left: 5px;
    border-top-left-radius: 999px;
    border-bottom-left-radius: 999px
}

.van-goods-action-button--last {
    margin-right: 5px;
    border-top-right-radius: 999px;
    border-bottom-right-radius: 999px
}

.van-goods-action-button--warning {
    background: -webkit-linear-gradient(left,#ffd01e,#ff8917);
    background: linear-gradient(to right,#ffd01e,#ff8917)
}

.van-goods-action-button--danger {
    background: -webkit-linear-gradient(left,#ff6034,#ee0a24);
    background: linear-gradient(to right,#ff6034,#ee0a24)
}

@media (max-width: 321px) {
    .van-goods-action-button {
        font-size:13px
    }
}

.van-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    box-sizing: content-box;
    width: 88px;
    max-width: 70%;
    min-height: 88px;
    padding: 16px;
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    white-space: pre-wrap;
    text-align: center;
    word-break: break-all;
    background-color: #000000b3;
    border-radius: 8px;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0)
}

.van-toast--unclickable {
    overflow: hidden
}

.van-toast--unclickable * {
    pointer-events: none
}

.van-toast--html,.van-toast--text {
    width: -webkit-fit-content;
    width: fit-content;
    min-width: 96px;
    min-height: 0;
    padding: 8px 12px
}

.van-toast--html .van-toast__text,.van-toast--text .van-toast__text {
    margin-top: 0
}

.van-toast--top {
    top: 20%
}

.van-toast--bottom {
    top: auto;
    bottom: 20%
}

.van-toast__icon {
    font-size: 36px
}

.van-toast__loading {
    padding: 4px;
    color: #fff
}

.van-toast__text {
    margin-top: 8px
}

.van-calendar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 100%;
    background-color: #fff
}

.van-calendar__popup.van-popup--bottom,.van-calendar__popup.van-popup--top {
    height: 80%
}

.van-calendar__popup.van-popup--left,.van-calendar__popup.van-popup--right {
    height: 100%
}

.van-calendar__popup .van-popup__close-icon {
    top: 11px
}

.van-calendar__header {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    box-shadow: 0 2px 10px #7d7e8029
}

.van-calendar__header-subtitle,.van-calendar__header-title,.van-calendar__month-title {
    height: 44px;
    font-weight: 500;
    line-height: 44px;
    text-align: center
}

.van-calendar__header-title {
    font-size: 16px
}

.van-calendar__header-subtitle,.van-calendar__month-title {
    font-size: 14px
}

.van-calendar__weekdays {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.van-calendar__weekday {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    font-size: 12px;
    line-height: 30px;
    text-align: center
}

.van-calendar__body {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.van-calendar__days {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-user-select: none;
    user-select: none
}

.van-calendar__month-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 0;
    color: #f2f3f5cc;
    font-size: 160px;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    pointer-events: none
}

.van-calendar__day,.van-calendar__selected-day {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    text-align: center
}

.van-calendar__day {
    position: relative;
    width: 14.285%;
    height: 64px;
    font-size: 16px;
    cursor: pointer
}

.van-calendar__day--end,.van-calendar__day--multiple-middle,.van-calendar__day--multiple-selected,.van-calendar__day--start,.van-calendar__day--start-end {
    color: #fff;
    background-color: #ee0a24
}

.van-calendar__day--start {
    border-radius: 4px 0 0 4px
}

.van-calendar__day--end {
    border-radius: 0 4px 4px 0
}

.van-calendar__day--multiple-selected,.van-calendar__day--start-end {
    border-radius: 4px
}

.van-calendar__day--middle {
    color: #ee0a24
}

.van-calendar__day--middle:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: currentColor;
    opacity: .1;
    content: ""
}

.van-calendar__day--disabled {
    color: #c8c9cc;
    cursor: default
}

.van-calendar__bottom-info,.van-calendar__top-info {
    position: absolute;
    right: 0;
    left: 0;
    font-size: 10px;
    line-height: 14px
}

@media (max-width: 350px) {
    .van-calendar__bottom-info,.van-calendar__top-info {
        font-size:9px
    }
}

.van-calendar__top-info {
    top: 6px
}

.van-calendar__bottom-info {
    bottom: 6px
}

.van-calendar__selected-day {
    width: 54px;
    height: 54px;
    color: #fff;
    background-color: #ee0a24;
    border-radius: 4px
}

.van-calendar__footer {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    padding: 0 16px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom)
}

.van-calendar__footer--unfit {
    padding-bottom: 0
}

.van-calendar__confirm {
    height: 36px;
    margin: 7px 0
}

.van-picker {
    position: relative;
    background-color: #fff;
    -webkit-user-select: none;
    user-select: none
}

.van-picker__toolbar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    height: 44px
}

.van-picker__cancel,.van-picker__confirm {
    height: 100%;
    padding: 0 16px;
    font-size: 14px;
    background-color: transparent;
    border: none;
    cursor: pointer
}

.van-picker__cancel:active,.van-picker__confirm:active {
    opacity: .7
}

.van-picker__confirm {
    color: #576b95
}

.van-picker__cancel {
    color: #969799
}

.van-picker__title {
    max-width: 50%;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    text-align: center
}

.van-picker__columns {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    cursor: grab
}

.van-picker__loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    color: #1989fa;
    background-color: #ffffffe6
}

.van-picker__frame {
    position: absolute;
    top: 50%;
    right: 16px;
    left: 16px;
    z-index: 2;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    pointer-events: none
}

.van-picker__mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-image: -webkit-linear-gradient(top,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4)),-webkit-linear-gradient(bottom,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4));
    background-image: linear-gradient(180deg,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4)),linear-gradient(0deg,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4));
    background-repeat: no-repeat;
    background-position: top,bottom;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    pointer-events: none
}

.van-picker-column {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    overflow: hidden;
    font-size: 16px
}

.van-picker-column__wrapper {
    -webkit-transition-timing-function: cubic-bezier(.23,1,.68,1);
    transition-timing-function: cubic-bezier(.23,1,.68,1)
}

.van-picker-column__item {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 0 4px;
    color: #000
}

.van-picker-column__item--disabled {
    cursor: not-allowed;
    opacity: .3
}

.van-action-sheet {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    max-height: 80%;
    overflow: hidden;
    color: #323233
}

.van-action-sheet__content {
    -webkit-box-flex: 1;
    -webkit-flex: 1 auto;
    flex: 1 auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch
}

.van-action-sheet__cancel,.van-action-sheet__item {
    display: block;
    width: 100%;
    padding: 14px 16px;
    font-size: 16px;
    background-color: #fff;
    border: none;
    cursor: pointer
}

.van-action-sheet__cancel:active,.van-action-sheet__item:active {
    background-color: #f2f3f5
}

.van-action-sheet__item {
    line-height: 22px
}

.van-action-sheet__item--disabled,.van-action-sheet__item--loading {
    color: #c8c9cc
}

.van-action-sheet__item--disabled:active,.van-action-sheet__item--loading:active {
    background-color: #fff
}

.van-action-sheet__item--disabled {
    cursor: not-allowed
}

.van-action-sheet__item--loading {
    cursor: default
}

.van-action-sheet__cancel {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    box-sizing: border-box;
    color: #646566
}

.van-action-sheet__subname {
    margin-top: 8px;
    color: #969799;
    font-size: 12px;
    line-height: 18px
}

.van-action-sheet__gap {
    display: block;
    height: 8px;
    background-color: #f7f8fa
}

.van-action-sheet__header {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    font-weight: 500;
    font-size: 16px;
    line-height: 48px;
    text-align: center
}

.van-action-sheet__description {
    position: relative;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    padding: 20px 16px;
    color: #969799;
    font-size: 14px;
    line-height: 20px;
    text-align: center
}

.van-action-sheet__description:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 16px;
    bottom: 0;
    left: 16px;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.van-action-sheet__loading-icon .van-loading__spinner {
    width: 22px;
    height: 22px
}

.van-action-sheet__close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    padding: 0 16px;
    color: #c8c9cc;
    font-size: 22px;
    line-height: inherit
}

.van-action-sheet__close:active {
    color: #969799
}

.van-goods-action {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    box-sizing: content-box;
    height: 50px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff
}

.van-goods-action--unfit {
    padding-bottom: 0
}

.van-dialog {
    position: fixed;
    top: 45%;
    left: 50%;
    width: 320px;
    overflow: hidden;
    font-size: 16px;
    background-color: #fff;
    border-radius: 16px;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: .3s;
    transition: .3s;
    -webkit-transition-property: opacity,-webkit-transform;
    transition-property: opacity,-webkit-transform;
    transition-property: transform,opacity;
    transition-property: transform,opacity,-webkit-transform
}
    .pay_wrap_box[data-v-6d27db42] {
    padding: 0 .4rem;
    box-sizing: border-box;
    display: block
}

.pay_wrap_box .pay_t[data-v-6d27db42] {
    text-align: center
}

.pay_wrap_box .pay_t p[data-v-6d27db42] {
    margin: 0;
    line-height: 1.7;
    color: #41464b;
    font-size: 0.59728rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.pay_wrap_box .pay_t p span[data-v-6d27db42] {
    color: red
}

.pay_wrap_box .pay_t p .count[data-v-6d27db42] {
    font-size: 0.85328rem;
    margin: 0 .16rem
}

.pay_wrap_box .pay_t p .count[data-v-6d27db42] .van-count-down {
    color: red;
    font-size: 0.42672rem
}

.pay_wrap_box .pay_type2[data-v-6d27db42] {
    padding-top: 1.42672rem
}

.pay_wrap_box .pay_type ul.ul[data-v-6d27db42] {
    display: flex;
    justify-content: center;
}

.pay_wrap_box .pay_type ul.ul li[data-v-6d27db42] {
    border: .0133rem solid #d8d8d8;
    /* flex: 1; */
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.64rem 0.34128rem;
    box-sizing: border-box;
    position: relative;
    border-radius: .16rem;
    width: 33.33%;
    
}

.pay_wrap_box .pay_type ul.ul li .tuijian[data-v-6d27db42] {
    position: absolute;
    top: -.2667rem;
    height: .5333rem;
    background: linear-gradient(180deg,#b66af0,#7c32cb 98%);
    border-radius: 0 .1333rem;
    font-size: 0.512rem;
    color: #fff;
    padding: 0 .16rem;
    right: -.0133rem;
    display: flex;
    align-items: center
}

.pay_wrap_box .pay_type ul.ul li .tuijian-today[data-v-6d27db42] {
    position: absolute;
    top: -.2667rem;
    height: .5333rem;
    background: linear-gradient(180deg,#ff6006,#ff3306);
    border-radius: 0 .1333rem;
    font-size: 0.512rem;
    color: #fff;
    padding: 0 .16rem;
    right: -.0133rem;
    display: flex;
    align-items: center
}

.pay_wrap_box .pay_type ul.ul li[data-v-6d27db42]:nth-child(2) {
    margin: 0 .2667rem
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42] {
    margin: 0
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42]:first-child {
    color: #333;
    font-size: .4rem
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42]:nth-child(2) {
    margin: 0.42672rem 0;
    font-size: 0.68272rem;
    color: #333;
    display: flex;
    align-items: center
}

.pay_wrap_box .pay_type ul.ul li p:nth-child(2) del[data-v-6d27db42] {
    font-size: 0.512rem;
    color: #666;
    opacity: .5;
    margin-left: .1067rem
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42]:nth-child(3) {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.512rem
}

.pay_wrap_box .pay_type .type1 .type1_jianyao[data-v-6d27db42] {
    background: #fef6f0;
    border: .0133rem solid #edbf79;
    border-radius: .1333rem;
    padding: 0.64rem 0.34128rem;
    box-sizing: border-box;
    text-align: center;
    color: #6c5229;
    font-size: .4rem;
    font-weight: 700;
    margin-bottom: 0.72528rem
}

.pay_wrap_box .pay_type .type1 .type1_jianyao .p2[data-v-6d27db42] {
    margin: 0.256rem 0;
    color: #999;
    font-size: 0.512rem;
    font-weight: 400
}

.pay_wrap_box .pay_type .type1 .type1_jiagou[data-v-6d27db42] {
    border: .0133rem solid #e1e0e0;
    border-radius: .1333rem;
    padding: .4rem;
    box-sizing: border-box;
    color: #666;
    font-size: 0.512rem;
    margin-bottom: 0.72528rem;
    position: relative
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .p1[data-v-6d27db42] {
    font-size: .4rem;
    font-weight: 700
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex[data-v-6d27db42] {
    display: flex
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex .jiagou_flex_l[data-v-6d27db42] {
    font-size: .24rem
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex .jiagou_flex_c[data-v-6d27db42] {
    white-space: nowrap;
    margin: 0 .2667rem;
    font-size: .4rem;
    color: #333;
    font-weight: 700
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex .jiagou_flex_c .total_price[data-v-6d27db42] {
    white-space: nowrap;
    transform: scale(.8);
    font-weight: 400;
    font-size: .4rem;
    text-decoration: line-through;
    color: #b0b0b0;
    position: absolute
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .chaozhijiagou_tag[data-v-6d27db42] {
    position: absolute;
    top: 0;
    left: 0;
    background: red;
    border-radius: .16rem 0;
    padding: .0267rem .16rem;
    font-size: 0.512rem;
    color: #fff
}

.pay_wrap_box .active[data-v-6d27db42] {
    border-color: #edbf79!important;
    background: #fef6f0
}

.pay_wrap_box .active p[data-v-6d27db42]:first-child {
    color: #6c5229!important
}

.pay_wrap_box .active p[data-v-6d27db42]:nth-child(2) {
    color: #6c5229!important;
    font-weight: 700
}

.pay_wrap_box .active p:nth-child(2) del[data-v-6d27db42] {
    font-weight: 400
}

.pay_wrap_box .btn[data-v-6d27db42] {
    margin-top: 0.42672rem
}

.pay_wrap_box .btn .pay-btn-wrapper[data-v-6d27db42] {
    display: flex
}
.width-100{
    width: 100%;
}
.pay_wrap_box .btn .pay-button[data-v-6d27db42] {
    margin-bottom: .2133rem
}

.pay_wrap_box .btn .pay-button+.pay-button[data-v-6d27db42] {
    margin-left: .4rem
}

.pay_wrap_box .btn .center[data-v-6d27db42] {
    text-align: center;
    font-size: 0.512rem;
    color: #666;
    margin-bottom: 0.42672rem;
    height: 20px;
}

.pay_wrap_box .btn .give_up_pay[data-v-6d27db42] {
    width: 100%;
    background: #9a999c;
    border-radius: 13.32rem;
    display: flex;
    box-sizing: border-box;
    padding: 0.29872rem 0;
    color: #fff;
    align-items: center;
    justify-content: center;
    margin-bottom: .24rem
}

.pay_wrap_box .btn .give_up_pay>div[data-v-6d27db42] {
    display: flex;
    align-items: center
}

.pay_wrap_box .btn .quanli[data-v-6d27db42] {
    color: #c58c34;
    font-weight: 500;
    font-size: 0.59728rem;
    margin-bottom: 0.42672rem
}

.pay_wrap_box .quanli_item .item[data-v-6d27db42] {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 .5333rem;
    height: auto;
    width: 346px;
}

.pay_wrap_box .quanli_item .item li[data-v-6d27db42] {
    display: flex;
    align-items: center;
    line-height: 1.6;
    color: #333;
    font-weight: 500;
    min-width: 5.97328rem;
    font-size: 0.512rem
}

.pay_wrap_box .quanli_item .item li span[data-v-6d27db42] {
    white-space: nowrap;
    font-size: 0.512rem
}

.pay_wrap_box .quanli_item .item li img[data-v-6d27db42] {
    width: .4267rem;
    height: .4267rem;
    margin-right: 0.128rem
}

.pay_wrap_box .quanli_item .item li del[data-v-6d27db42] {
    color: #ccc;
    text-decoration: line-through
}

.pay_wrap_box .renzheng[data-v-6d27db42] {
    display: flex;
    justify-content: center;
    margin-top: 0.42672rem
}

.pay_wrap_box .renzheng img[data-v-6d27db42] {
    height: 1.28rem
}

.pay_wrap_box .lv[data-v-6d27db42] {
    color: #5ec455;
    display: flex;
    justify-content: center;
    line-height: 3;
    font-weight: 500;
    font-size: 0.512rem
}

.pay_wrap_box .scroll[data-v-6d27db42] {
    position: relative;
    display: inline-block;
    color: #fff;
    height: 1.2em; /* 与文字行高一致，只显示一个数字 */
    width: 1em;
    overflow: hidden; /* 隐藏超出部分，实现垂直滚动效果 */
    vertical-align: baseline; /* 与文字基线对齐 */
}

/* scroll1和scroll2样式已移除，使用统一的scroll容器 */

.pay_wrap_box .scroll-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
    /* 使用transform3d启用硬件加速 */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-transition: -webkit-transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    transition: transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    /* 优化渲染性能 */
    will-change: transform;
}

.pay_wrap_box .scroll-wrapper.scrolling {
    -webkit-transform: translate3d(0, -1.4em, 0);
    transform: translate3d(0, -1.4em, 0);
}

.pay_wrap_box .scroll-item {
    display: block;
    height: 1.2em;
    line-height: 1.2em;
    text-align: center;
    width: 100%;
    /* 防止文字模糊 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.pay_wrap_box .scroll-item.current {
    position: relative;
    top: 0;
    margin: 0;
}

.pay_wrap_box .scroll-item.next {
    position: absolute;
    top: 1.2em; /* 使用具体的高度值，确保完全显示 */
    left: 0;
    width: 100%;
    margin: 0;
}

/* 防止动画期间的闪烁 */
.pay_wrap_box .scroll[data-v-6d27db42] * {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    color: #666;
    animation-delay: 2s
}

/* 旧的动画关键帧已移除，使用新的transform动画 */

@media (min-width: 1200px) {
    .none[data-v-6d27db42] {
        display:none
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .none[data-v-6d27db42] {
        display:none
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .none[data-v-6d27db42] {
        display:none!important
    }
}

.buy_btn[data-v-bf9bd6de] {
    position: absolute;
    cursor: pointer;
    z-index: 99;
    box-shadow: .10667rem .10667rem .42667rem #ac8b8a;
    bottom: var(--3544effe);
    left: 10%;
    width: 80%;
    background: #e03a2e;
    color: #fff;
    font-size: 0.512rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: .16rem;
    margin: auto;
    padding: 0.256rem 0
}

.buy_btn i[data-v-bf9bd6de] {
    font-size: 0.85328rem;
    font-weight: 700;
    line-height: 1.5
}

.buy_btn i[data-v-bf9bd6de]:last-child {
    font-size: 0.512rem
}

@media (min-width: 1200px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}

@media (min-width: 480px) and (max-width: 767px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}
</style>
<script>
    $("#my_pop_shadow").click(function () {
        $(this).hide();
        $("#my_pop_box").removeClass('u-slide-up-enter-active').slideUp();
    })
    
    $(".unlock_full_btn").click(function () {
        $("#my_pop_box").addClass('u-slide-up-enter-active').slideDown();
        $("#my_pop_shadow").show();
    })
    var price = 10;
    var order_type = 3;

    function updatOrder(e) {
        var channel = e ? 'alipay' : 'wxpay';
        var url = "/mbti/pay/submitpayqx";
        var $sn = '<?php echo $row['sn']; ?>';
        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&order_type=' + order_type+'&is_two_pay=1'+ '&qt_type='+currentReportType;
        return false;
    }
    function updatOldOrder(e) {
        var channel = e ? 'alipay' : 'wechat';
        var url = "/addons/epay/mbti/submitpay";
        var $sn = '<?php echo $row['sn']; ?>';
        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&order_type=' + order_type+'&is_two_pay=1'+ '&qt_type='+currentReportType;
        return false;
    }
    localStorage.setItem("order_sn_result", '<?php echo $row['sn']; ?>');
    
</script>
</body>

</html>