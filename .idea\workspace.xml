<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3b7de00c-0399-40d9-be13-fba730e19b64" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" doNotAsk="true" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/thinkphp/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2v57mFYCM5DI7mIXFmYOmP6U8Hh" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/PHPCUSTOM/wwwroot/web1.mbti366.com&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.webide.settings.project.settings.php&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="F:\PHPCUSTOM\wwwroot\web1.mbti366.com\application\mbti\view" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3b7de00c-0399-40d9-be13-fba730e19b64" name="Changes" comment="" />
      <created>1743423237485</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743423237485</updated>
      <workItem from="1743423238632" duration="12279000" />
      <workItem from="1743950392982" duration="20124000" />
      <workItem from="1744241247022" duration="9349000" />
      <workItem from="1745497940489" duration="3409000" />
      <workItem from="1745501725852" duration="9360000" />
      <workItem from="1746533619670" duration="6503000" />
      <workItem from="1748961607305" duration="2016000" />
      <workItem from="1749558857590" duration="11265000" />
      <workItem from="1749729817094" duration="20884000" />
      <workItem from="1750083331639" duration="15691000" />
      <workItem from="1750506076333" duration="706000" />
      <workItem from="1750857113978" duration="27000" />
      <workItem from="1750858637534" duration="25010000" />
      <workItem from="1751111266859" duration="14067000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>