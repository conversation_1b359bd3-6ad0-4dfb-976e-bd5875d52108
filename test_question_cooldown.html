<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
    <title>答题点击间隔测试</title>
    <script src="/Public/layer/jquery.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 30px;
            font-weight: bold;
        }
        
        .question-container {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .question-title {
            font-size: 18px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .question-type__single-label {
            position: relative;
            display: flex;
            padding: 15px 15px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            color: #333;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }
        
        .question-type__single-label:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .question-type__single-label.is-active {
            background: #4CAF50 !important;
            color: white !important;
            transform: scale(1.02);
        }
        

        
        .option-text {
            flex: 1;
            font-size: 16px;
            font-weight: 500;
        }
        
        .progress-container {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #4CAF50;
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }
        
        .status-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .status-label {
            font-weight: bold;
        }
        
        .status-value {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .cooldown-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
            display: none;
            z-index: 1000;
        }
        
        .cooldown-indicator.active {
            display: block;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="cooldown-indicator" id="cooldownIndicator">
        冷却中... <span id="cooldownTimer">1</span>s
    </div>
    
    <div class="test-container">
        <div class="title">答题点击间隔测试</div>
        
        <div class="progress-container">
            <div>题目进度: <span id="currentQuestion">1</span> / <span id="totalQuestions">5</span></div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <div class="status-info">
            <div class="status-row">
                <span class="status-label">答题状态:</span>
                <span class="status-value" id="answerStatus">就绪</span>
            </div>
            <div class="status-row">
                <span class="status-label">冷却时间:</span>
                <span class="status-value">1000ms</span>
            </div>
            <div class="status-row">
                <span class="status-label">连续点击:</span>
                <span class="status-value" id="clickCount">0</span>
            </div>
            <div class="status-row">
                <span class="status-label">有效点击:</span>
                <span class="status-value" id="validClickCount">0</span>
            </div>
        </div>
        
        <div class="question-container" id="questionContainer">
            <div class="question-title" id="questionTitle">
                第1题: 你更喜欢哪种活动方式？
            </div>
            
            <div class="question-type__single-label" data-index="1" data-answer="A">
                <div class="option-text">A. 和朋友一起参加聚会</div>
            </div>
            
            <div class="question-type__single-label" data-index="1" data-answer="B">
                <div class="option-text">B. 独自在家看书或思考</div>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="resetTest()">重置测试</button>
            <button class="btn" onclick="simulateRapidClick()">模拟快速点击</button>
            <button class="btn" onclick="showStats()">查看统计</button>
        </div>
        
        <div class="info-box">
            <h4>测试说明：</h4>
            <ul>
                <li>点击选项后自动渲染进度条</li>
                <li>进度渲染期间无法点击其他选项</li>
                <li>渲染完成后自动显示下一题</li>
                <li>连续快速点击会被自然阻止</li>
                <li>整个渲染过程约1秒钟</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟MBTI测试的变量
        var index = 1;
        var tcount = 5;
        var select_answer = {};
        var isAnswering = false;
        var clickCooldown = 1000;
        var totalClicks = 0;
        var validClicks = 0;
        
        // 题目数据
        var questions = [
            "第1题: 你更喜欢哪种活动方式？",
            "第2题: 在做决定时，你更依赖什么？",
            "第3题: 你如何处理压力？",
            "第4题: 你更喜欢什么样的工作环境？",
            "第5题: 你如何看待变化？"
        ];
        
        var options = [
            ["A. 和朋友一起参加聚会", "B. 独自在家看书或思考"],
            ["A. 逻辑分析和事实", "B. 直觉和感受"],
            ["A. 制定详细计划", "B. 随机应变"],
            ["A. 结构化和有序的", "B. 灵活和自由的"],
            ["A. 谨慎对待，需要时间适应", "B. 欢迎变化，享受新体验"]
        ];

        $(function () {
            $(".question-type__single-label").click(function () {
                totalClicks++;
                updateClickStats();

                // 检查是否在答题渲染期间
                if (isAnswering) {
                    console.log('渲染中，请稍等...');
                    return false;
                }

                validClicks++;
                updateClickStats();

                // 设置答题状态为true，防止连续点击
                isAnswering = true;
                updateAnswerStatus('渲染进度中');

                $(this).addClass('is-active').siblings().removeClass('is-active');
                index = $(this).data('index') * 1;
                select_answer[index] = $(this).data('answer');

                if (index == tcount) {
                    // 最后一题渲染完成后结束测试
                    setTimeout(function() {
                        alert('测试完成！');
                        resetTest();
                    }, 1000);
                    return;
                }

                console.log('选择答案:', $(this).data('answer'));
                index++;

                // 更新进度条渲染
                updateProgress();

                // 进度渲染完成后显示下一题
                setTimeout(function (){
                    showNextQuestion();
                    updateAnswerStatus('就绪');

                    // 下一题显示完成后重新允许点击
                    isAnswering = false;

                }, 1000); // 1秒渲染时间
            });
        });
        
        function updateProgress() {
            const progress = ((index - 1) / tcount * 100).toFixed(2);
            $('#progressFill').css('width', progress + '%');
            $('#currentQuestion').text(index);
        }
        
        function showNextQuestion() {
            if (index <= tcount) {
                $('#questionTitle').text(questions[index - 1]);
                const currentOptions = options[index - 1];
                
                $('.question-type__single-label').each(function(i) {
                    $(this).find('.option-text').text(currentOptions[i]);
                    $(this).data('index', index);
                    $(this).removeClass('is-active');
                });
            }
        }
        
        function updateAnswerStatus(status) {
            $('#answerStatus').text(status);
        }
        
        function updateClickStats() {
            $('#clickCount').text(totalClicks);
            $('#validClickCount').text(validClicks);
        }
        
        function showCooldownIndicator() {
            const indicator = $('#cooldownIndicator');
            indicator.addClass('active');
            
            let countdown = Math.ceil(clickCooldown / 1000);
            const timer = setInterval(() => {
                $('#cooldownTimer').text(countdown);
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                }
            }, 1000);
        }
        
        function hideCooldownIndicator() {
            $('#cooldownIndicator').removeClass('active');
        }
        
        function showCooldownMessage() {
            // 可以添加更多视觉反馈
            console.log('冷却中，请稍等...');
        }
        
        function resetTest() {
            index = 1;
            select_answer = {};
            isAnswering = false;
            totalClicks = 0;
            validClicks = 0;

            updateProgress();
            updateAnswerStatus('就绪');
            updateClickStats();

            $('#questionTitle').text(questions[0]);
            $('.question-type__single-label').each(function(i) {
                $(this).find('.option-text').text(options[0][i]);
                $(this).data('index', 1);
                $(this).removeClass('is-active');
            });
        }
        
        function simulateRapidClick() {
            // 模拟快速连续点击
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    $('.question-type__single-label').first().click();
                }, i * 100);
            }
        }
        
        function showStats() {
            const efficiency = totalClicks > 0 ? (validClicks / totalClicks * 100).toFixed(1) : 0;
            alert(`点击统计:\n总点击: ${totalClicks}\n有效点击: ${validClicks}\n点击效率: ${efficiency}%`);
        }
    </script>
</body>
</html>
