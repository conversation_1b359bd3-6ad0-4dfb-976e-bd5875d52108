<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
    <title>流畅计数器测试</title>
    <script src="/Public/layer/jquery.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .title {
            text-align: center;
            font-size: 24px;
            margin-bottom: 30px;
            font-weight: bold;
        }
        
        .counter-display {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .center {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            font-size: 28px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .buynumshow {
            color: #4CAF50;
            font-weight: bold;
            font-size: 32px;
        }
        
        .scroll {
            display: flex;
            align-items: center;
            gap: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            margin: 0 8px;
        }
        
        .scroll1, .scroll2 {
            position: relative;
            display: inline-block;
            overflow: hidden;
            height: 1.2em;
            line-height: 1.2em;
            vertical-align: top;
            width: 1em;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            color: #333;
            border-radius: 4px;
            font-weight: bold;
            font-size: 24px;
            /* 启用硬件加速 */
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }
        
        .scroll-wrapper {
            position: relative;
            height: 100%;
            width: 100%;
            /* 使用transform3d启用硬件加速 */
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
            -webkit-transition: -webkit-transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
            transition: transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
            /* 优化渲染性能 */
            will-change: transform;
        }
        
        .scroll-wrapper.scrolling {
            -webkit-transform: translate3d(0, -100%, 0);
            transform: translate3d(0, -100%, 0);
        }
        
        .scroll-item {
            display: block;
            height: 1.2em;
            line-height: 1.2em;
            text-align: center;
            width: 100%;
            /* 防止文字模糊 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .scroll-item.current {
            position: relative;
            top: 0;
        }
        
        .scroll-item.next {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
        }
        
        /* 防止动画期间的闪烁 */
        .scroll1 *, .scroll2 * {
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }
        
        .status-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .status-label {
            font-weight: bold;
        }
        
        .status-value {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s;
            font-size: 14px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-item {
            font-size: 12px;
            margin-bottom: 5px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        
        .log-time {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .animation-indicator {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 10px;
            height: 10px;
            background: #ff4444;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .animation-indicator.active {
            opacity: 1;
            animation: pulse 0.8s ease-in-out;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.5); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="title">流畅计数器效果测试</div>
        
        <div class="counter-display">
            <div class="center">
                已有<span class="buynumshow">338997</span>
                <div class="scroll">=
                    <div class="scroll1" style="position: relative;">
                        <div class="animation-indicator"></div>
                        <div class="scroll-item">0</div>
                    </div>
                    <div class="scroll2" style="position: relative;">
                        <div class="animation-indicator"></div>
                        <div class="scroll-item">1</div>
                    </div>
                </div>人购买
            </div>
        </div>
        
        <div class="status-info">
            <div class="status-row">
                <span class="status-label">本地存储总数:</span>
                <span class="status-value" id="totalCount">3389970</span>
            </div>
            <div class="status-row">
                <span class="status-label">显示数字:</span>
                <span class="status-value" id="displayNumber">338997</span>
            </div>
            <div class="status-row">
                <span class="status-label">scroll1:</span>
                <span class="status-value" id="scroll1Display">0</span>
            </div>
            <div class="status-row">
                <span class="status-label">scroll2:</span>
                <span class="status-value" id="scroll2Display">1</span>
            </div>
            <div class="status-row">
                <span class="status-label">动画状态:</span>
                <span class="status-value" id="animationStatus">空闲</span>
            </div>
            <div class="status-row">
                <span class="status-label">运行状态:</span>
                <span class="status-value" id="runningStatus">准备中</span>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="manualUpdate()" id="manualBtn">手动更新</button>
            <button class="btn" onclick="toggleTimer()">暂停/恢复</button>
            <button class="btn" onclick="resetCounter()">重置计数器</button>
            <button class="btn" onclick="clearLogs()">清空日志</button>
        </div>
        
        <div class="info-box">
            <h4>流畅计数器特性：</h4>
            <ul>
                <li><strong>防止跳跃</strong>：动画期间阻止新的更新</li>
                <li><strong>自然滚动</strong>：使用transform实现流畅的滚动效果</li>
                <li><strong>硬件加速</strong>：启用GPU加速提升性能</li>
                <li><strong>步长控制</strong>：每次随机增加1或2</li>
                <li><strong>状态管理</strong>：完整的动画状态跟踪</li>
            </ul>
        </div>
        
        <div class="log-container">
            <h4>更新日志：</h4>
            <div id="logContent"></div>
        </div>
    </div>

    <script>
        // 购买人数本地存储和滚动数字功能
        let buyUserCount = parseInt(localStorage.getItem('buyUserCountTwoPay') || 3389970);
        let currentScroll1 = buyUserCount % 10; // 当前显示的个位数
        let currentScroll2 = (currentScroll1 + 1) % 10; // 当前显示的下一个数字
        let scrollTimer = null;
        let isAnimating = false; // 动画状态标志
        let updateCount = 0;

        // 初始化显示
        function initializeBuyCount() {
            // 显示十位数开始到最大位数（不显示个位数）
            const displayNumber = Math.floor(buyUserCount / 10);
            $('.buynumshow').text(displayNumber);

            // 初始化滚动数字的HTML结构
            initializeScrollStructure();

            // 更新状态显示
            updateStatusDisplay();

            addLog('初始化', `总数: ${buyUserCount}, 显示: ${displayNumber}, scroll1: ${currentScroll1}, scroll2: ${currentScroll2}`);
        }

        // 初始化滚动数字的HTML结构
        function initializeScrollStructure() {
            $('.scroll1').html(`<div class="scroll-item">${currentScroll1}</div>`);
            $('.scroll2').html(`<div class="scroll-item">${currentScroll2}</div>`);
        }

        // 自然的滚动数字更新
        function updateScrollNumbers() {
            if (isAnimating) {
                addLog('跳过', '动画进行中，跳过本次更新');
                return; // 如果正在动画中，跳过这次更新
            }

            isAnimating = true;
            $('#animationStatus').text('动画中').css('color', '#ff9800');
            $('#manualBtn').prop('disabled', true);

            // 显示动画指示器
            $('.animation-indicator').addClass('active');

            // 随机增加1或2
            const increment = Math.random() < 0.5 ? 1 : 2;

            // 计算新的数值
            const oldCount = buyUserCount;
            const newBuyUserCount = buyUserCount + increment;
            const newScroll1 = newBuyUserCount % 10;
            const newScroll2 = (newScroll1 + 1) % 10;

            // 更新显示的总数（十位数开始）
            const newDisplayNumber = Math.floor(newBuyUserCount / 10);
            $('.buynumshow').text(newDisplayNumber);

            // 准备新的HTML结构用于动画
            const scroll1NewHtml = `
                <div class="scroll-wrapper">
                    <div class="scroll-item current">${currentScroll1}</div>
                    <div class="scroll-item next">${newScroll1}</div>
                </div>
            `;

            const scroll2NewHtml = `
                <div class="scroll-wrapper">
                    <div class="scroll-item current">${currentScroll2}</div>
                    <div class="scroll-item next">${newScroll2}</div>
                </div>
            `;

            // 更新HTML结构
            $('.scroll1').html(scroll1NewHtml);
            $('.scroll2').html(scroll2NewHtml);

            // 强制重排，确保DOM更新
            $('.scroll1')[0].offsetHeight;
            $('.scroll2')[0].offsetHeight;

            // 使用双重requestAnimationFrame确保动画流畅
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    // 触发滚动动画
                    $('.scroll1 .scroll-wrapper').addClass('scrolling');
                    $('.scroll2 .scroll-wrapper').addClass('scrolling');
                });
            });

            // 记录更新开始
            updateCount++;
            addLog('开始更新', `+${increment}: ${oldCount}→${newBuyUserCount}, scroll1: ${currentScroll1}→${newScroll1}, scroll2: ${currentScroll2}→${newScroll2}`);

            // 动画完成后更新状态
            setTimeout(() => {
                // 更新本地存储和内部状态
                buyUserCount = newBuyUserCount;
                currentScroll1 = newScroll1;
                currentScroll2 = newScroll2;
                localStorage.setItem('buyUserCountTwoPay', buyUserCount);

                // 清理HTML，只保留最终数字
                $('.scroll1').html(`<div class="scroll-item">${currentScroll1}</div>`);
                $('.scroll2').html(`<div class="scroll-item">${currentScroll2}</div>`);

                // 重置动画状态
                isAnimating = false;
                $('#animationStatus').text('空闲').css('color', '#4CAF50');
                $('#manualBtn').prop('disabled', false);

                // 隐藏动画指示器
                $('.animation-indicator').removeClass('active');

                // 更新状态显示
                updateStatusDisplay();

                addLog('完成更新', `动画完成，当前状态: ${buyUserCount}`);

                console.log('购买人数更新完成:', buyUserCount, '显示数字:', newDisplayNumber, 'scroll1:', currentScroll1, 'scroll2:', currentScroll2);
            }, 800); // 动画持续时间
        }

        // 更新状态显示
        function updateStatusDisplay() {
            const displayNumber = Math.floor(buyUserCount / 10);
            $('#totalCount').text(buyUserCount);
            $('#displayNumber').text(displayNumber);
            $('#scroll1Display').text(currentScroll1);
            $('#scroll2Display').text(currentScroll2);
        }

        // 添加日志
        function addLog(type, message) {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logHtml = `
                <div class="log-item">
                    <span class="log-time">[${timeStr}]</span>
                    <strong>${type}:</strong> ${message}
                </div>
            `;
            $('#logContent').prepend(logHtml);

            // 限制日志数量
            const logs = $('#logContent .log-item');
            if (logs.length > 30) {
                logs.slice(30).remove();
            }
        }

        // 启动定时器
        function startScrollTimer() {
            if (scrollTimer) {
                clearInterval(scrollTimer);
            }

            scrollTimer = setInterval(function() {
                updateScrollNumbers();
            }, 2200); // 每2.2秒更新一次

            $('#runningStatus').text('运行中').css('color', '#4CAF50');
        }

        // 停止定时器
        function stopScrollTimer() {
            if (scrollTimer) {
                clearInterval(scrollTimer);
                scrollTimer = null;
            }
            $('#runningStatus').text('已暂停').css('color', '#ff9800');
        }

        // 手动更新
        function manualUpdate() {
            updateScrollNumbers();
        }

        // 切换定时器
        function toggleTimer() {
            if (scrollTimer) {
                stopScrollTimer();
                addLog('操作', '定时器已暂停');
            } else {
                startScrollTimer();
                addLog('操作', '定时器已启动');
            }
        }

        // 重置计数器
        function resetCounter() {
            if (isAnimating) {
                addLog('错误', '动画进行中，无法重置');
                return;
            }

            buyUserCount = 3389970;
            currentScroll1 = buyUserCount % 10;
            currentScroll2 = (currentScroll1 + 1) % 10;
            localStorage.setItem('buyUserCountTwoPay', buyUserCount);
            updateCount = 0;

            initializeBuyCount();
            addLog('操作', '计数器已重置到初始值');
        }

        // 清空日志
        function clearLogs() {
            $('#logContent').empty();
            addLog('操作', '日志已清空');
        }

        // 页面加载完成后初始化
        $(document).ready(function() {
            // 立即初始化显示
            initializeBuyCount();
            addLog('系统', '页面加载完成，计数器将在2秒后启动');

            // 显示倒计时
            let countdown = 2;
            $('#runningStatus').text(`启动倒计时: ${countdown}s`).css('color', '#ff9800');

            const countdownTimer = setInterval(function() {
                countdown--;
                if (countdown > 0) {
                    $('#runningStatus').text(`启动倒计时: ${countdown}s`);
                } else {
                    clearInterval(countdownTimer);
                    $('#runningStatus').text('启动中...').css('color', '#2196F3');
                }
            }, 1000);

            // 延迟2秒后启动计数器
            setTimeout(function() {
                startScrollTimer();
                addLog('系统', '计数器已启动，开始自动更新');
            }, 2000);
        });

        // 页面卸载时停止定时器
        $(window).on('beforeunload', function() {
            stopScrollTimer();
        });
    </script>
</body>
</html>
