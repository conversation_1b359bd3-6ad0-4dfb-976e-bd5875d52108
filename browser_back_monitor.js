/**
 * 浏览器返回监控脚本
 * 当用户点击浏览器返回按钮时，弹出优惠弹窗并阻止返回
 */

$(document).ready(function() {
    // 浏览器返回监控配置
    var backMonitorConfig = {
        dialogId: 'huodongyouhui_dialog', // 弹窗元素ID
        enableKeyboardBlock: true, // 是否阻止ESC键
        enableBeforeUnload: true, // 是否启用页面卸载提示
        debug: false // 调试模式
    };
    
    // 初始化浏览器返回监控
    function initBrowserBackMonitor() {
        // 检查浏览器是否支持History API
        if (!window.history || !window.history.pushState) {
            console.warn('浏览器不支持History API，无法监控返回操作');
            return;
        }
        
        // 添加虚拟历史记录状态
        addVirtualHistoryState();
        
        // 监听popstate事件
        window.addEventListener('popstate', handlePopState);
        
        // 可选功能
        if (backMonitorConfig.enableKeyboardBlock) {
            addKeyboardListener();
        }
        
        if (backMonitorConfig.enableBeforeUnload) {
            addBeforeUnloadListener();
        }
        
        if (backMonitorConfig.debug) {
            console.log('浏览器返回监控已初始化');
        }
    }
    
    // 添加虚拟历史记录状态
    function addVirtualHistoryState() {
        try {
            // 添加一个标识状态，用于检测返回操作
            window.history.pushState({
                preventBack: true,
                timestamp: Date.now()
            }, null, window.location.href);
            
            if (backMonitorConfig.debug) {
                console.log('虚拟历史记录状态已添加');
            }
        } catch (e) {
            console.error('添加虚拟历史记录失败:', e);
        }
    }
    
    // 处理popstate事件
    function handlePopState(event) {
        if (backMonitorConfig.debug) {
            console.log('检测到popstate事件:', event.state);
        }
        
        // 检查是否是我们的虚拟状态或者返回操作
        if (!event.state || event.state.preventBack) {
            // 立即重新添加虚拟状态，阻止真正的返回
            addVirtualHistoryState();
            
            // 显示优惠弹窗
            showBackDialog();
        }
    }
    
    // 显示返回拦截弹窗
    function showBackDialog() {
        var dialogElement = $('#' + backMonitorConfig.dialogId);
        
        if (dialogElement.length === 0) {
            console.error('找不到弹窗元素:', backMonitorConfig.dialogId);
            return;
        }
        
        // 方法1：使用layer插件（如果可用）
        if (typeof layer !== 'undefined') {
            showLayerDialog(dialogElement);
        } 
        // 方法2：直接显示原有弹窗
        else {
            showNativeDialog(dialogElement);
        }
        
        if (backMonitorConfig.debug) {
            console.log('返回拦截弹窗已显示');
        }
    }
    
    // 使用layer插件显示弹窗
    function showLayerDialog(dialogElement) {
        var dialogContent = dialogElement.html();
        
        if (!dialogContent) {
            console.error('弹窗内容为空');
            return;
        }
        
        layer.open({
            type: 1,
            title: false,
            closeBtn: 0, // 不显示关闭按钮
            area: ['90%', 'auto'],
            maxWidth: '400px',
            skin: 'layui-layer-nobg layui-layer-rim',
            shadeClose: false, // 点击遮罩不关闭
            shade: [0.8, '#000'],
            content: dialogContent,
            success: function(layero, index) {
                // 弹窗显示成功后的回调
                bindDialogEvents(layero, index);
            },
            cancel: function(index) {
                // 阻止默认关闭行为
                return false;
            }
        });
    }
    
    // 直接显示原生弹窗
    function showNativeDialog(dialogElement) {
        // 显示弹窗
        dialogElement.show();
        
        // 添加遮罩层（如果不存在）
        if ($('.back-dialog-overlay').length === 0) {
            $('body').append('<div class="back-dialog-overlay" style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.8);z-index:9999;"></div>');
        }
        
        // 将弹窗置于最顶层
        dialogElement.css({
            'position': 'fixed',
            'z-index': '10000',
            'top': '50%',
            'left': '50%',
            'transform': 'translate(-50%, -50%)'
        });
        
        // 绑定事件
        bindNativeDialogEvents(dialogElement);
    }
    
    // 绑定layer弹窗事件
    function bindDialogEvents(layero, index) {
        // 为弹窗内的按钮添加点击事件
        layero.find('.dialog2_btn, .buy_btn, .close_btn').on('click', function() {
            var btnClass = $(this).attr('class');
            
            if (backMonitorConfig.debug) {
                console.log('用户点击了按钮:', btnClass);
            }
            
            // 根据按钮类型执行不同操作
            if (btnClass.includes('buy') || btnClass.includes('dialog2_btn')) {
                // 购买按钮 - 可以在这里添加购买逻辑
                handlePurchaseClick();
            }
            
            // 关闭弹窗
            layer.close(index);
        });
        
        // 阻止弹窗内容的点击事件冒泡
        layero.find('.layui-layer-content').on('click', function(e) {
            e.stopPropagation();
        });
    }
    
    // 绑定原生弹窗事件
    function bindNativeDialogEvents(dialogElement) {
        // 移除之前的事件监听器，避免重复绑定
        dialogElement.off('click.backDialog');
        
        // 为弹窗内的按钮添加点击事件
        dialogElement.on('click.backDialog', '.dialog2_btn, .buy_btn, .close_btn', function(e) {
            e.stopPropagation();
            
            var btnClass = $(this).attr('class');
            
            if (backMonitorConfig.debug) {
                console.log('用户点击了按钮:', btnClass);
            }
            
            // 根据按钮类型执行不同操作
            if (btnClass.includes('buy') || btnClass.includes('dialog2_btn')) {
                handlePurchaseClick();
            }
            
            // 关闭弹窗
            closeNativeDialog(dialogElement);
        });
        
        // 点击遮罩层关闭弹窗（可选）
        $('.back-dialog-overlay').off('click.backDialog').on('click.backDialog', function() {
            closeNativeDialog(dialogElement);
        });
    }
    
    // 关闭原生弹窗
    function closeNativeDialog(dialogElement) {
        dialogElement.hide();
        $('.back-dialog-overlay').remove();
    }
    
    // 处理购买按钮点击
    function handlePurchaseClick() {
        // 这里可以添加购买相关的逻辑
        // 例如：跳转到支付页面、显示支付弹窗等
        
        if (backMonitorConfig.debug) {
            console.log('执行购买逻辑');
        }
        
        // 示例：触发现有的购买按钮点击事件
        if ($('.buy_btn').length > 0) {
            $('.buy_btn').first().trigger('click');
        }
    }
    
    // 添加键盘监听器
    function addKeyboardListener() {
        $(document).on('keydown.backMonitor', function(e) {
            // 阻止ESC键
            if (e.keyCode === 27) {
                e.preventDefault();
                showBackDialog();
                
                if (backMonitorConfig.debug) {
                    console.log('阻止了ESC键操作');
                }
            }
            
            // 可选：阻止F5刷新键
            if (e.keyCode === 116) {
                e.preventDefault();
                showBackDialog();
                
                if (backMonitorConfig.debug) {
                    console.log('阻止了F5刷新操作');
                }
            }
        });
    }
    
    // 添加页面卸载监听器
    function addBeforeUnloadListener() {
        window.addEventListener('beforeunload', function(e) {
            // 现代浏览器会显示通用的确认对话框
            var message = '确定要离开吗？您可能会错过限时优惠！';
            e.preventDefault();
            e.returnValue = message;
            return message;
        });
    }
    
    // 销毁监控器
    function destroyBrowserBackMonitor() {
        // 移除事件监听器
        window.removeEventListener('popstate', handlePopState);
        $(document).off('keydown.backMonitor');
        
        // 清理弹窗
        $('.back-dialog-overlay').remove();
        
        if (backMonitorConfig.debug) {
            console.log('浏览器返回监控已销毁');
        }
    }
    
    // 公开API
    window.BrowserBackMonitor = {
        init: initBrowserBackMonitor,
        destroy: destroyBrowserBackMonitor,
        showDialog: showBackDialog,
        config: backMonitorConfig
    };
    
    // 自动初始化
    initBrowserBackMonitor();
});
