<div id="my_pop_shadow"></div>
<uni-view id="my_pop_box">
    <div data-v-6d27db42="" data-v-c3c1a80a="" class="pay_wrap_box none">

        <div data-v-6d27db42="" class="pay_type pay_type2">
            <ul data-v-6d27db42="" class="ul">
                {if condition="$row.qt_type == 2" }
                <li data-v-6d27db42="" class="active" data-qt-type="2">
                    <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整报告</p>
                    <p data-v-6d27db42=""> <span class="full_price">¥10</span> <del data-v-6d27db42="">¥99</del></p>
                    <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">在简要报告的基础上，增加了性格优劣势详细分析，成长建议与成就以及荣格八维的解读，真正有深度的性格剖析</p>
                    <div data-v-6d27db42="" class="tuijian">92%的人选择</div>
                   
                </li>
                {/if}

                <li data-v-6d27db42="" class="" data-qt-type="3">
                    <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整解读Pro</p>
                    <p data-v-6d27db42=""> <span class="pro_price">¥{$pro_diff_price}</span> <del data-v-6d27db42="">¥699</del></p>
                    <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">MBTI十六型人格完整解读报告，解锁12个大类，45个子类全部报告内容，自我认知，人际交往，职业发展，恋爱交友等维度深度剖析，详实呈现</p>
                    <!---->
                    <div data-v-6d27db42="" class="tuijian-today">仅限今日</div>
                </li>
            </ul>
            <div data-v-6d27db42="" class="btn">
                <div data-v-6d27db42="" class="pay-btn-wrapper width-100">
                    <button data-v-6d27db42="" onclick="updatOrder(0)" class="pay-button van-button van-button--primary van-button--small van-button--block van-button--round">
                        <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-wechat-pay van-button__icon">
                                <!---->
                            </i><span data-v-6d27db42="" class="van-button__text">微信支付</span></div>
                    </button>
                    {if !$is_weixin_browser}
                    <button data-v-6d27db42="" onclick="updatOldOrder(1)" class="pay-button van-button van-button--info van-button--small van-button--block van-button--round">
                        <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-alipay van-button__icon">
                                <!---->
                            </i><span data-v-6d27db42="" class="van-button__text">支付宝支付</span></div>
                    </button>
                    {/if}
                </div>
                <div data-v-6d27db42="" class="center"> 已有<span class="buynumshow">238997</span>
                  <div data-v-6d27db42="" class="scroll">=<div class="scroll-item">0</div></div>人购买
                  </div>

                <div data-v-6d27db42="" class="center quanli">付费解锁后，您可以享受以下权利</div>
                <div data-v-6d27db42="" class="quanli_item">
                    <ul data-v-6d27db42="" class="item item2">
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI类型</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">基础解读</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI字母详解</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">报告永久保存</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">完整解读</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">同类型占比</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">价值观分析</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">性格解析-优劣势</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">成长建议</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">荣格八维性格解读</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">不同阶段的你</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职场避雷锦囊</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">团队中的你</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职业参考宝典</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">恋爱状态</span></li>
                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">最佳恋爱匹配类型</span></li>
                    </ul>
                </div>
                <div data-v-6d27db42="" class="renzheng"><img data-v-6d27db42="" src="https://ggbondtech.com/rz.png" alt=""></div>
                <div data-v-6d27db42="" class="lv">支付系统已经过安全联盟认证请放心使用</div>
            </div>
        </div>
    </div>
</uni-view>
<link rel="stylesheet" href="/Public/mbit/result2_files/mbtijg.css">
<script>
    {if condition="$row.qt_type == 2" }
        var currentReportType = 2; // 默认选中第二个报告
    {else /}
    var currentReportType = 3; // 默认选中第二个报告
    {/if}
    
    // 为报告选项添加点击事件
  $('.pay_type2 ul li').on('click', function() {
    // 移除所有active类
    $('.pay_type2 ul li').removeClass('active');
    // 为当前点击项添加active类
    let cindex = $(this).index();
    console.log(cindex);
    // 为当前点击项和对应位置的另一项添加active类
 
    $('.pay_type2 ul li').eq(cindex).addClass('active');
    
    // 获取当前选中的报告类型(1,2,3)
    currentReportType = $(this).data('qt-type');
   
    
    
    // 根据选中的报告类型更新权限显示
    updateReportPermissions();
  });

  // 更新报告权限显示的函数
  function updateReportPermissions() {
    // 分别获取两个.item容器下的权限项
    const $permissionItems2 = $('.item2 li');
 
    
    // 处理第一个.item容器
    applyPermissionLogic($permissionItems2);
    

    
    function applyPermissionLogic($items) {
      // 根据当前选中的报告类型设置权限显示
      if (currentReportType === 1) {
        // 简要报告 - 从第5个开始变为del
        $items.each(function(index) {
          const $span = $(this).find('span');
          const $del = $(this).find('del');
          
          if (index >= 4) {
            if ($span.length) {
              const attrs = $span.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $span.replaceWith(`<del${attrStr}>` + $span.text() + '</del>');
            }
          } else {
            if ($del.length) {
              const attrs = $del.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
            }
          }
        });
      } else if (currentReportType === 2) {
        // 完整报告 - 只有最后两个变为del
        $items.each(function(index) {
          const $span = $(this).find('span');
          const $del = $(this).find('del');
          
          if (index >= $items.length - 2) {
            if ($span.length) {
              const attrs = $span.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $span.replaceWith(`<del${attrStr}>` + $span.text() + '</del>');
            } else if ($del.length === 0) {
              // 如果没有span但有内容，直接创建del标签
              const text = $(this).text().trim();
              if (text) {
                const $prevSpan = $(this).find('span');
                const attrs = $prevSpan.length ? $prevSpan.get(0).attributes : {};
                let attrStr = '';
                for (let i = 0; i < attrs.length; i++) {
                  attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
                }
                $(this).html(`<del${attrStr}>${text}</del>`);
              }
            }
          } else {
            if ($del.length) {
              const attrs = $del.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
            }
          }
        });
      } else if (currentReportType === 3) {
        // 完整解读Pro - 所有都是span
        $items.each(function() {
          const $del = $(this).find('del');
          if ($del.length) {
            const attrs = $del.get(0).attributes;
            let attrStr = '';
            for (let i = 0; i < attrs.length; i++) {
              attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
            }
            $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
          }
        });
      }
    }
  }

  // 初始化权限显示
  updateReportPermissions();

  // 购买人数本地存储和滚动数字功能
  let buyUserCount = parseInt(localStorage.getItem('buyUserCountTwoPay') || 3389970);
  let currentScroll1 = buyUserCount % 10; // 当前显示的个位数
  let currentScroll2 = (currentScroll1 + 1) % 10; // 当前显示的下一个数字
  let scrollTimer = null;
  let isAnimating = false; // 动画状态标志

  // 初始化显示
  function initializeBuyCount() {
    // 显示十位数开始到最大位数（不显示个位数）
    const displayNumber = Math.floor(buyUserCount / 10);
    $('.buynumshow').text(displayNumber);

    // 初始化滚动数字的HTML结构
    initializeScrollStructure();
  }

  // 初始化滚动数字的HTML结构
  function initializeScrollStructure() {
    // 在scroll容器中创建垂直轮播结构，保持原有的"="符号
    const scrollHtml = `
      =<div class="scroll-wrapper">
        <div class="scroll-item current">${currentScroll1}</div>
        <div class="scroll-item next">${currentScroll2}</div>
      </div>
    `;

    // 替换scroll容器的内容，保持"人购买"文字不变
    $('.scroll').html(scrollHtml);
  }

  // 自然的滚动数字更新
  function updateScrollNumbers() {
    if (isAnimating) {
      return; // 如果正在动画中，跳过这次更新
    }

    isAnimating = true;

    // 随机增加1或2
    const increment = Math.random() < 0.5 ? 1 : 2;

    // 计算新的数值
    const newBuyUserCount = buyUserCount + increment;
    const newScroll1 = newBuyUserCount % 10;
    const newScroll2 = (newScroll1 + 1) % 10;

    // 更新显示的总数（十位数开始）- 直接更新，不需要滚动动画
    const newDisplayNumber = Math.floor(newBuyUserCount / 10);
    $('.buynumshow').text(newDisplayNumber);

    // 准备个位数的HTML结构用于垂直轮播动画
    const scrollNewHtml = `
      =<div class="scroll-wrapper">
        <div class="scroll-item current">${currentScroll1}</div>
        <div class="scroll-item next">${newScroll1}</div>
      </div>
    `;

    // 更新HTML结构
    $('.scroll').html(scrollNewHtml);

    // 强制重排，确保DOM更新
    $('.scroll')[0].offsetHeight;

    // 使用双重requestAnimationFrame确保动画流畅
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        // 触发滚动动画
        $('.scroll .scroll-wrapper').addClass('scrolling');
      });
    });

    // 动画完成后更新状态
    setTimeout(() => {
      // 更新本地存储和内部状态
      buyUserCount = newBuyUserCount;
      currentScroll1 = newScroll1;
      currentScroll2 = newScroll2;
      localStorage.setItem('buyUserCountTwoPay', buyUserCount);

      // 清理HTML，只保留最终数字，保持"="符号
      $('.scroll').html(`=<div class="scroll-item">${currentScroll1}</div>`);

      // 重置动画状态
      isAnimating = false;

      console.log('购买人数更新完成:', buyUserCount, '显示数字:', newDisplayNumber, 'scroll1:', currentScroll1, 'scroll2:', currentScroll2);
    }, 800); // 动画持续时间
  }



  // 启动定时器
  function startScrollTimer() {
    if (scrollTimer) {
      clearInterval(scrollTimer);
    }

    scrollTimer = setInterval(function() {
      updateScrollNumbers();
    }, 2200); // 每1秒更新一次
  }

  // 停止定时器
  function stopScrollTimer() {
    if (scrollTimer) {
      clearInterval(scrollTimer);
      scrollTimer = null;
    }
  }

  // 页面加载完成后初始化
  $(document).ready(function() {
    // 立即初始化显示
    initializeBuyCount();

    // 延迟2秒后启动计数器
    setTimeout(function() {
      startScrollTimer();
    }, 2000);
  });

  // 页面卸载时停止定时器
 /*  $(window).on('beforeunload', function() {
    stopScrollTimer();
  }); */

</script>
<style>
    .van-button {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    height: 44px;
    margin: 0;
    padding: 0;
    font-size: 16px;
    line-height: 1.2;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
    -webkit-transition: opacity .2s;
    transition: opacity .2s;
    -webkit-appearance: none
}

.van-button:before {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background-color: #000;
    border: inherit;
    border-color: #000;
    border-radius: inherit;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    opacity: 0;
    content: " "
}

.van-button:active:before {
    opacity: .1
}

.van-button--disabled:before,.van-button--loading:before {
    display: none
}

.van-button--default {
    color: #323233;
    background-color: #fff;
    border: 1px solid #ebedf0
}

.van-button--primary {
    color: #fff;
    background-color: #07c160;
    border: 1px solid #07c160
}

.van-button--info {
    color: #fff;
    background-color: #1989fa;
    border: 1px solid #1989fa
}

.van-button--danger {
    color: #fff;
    background-color: #ee0a24;
    border: 1px solid #ee0a24
}

.van-button--warning {
    color: #fff;
    background-color: #ff976a;
    border: 1px solid #ff976a
}

.van-button--plain {
    background-color: #fff
}

.van-button--plain.van-button--primary {
    color: #07c160
}

.van-button--plain.van-button--info {
    color: #1989fa
}

.van-button--plain.van-button--danger {
    color: #ee0a24
}

.van-button--plain.van-button--warning {
    color: #ff976a
}

.van-button--large {
    width: 100%;
    height: 50px
}

.van-button--normal {
    padding: 0 15px;
    font-size: 14px
}

.van-button--small {
    height: 32px;
    padding: 0 8px;
    font-size: 12px
}

.van-button__loading {
    color: inherit;
    font-size: inherit
}

.van-button--mini {
    height: 24px;
    padding: 0 4px;
    font-size: 10px
}

.van-button--mini+.van-button--mini {
    margin-left: 4px
}

.van-button--block {
    display: block;
    width: 100%
}

.van-button--disabled {
    cursor: not-allowed;
    opacity: .5
}

.van-button--loading {
    cursor: default
}

.van-button--round {
    border-radius: 999px
}

.van-button--square {
    border-radius: 0
}

.van-button__content {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    height: 100%
}

.van-button__content:before {
    content: " "
}

.van-button__icon {
    font-size: 1.2em;
    line-height: inherit
}

.van-button__icon+.van-button__text,.van-button__loading+.van-button__text,.van-button__text+.van-button__icon,.van-button__text+.van-button__loading {
    margin-left: 4px
}

.van-button--hairline {
    border-width: 0
}

.van-button--hairline:after {
    border-color: inherit;
    border-radius: 4px
}

.van-button--hairline.van-button--round:after {
    border-radius: 999px
}

.van-button--hairline.van-button--square:after {
    border-radius: 0
}

.van-submit-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
    width: 100%;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff;
    -webkit-user-select: none;
    user-select: none
}

.van-submit-bar__tip {
    padding: 8px 12px;
    color: #f56723;
    font-size: 12px;
    line-height: 1.5;
    background-color: #fff7cc
}

.van-submit-bar__tip-icon {
    min-width: 18px;
    font-size: 12px;
    vertical-align: middle
}

.van-submit-bar__tip-text {
    vertical-align: middle
}

.van-submit-bar__bar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    height: 50px;
    padding: 0 16px;
    font-size: 14px
}

.van-submit-bar__text {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    padding-right: 12px;
    color: #323233;
    text-align: right
}

.van-submit-bar__text span {
    display: inline-block
}

.van-submit-bar__suffix-label {
    margin-left: 5px;
    font-weight: 500
}

.van-submit-bar__price {
    color: #ee0a24;
    font-weight: 500;
    font-size: 12px
}

.van-submit-bar__price--integer {
    font-size: 20px;
    font-family: Avenir-Heavy,PingFang SC,Helvetica Neue,Arial,sans-serif
}

.van-submit-bar__button {
    width: 110px;
    height: 40px;
    font-weight: 500;
    border: none
}

.van-submit-bar__button--danger {
    background: -webkit-linear-gradient(left,#ff6034,#ee0a24);
    background: linear-gradient(to right,#ff6034,#ee0a24)
}

.van-submit-bar--unfit {
    padding-bottom: 0
}

.van-goods-action-button {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    height: 40px;
    font-weight: 500;
    font-size: 14px;
    border: none;
    border-radius: 0
}

.van-goods-action-button--first {
    margin-left: 5px;
    border-top-left-radius: 999px;
    border-bottom-left-radius: 999px
}

.van-goods-action-button--last {
    margin-right: 5px;
    border-top-right-radius: 999px;
    border-bottom-right-radius: 999px
}

.van-goods-action-button--warning {
    background: -webkit-linear-gradient(left,#ffd01e,#ff8917);
    background: linear-gradient(to right,#ffd01e,#ff8917)
}

.van-goods-action-button--danger {
    background: -webkit-linear-gradient(left,#ff6034,#ee0a24);
    background: linear-gradient(to right,#ff6034,#ee0a24)
}

@media (max-width: 321px) {
    .van-goods-action-button {
        font-size:13px
    }
}

.van-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    box-sizing: content-box;
    width: 88px;
    max-width: 70%;
    min-height: 88px;
    padding: 16px;
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    white-space: pre-wrap;
    text-align: center;
    word-break: break-all;
    background-color: #000000b3;
    border-radius: 8px;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0)
}

.van-toast--unclickable {
    overflow: hidden
}

.van-toast--unclickable * {
    pointer-events: none
}

.van-toast--html,.van-toast--text {
    width: -webkit-fit-content;
    width: fit-content;
    min-width: 96px;
    min-height: 0;
    padding: 8px 12px
}

.van-toast--html .van-toast__text,.van-toast--text .van-toast__text {
    margin-top: 0
}

.van-toast--top {
    top: 20%
}

.van-toast--bottom {
    top: auto;
    bottom: 20%
}

.van-toast__icon {
    font-size: 36px
}

.van-toast__loading {
    padding: 4px;
    color: #fff
}

.van-toast__text {
    margin-top: 8px
}

.van-calendar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 100%;
    background-color: #fff
}

.van-calendar__popup.van-popup--bottom,.van-calendar__popup.van-popup--top {
    height: 80%
}

.van-calendar__popup.van-popup--left,.van-calendar__popup.van-popup--right {
    height: 100%
}

.van-calendar__popup .van-popup__close-icon {
    top: 11px
}

.van-calendar__header {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    box-shadow: 0 2px 10px #7d7e8029
}

.van-calendar__header-subtitle,.van-calendar__header-title,.van-calendar__month-title {
    height: 44px;
    font-weight: 500;
    line-height: 44px;
    text-align: center
}

.van-calendar__header-title {
    font-size: 16px
}

.van-calendar__header-subtitle,.van-calendar__month-title {
    font-size: 14px
}

.van-calendar__weekdays {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex
}

.van-calendar__weekday {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    font-size: 12px;
    line-height: 30px;
    text-align: center
}

.van-calendar__body {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.van-calendar__days {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-user-select: none;
    user-select: none
}

.van-calendar__month-mark {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 0;
    color: #f2f3f5cc;
    font-size: 160px;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    pointer-events: none
}

.van-calendar__day,.van-calendar__selected-day {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    text-align: center
}

.van-calendar__day {
    position: relative;
    width: 14.285%;
    height: 64px;
    font-size: 16px;
    cursor: pointer
}

.van-calendar__day--end,.van-calendar__day--multiple-middle,.van-calendar__day--multiple-selected,.van-calendar__day--start,.van-calendar__day--start-end {
    color: #fff;
    background-color: #ee0a24
}

.van-calendar__day--start {
    border-radius: 4px 0 0 4px
}

.van-calendar__day--end {
    border-radius: 0 4px 4px 0
}

.van-calendar__day--multiple-selected,.van-calendar__day--start-end {
    border-radius: 4px
}

.van-calendar__day--middle {
    color: #ee0a24
}

.van-calendar__day--middle:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: currentColor;
    opacity: .1;
    content: ""
}

.van-calendar__day--disabled {
    color: #c8c9cc;
    cursor: default
}

.van-calendar__bottom-info,.van-calendar__top-info {
    position: absolute;
    right: 0;
    left: 0;
    font-size: 10px;
    line-height: 14px
}

@media (max-width: 350px) {
    .van-calendar__bottom-info,.van-calendar__top-info {
        font-size:9px
    }
}

.van-calendar__top-info {
    top: 6px
}

.van-calendar__bottom-info {
    bottom: 6px
}

.van-calendar__selected-day {
    width: 54px;
    height: 54px;
    color: #fff;
    background-color: #ee0a24;
    border-radius: 4px
}

.van-calendar__footer {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    padding: 0 16px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom)
}

.van-calendar__footer--unfit {
    padding-bottom: 0
}

.van-calendar__confirm {
    height: 36px;
    margin: 7px 0
}

.van-picker {
    position: relative;
    background-color: #fff;
    -webkit-user-select: none;
    user-select: none
}

.van-picker__toolbar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    height: 44px
}

.van-picker__cancel,.van-picker__confirm {
    height: 100%;
    padding: 0 16px;
    font-size: 14px;
    background-color: transparent;
    border: none;
    cursor: pointer
}

.van-picker__cancel:active,.van-picker__confirm:active {
    opacity: .7
}

.van-picker__confirm {
    color: #576b95
}

.van-picker__cancel {
    color: #969799
}

.van-picker__title {
    max-width: 50%;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    text-align: center
}

.van-picker__columns {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    cursor: grab
}

.van-picker__loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    color: #1989fa;
    background-color: #ffffffe6
}

.van-picker__frame {
    position: absolute;
    top: 50%;
    right: 16px;
    left: 16px;
    z-index: 2;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    pointer-events: none
}

.van-picker__mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-image: -webkit-linear-gradient(top,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4)),-webkit-linear-gradient(bottom,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4));
    background-image: linear-gradient(180deg,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4)),linear-gradient(0deg,hsla(0,0%,100%,.9),hsla(0,0%,100%,.4));
    background-repeat: no-repeat;
    background-position: top,bottom;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    pointer-events: none
}

.van-picker-column {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    overflow: hidden;
    font-size: 16px
}

.van-picker-column__wrapper {
    -webkit-transition-timing-function: cubic-bezier(.23,1,.68,1);
    transition-timing-function: cubic-bezier(.23,1,.68,1)
}

.van-picker-column__item {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 0 4px;
    color: #000
}

.van-picker-column__item--disabled {
    cursor: not-allowed;
    opacity: .3
}

.van-action-sheet {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    max-height: 80%;
    overflow: hidden;
    color: #323233
}

.van-action-sheet__content {
    -webkit-box-flex: 1;
    -webkit-flex: 1 auto;
    flex: 1 auto;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch
}

.van-action-sheet__cancel,.van-action-sheet__item {
    display: block;
    width: 100%;
    padding: 14px 16px;
    font-size: 16px;
    background-color: #fff;
    border: none;
    cursor: pointer
}

.van-action-sheet__cancel:active,.van-action-sheet__item:active {
    background-color: #f2f3f5
}

.van-action-sheet__item {
    line-height: 22px
}

.van-action-sheet__item--disabled,.van-action-sheet__item--loading {
    color: #c8c9cc
}

.van-action-sheet__item--disabled:active,.van-action-sheet__item--loading:active {
    background-color: #fff
}

.van-action-sheet__item--disabled {
    cursor: not-allowed
}

.van-action-sheet__item--loading {
    cursor: default
}

.van-action-sheet__cancel {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    box-sizing: border-box;
    color: #646566
}

.van-action-sheet__subname {
    margin-top: 8px;
    color: #969799;
    font-size: 12px;
    line-height: 18px
}

.van-action-sheet__gap {
    display: block;
    height: 8px;
    background-color: #f7f8fa
}

.van-action-sheet__header {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    font-weight: 500;
    font-size: 16px;
    line-height: 48px;
    text-align: center
}

.van-action-sheet__description {
    position: relative;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    padding: 20px 16px;
    color: #969799;
    font-size: 14px;
    line-height: 20px;
    text-align: center
}

.van-action-sheet__description:after {
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    right: 16px;
    bottom: 0;
    left: 16px;
    border-bottom: 1px solid #ebedf0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.van-action-sheet__loading-icon .van-loading__spinner {
    width: 22px;
    height: 22px
}

.van-action-sheet__close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    padding: 0 16px;
    color: #c8c9cc;
    font-size: 22px;
    line-height: inherit
}

.van-action-sheet__close:active {
    color: #969799
}

.van-goods-action {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    box-sizing: content-box;
    height: 50px;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    background-color: #fff
}

.van-goods-action--unfit {
    padding-bottom: 0
}

.van-dialog {
    position: fixed;
    top: 45%;
    left: 50%;
    width: 320px;
    overflow: hidden;
    font-size: 16px;
    background-color: #fff;
    border-radius: 16px;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: .3s;
    transition: .3s;
    -webkit-transition-property: opacity,-webkit-transform;
    transition-property: opacity,-webkit-transform;
    transition-property: transform,opacity;
    transition-property: transform,opacity,-webkit-transform
}
    .pay_wrap_box[data-v-6d27db42] {
    padding: 0 .4rem;
    box-sizing: border-box;
    display: block
}

.pay_wrap_box .pay_t[data-v-6d27db42] {
    text-align: center
}

.pay_wrap_box .pay_t p[data-v-6d27db42] {
    margin: 0;
    line-height: 1.7;
    color: #41464b;
    font-size: 0.59728rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.pay_wrap_box .pay_t p span[data-v-6d27db42] {
    color: red
}

.pay_wrap_box .pay_t p .count[data-v-6d27db42] {
    font-size: 0.85328rem;
    margin: 0 .16rem
}

.pay_wrap_box .pay_t p .count[data-v-6d27db42] .van-count-down {
    color: red;
    font-size: 0.42672rem
}

.pay_wrap_box .pay_type2[data-v-6d27db42] {
    padding-top: 1.42672rem
}

.pay_wrap_box .pay_type ul.ul[data-v-6d27db42] {
    display: flex;
    justify-content: center;
}

.pay_wrap_box .pay_type ul.ul li[data-v-6d27db42] {
    border: .0133rem solid #d8d8d8;
    /* flex: 1; */
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.64rem 0.34128rem;
    box-sizing: border-box;
    position: relative;
    border-radius: .16rem;
    width: 33.33%;
    
}

.pay_wrap_box .pay_type ul.ul li .tuijian[data-v-6d27db42] {
    position: absolute;
    top: -.2667rem;
    height: .5333rem;
    background: linear-gradient(180deg,#b66af0,#7c32cb 98%);
    border-radius: 0 .1333rem;
    font-size: 0.512rem;
    color: #fff;
    padding: 0 .16rem;
    right: -.0133rem;
    display: flex;
    align-items: center
}

.pay_wrap_box .pay_type ul.ul li .tuijian-today[data-v-6d27db42] {
    position: absolute;
    top: -.2667rem;
    height: .5333rem;
    background: linear-gradient(180deg,#ff6006,#ff3306);
    border-radius: 0 .1333rem;
    font-size: 0.512rem;
    color: #fff;
    padding: 0 .16rem;
    right: -.0133rem;
    display: flex;
    align-items: center
}

.pay_wrap_box .pay_type ul.ul li[data-v-6d27db42]:nth-child(2) {
    margin: 0 .2667rem
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42] {
    margin: 0
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42]:first-child {
    color: #333;
    font-size: .4rem
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42]:nth-child(2) {
    margin: 0.42672rem 0;
    font-size: 0.68272rem;
    color: #333;
    display: flex;
    align-items: center
}

.pay_wrap_box .pay_type ul.ul li p:nth-child(2) del[data-v-6d27db42] {
    font-size: 0.512rem;
    color: #666;
    opacity: .5;
    margin-left: .1067rem
}

.pay_wrap_box .pay_type ul.ul li p[data-v-6d27db42]:nth-child(3) {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.512rem
}

.pay_wrap_box .pay_type .type1 .type1_jianyao[data-v-6d27db42] {
    background: #fef6f0;
    border: .0133rem solid #edbf79;
    border-radius: .1333rem;
    padding: 0.64rem 0.34128rem;
    box-sizing: border-box;
    text-align: center;
    color: #6c5229;
    font-size: .4rem;
    font-weight: 700;
    margin-bottom: 0.72528rem
}

.pay_wrap_box .pay_type .type1 .type1_jianyao .p2[data-v-6d27db42] {
    margin: 0.256rem 0;
    color: #999;
    font-size: 0.512rem;
    font-weight: 400
}

.pay_wrap_box .pay_type .type1 .type1_jiagou[data-v-6d27db42] {
    border: .0133rem solid #e1e0e0;
    border-radius: .1333rem;
    padding: .4rem;
    box-sizing: border-box;
    color: #666;
    font-size: 0.512rem;
    margin-bottom: 0.72528rem;
    position: relative
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .p1[data-v-6d27db42] {
    font-size: .4rem;
    font-weight: 700
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex[data-v-6d27db42] {
    display: flex
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex .jiagou_flex_l[data-v-6d27db42] {
    font-size: .24rem
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex .jiagou_flex_c[data-v-6d27db42] {
    white-space: nowrap;
    margin: 0 .2667rem;
    font-size: .4rem;
    color: #333;
    font-weight: 700
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .jiagou_flex .jiagou_flex_c .total_price[data-v-6d27db42] {
    white-space: nowrap;
    transform: scale(.8);
    font-weight: 400;
    font-size: .4rem;
    text-decoration: line-through;
    color: #b0b0b0;
    position: absolute
}

.pay_wrap_box .pay_type .type1 .type1_jiagou .chaozhijiagou_tag[data-v-6d27db42] {
    position: absolute;
    top: 0;
    left: 0;
    background: red;
    border-radius: .16rem 0;
    padding: .0267rem .16rem;
    font-size: 0.512rem;
    color: #fff
}

.pay_wrap_box .active[data-v-6d27db42] {
    border-color: #edbf79!important;
    background: #fef6f0
}

.pay_wrap_box .active p[data-v-6d27db42]:first-child {
    color: #6c5229!important
}

.pay_wrap_box .active p[data-v-6d27db42]:nth-child(2) {
    color: #6c5229!important;
    font-weight: 700
}

.pay_wrap_box .active p:nth-child(2) del[data-v-6d27db42] {
    font-weight: 400
}

.pay_wrap_box .btn[data-v-6d27db42] {
    margin-top: 0.42672rem
}

.pay_wrap_box .btn .pay-btn-wrapper[data-v-6d27db42] {
    display: flex
}
.width-100{
    width: 100%;
}
.pay_wrap_box .btn .pay-button[data-v-6d27db42] {
    margin-bottom: .2133rem
}

.pay_wrap_box .btn .pay-button+.pay-button[data-v-6d27db42] {
    margin-left: .4rem
}

.pay_wrap_box .btn .center[data-v-6d27db42] {
    text-align: center;
    font-size: 0.512rem;
    color: #666;
    margin-bottom: 0.42672rem;
    height: 20px;
}

.pay_wrap_box .btn .give_up_pay[data-v-6d27db42] {
    width: 100%;
    background: #9a999c;
    border-radius: 13.32rem;
    display: flex;
    box-sizing: border-box;
    padding: 0.29872rem 0;
    color: #fff;
    align-items: center;
    justify-content: center;
    margin-bottom: .24rem
}

.pay_wrap_box .btn .give_up_pay>div[data-v-6d27db42] {
    display: flex;
    align-items: center
}

.pay_wrap_box .btn .quanli[data-v-6d27db42] {
    color: #c58c34;
    font-weight: 500;
    font-size: 0.59728rem;
    margin-bottom: 0.42672rem
}

.pay_wrap_box .quanli_item .item[data-v-6d27db42] {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 .5333rem;
    height: auto;
    width: 346px;
}

.pay_wrap_box .quanli_item .item li[data-v-6d27db42] {
    display: flex;
    align-items: center;
    line-height: 1.6;
    color: #333;
    font-weight: 500;
    min-width: 5.97328rem;
    font-size: 0.512rem
}

.pay_wrap_box .quanli_item .item li span[data-v-6d27db42] {
    white-space: nowrap;
    font-size: 0.512rem
}

.pay_wrap_box .quanli_item .item li img[data-v-6d27db42] {
    width: .4267rem;
    height: .4267rem;
    margin-right: 0.128rem
}

.pay_wrap_box .quanli_item .item li del[data-v-6d27db42] {
    color: #ccc;
    text-decoration: line-through
}

.pay_wrap_box .renzheng[data-v-6d27db42] {
    display: flex;
    justify-content: center;
    margin-top: 0.42672rem
}

.pay_wrap_box .renzheng img[data-v-6d27db42] {
    height: 1.28rem
}

.pay_wrap_box .lv[data-v-6d27db42] {
    color: #5ec455;
    display: flex;
    justify-content: center;
    line-height: 3;
    font-weight: 500;
    font-size: 0.512rem
}

.pay_wrap_box .scroll[data-v-6d27db42] {
    position: relative;
    display: inline-block;
    color: #fff;
    height: 1em; /* 与文字高度一致 */
    width: 1em;
    overflow: hidden; /* 隐藏超出部分，实现垂直滚动效果 */
    vertical-align: middle; /* 与文字中线对齐 */
    line-height: 1; /* 确保行高一致 */
}

/* scroll1和scroll2样式已移除，使用统一的scroll容器 */

.pay_wrap_box .scroll-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
    /* 使用transform3d启用硬件加速 */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    -webkit-transition: -webkit-transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    transition: transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    /* 优化渲染性能 */
    will-change: transform;
}

.pay_wrap_box .scroll-wrapper.scrolling {
    -webkit-transform: translate3d(0, -1em, 0);
    transform: translate3d(0, -1em, 0);
}

.pay_wrap_box .scroll-item {
    display: block;
    height: 1em;
    line-height: 1em;
    text-align: center;
    width: 100%;
    /* 防止文字模糊 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.pay_wrap_box .scroll-item.current {
    position: relative;
    top: 0;
    margin: 0;
}

.pay_wrap_box .scroll-item.next {
    position: absolute;
    top: 1em; /* 使用具体的高度值，确保完全显示 */
    left: 0;
    width: 100%;
    margin: 0;
}

/* 防止动画期间的闪烁 */
.pay_wrap_box .scroll[data-v-6d27db42] * {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    color: #666;
    animation-delay: 2s
}



/* 旧的动画关键帧已移除，使用新的transform动画 */

@media (min-width: 1200px) {
    .none[data-v-6d27db42] {
        display:none
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .none[data-v-6d27db42] {
        display:none
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .none[data-v-6d27db42] {
        display:none!important
    }
}

.buy_btn[data-v-bf9bd6de] {
    position: absolute;
    cursor: pointer;
    z-index: 99;
    box-shadow: .10667rem .10667rem .42667rem #ac8b8a;
    bottom: var(--3544effe);
    left: 10%;
    width: 80%;
    background: #e03a2e;
    color: #fff;
    font-size: 0.512rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: .16rem;
    margin: auto;
    padding: 0.256rem 0
}

.buy_btn i[data-v-bf9bd6de] {
    font-size: 0.85328rem;
    font-weight: 700;
    line-height: 1.5
}

.buy_btn i[data-v-bf9bd6de]:last-child {
    font-size: 0.512rem
}

@media (min-width: 1200px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}

@media (min-width: 480px) and (max-width: 767px) {
    .buy_btn[data-v-bf9bd6de] {
        left:20%;
        width: 60%;
        font-size: 0.256rem;
        border-radius: 0.128rem;
        padding: 0.256rem 0
    }

    .buy_btn i[data-v-bf9bd6de] {
        font-size: 0.42672rem;
        line-height: 1.5
    }

    .buy_btn i[data-v-bf9bd6de]:last-child {
        font-size: .16rem
    }
}
</style>
<script>
    $("#my_pop_shadow").click(function () {
        $(this).hide();
        $("#my_pop_box").removeClass('u-slide-up-enter-active').slideUp();
    })
    
    $(".unlock_full_btn").click(function () {
        $("#my_pop_box").addClass('u-slide-up-enter-active').slideDown();
        $("#my_pop_shadow").show();
    })
    var price = 10;
    var order_type = 3;

    function updatOrder(e) {
        var channel = e ? 'alipay' : 'wxpay';
        var url = "/mbti/pay/submitpayqx";
        var $sn = '{$row.sn}';
        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&order_type=' + order_type+'&is_two_pay=1'+ '&qt_type='+currentReportType;
        return false;
    }
    function updatOldOrder(e) {
        var channel = e ? 'alipay' : 'wechat';
        var url = "/addons/epay/mbti/submitpay";
        var $sn = '{$row.sn}';
        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&order_type=' + order_type+'&is_two_pay=1'+ '&qt_type='+currentReportType;
        return false;
    }
    localStorage.setItem("order_sn_result", '{$row.sn}');
    
</script>