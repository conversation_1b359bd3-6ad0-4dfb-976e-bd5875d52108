<div id="my_pop_shadow"></div>
<uni-view id="my_pop_box">
    <uni-view data-v-1c9eda50=""  style="flex: 1 1 0%;">
        <uni-view data-v-ec2c3526="" class="u-action-sheet">
            <div data-v-6df0b6cd="" class="pay-sheet">
                <uni-view data-v-6df0b6cd="" class="pay-sheet-introduce__wrapper">
                    <uni-view data-v-2773b30f="" data-v-6df0b6cd="">
                        <uni-view data-v-2773b30f="" class="pay-sheet__introduce">
                            <uni-view data-v-2773b30f="" class="pay-sheet__introduce-title">加购完整报告解读，额外包含：
                                <uni-view data-v-2773b30f="" class="underline"></uni-view>
                            </uni-view>
                            <uni-view data-v-2773b30f="" class="pay-sheet__introduce-list">
                                <uni-view data-v-2773b30f="" class="left">
                                    <uni-view data-v-2773b30f="">1.如何更好发挥你的<strong data-v-2773b30f="">潜能</strong>
                                    </uni-view>
                                    <uni-view data-v-2773b30f="">2.你的<strong data-v-2773b30f="">优势</strong>是什么
                                    </uni-view>
                                    <uni-view data-v-2773b30f="">3.什么样的<strong data-v-2773b30f="">职业</strong>最适合你
                                    </uni-view>
                                    <uni-view data-v-2773b30f="">4.如何提高<strong data-v-2773b30f="">人际沟通</strong>技巧
                                    </uni-view>
                                    <uni-view data-v-2773b30f="">5.吸引你的<strong data-v-2773b30f="">工作模式</strong>
                                    </uni-view>
                                    <uni-view data-v-2773b30f="">6.你可能存在的<strong data-v-2773b30f="">盲点和误区</strong>
                                    </uni-view>
                                    <uni-view data-v-2773b30f="">7.你的<strong data-v-2773b30f="">情感交友模式</strong>
                                    </uni-view>
                                    <uni-view data-v-2773b30f="">8.你最适合与哪个类型的人<strong data-v-2773b30f="">谈恋爱</strong>
                                    </uni-view>
                                </uni-view>
                                <uni-view data-v-2773b30f="" class="right">
                                    <uni-view data-v-5508fff2="" data-v-2773b30f="" class="price" type="list">
                                        <uni-view data-v-5508fff2="">超值加购</uni-view>
                                        <uni-view data-v-5508fff2="" class="now">10元
                                            <uni-view data-v-5508fff2="" class="line-price">￥39.9</uni-view>
                                        </uni-view>
                                        <uni-view data-v-25f39ece="" data-v-5508fff2=""
                                                  class="u-checkbox-group u-checkbox-group--row">
                                            <uni-view data-v-45c84c98="" data-v-5508fff2=""
                                                      class="u-checkbox u-checkbox-label--left">

                                            </uni-view>
                                            <uni-text data-v-45c84c98=""
                                                      style="color: rgb(96, 98, 102); font-size: 15px; line-height: 15px;">
                                                <span></span>
                                            </uni-text>
                                        </uni-view>
                                    </uni-view>
                                </uni-view>
                            </uni-view>
                        </uni-view>
                    </uni-view>
                </uni-view>
        </uni-view>
        <div class="ffbox" id="ffbox">

            <span onclick="updatOrder(0)" class="jgff wx">微信支付</span>

            {if !$is_weixin_browser}
<!--            <span onclick="updatOrder(1)" class="jgff zfb">支付宝支付</span>-->
            {/if}
            {if !$is_weixin_browser}
            <span onclick="updatOldOrder(1)" class="jgff zfb">支付宝支付</span>
            {/if}
        </div>

        <div style="margin-top: 15px;" class="pay-count">累计{:round(time()/50000)}人已购</div>
    </uni-view>
</uni-view>
<link rel="stylesheet" href="/Public/mbit/result2_files/mbtijg.css">
<script>
    $("#my_pop_shadow").click(function () {
        $(this).hide();
        $("#my_pop_box").removeClass('u-slide-up-enter-active').slideUp();
    })
    
    $(".unlock_full_btn").click(function () {
        $("#my_pop_box").addClass('u-slide-up-enter-active').slideDown();
        $("#my_pop_shadow").show();
    })
    var price = 10;
    var order_type = 3;
    var currentReportType = 3
    function updatOrder(e) {
        var channel = e ? 'alipay' : 'wxpay';
        var url = "/mbti/pay/submitpayqx";
        var $sn = '{$row.sn}';
        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&order_type=' + order_type+'&is_two_pay=1'+ '&qt_type='+currentReportType;
        return false;
    }
    function updatOldOrder(e) {
        var channel = e ? 'alipay' : 'wechat';
        var url = "/addons/epay/mbti/submitpay";
        var $sn = '{$row.sn}';
        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&order_type=' + order_type+'&is_two_pay=1'+ '&qt_type='+currentReportType;
        return false;
    }
    localStorage.setItem("order_sn_result", '{$row.sn}');
    
</script>