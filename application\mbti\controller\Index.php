<?php

namespace app\mbti\controller;

use app\common\controller\Commons;
use app\common\service\AdReportService;
use app\common\service\MbtiOrderTwopayService;
use app\mbti\controller\Base;
use think\Request;
use think\Session;
use think\Cookie;
use think\Db;

class Index extends Base
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';
    protected $model = '';
    protected $phone = '';
    protected $com = null;


    public function index()
    {
        //百度
        $bd_vid = $this->request->param('bd_vid', '');//百度
        $clickid = $this->request->param('clickid', '');
        $adid = $this->request->param('adid', '');//抖音
        $track_id = $this->request->get('track_id', '');//B站
        $qz_gdt = $this->request->param('qz_gdt', '');//腾讯 非微信流量
        $gdt_vid = $this->request->param('gdt_vid', '');//腾讯 微信流量
        $ad_source = $this->request->param('ad_source', '');//广告来源
        $ad_acount = $this->request->param('ad_acount', '');//广告来源
        $params = [];
        $params['state'] = 1;
        $params['report_result'] = 1;
        $params['request_url'] = $this->request->url(true);
        $flag = false;
        $sessionInfo = [];
        if ($ad_source == 'baidu' && $bd_vid){
            $params['click_id'] = $bd_vid;
            $params['source_type'] = $ad_source;
            $params['ad_acount'] = $ad_acount;
            $params['create_time'] = time();
            $params['update_time'] = time();
            $flag = true;
            $sessionInfo['click_id'] = $bd_vid;
            $sessionInfo['source_type'] = $ad_source;
            $sessionInfo['ad_acount'] = $ad_acount;
        }elseif ($ad_source == 'tengxun'){

            $params['click_id'] = $qz_gdt=='' ? $gdt_vid : $qz_gdt;

            $params['source_type'] = $ad_source;
            $params['ad_acount'] = $ad_acount;
            $params['create_time'] = time();
            $params['update_time'] = time();
            $flag = true;
            $sessionInfo['click_id'] = $clickid;
            $sessionInfo['source_type'] = $ad_source;
            $sessionInfo['ad_acount'] = $ad_acount;

        }elseif ($ad_source == 'douyin' && $clickid){
            $params['click_id'] = $clickid;
            $params['ad_id'] = $adid;
            $params['source_type'] = $ad_source;
            $params['ad_acount'] = $ad_acount;
            $params['create_time'] = time();
            $params['update_time'] = time();
            $flag = true;
            $sessionInfo['click_id'] = $clickid;
            $sessionInfo['source_type'] = $ad_source;
            $sessionInfo['ad_acount'] = $ad_acount;


        }elseif ($ad_source=='bilibili' && $track_id){
            $params['click_id'] = $track_id;

            $params['source_type'] = $ad_source;
            $params['ad_acount'] = $ad_acount;
            $params['create_time'] = time();
            $params['update_time'] = time();
            $flag = true;
            $sessionInfo['click_id'] = $track_id;
            $sessionInfo['source_type'] = $ad_source;
            $sessionInfo['ad_acount'] = $ad_acount;
        }

//        Session::set(config("customer.tuiguanginfokey"),$sessionInfo);
        if ($flag){
            if ($sessionInfo){
                Session::set(config("customer.tuiguanginfokey"),$sessionInfo);
            }
            AdReportService::writeAdReport($params);
        }


        return $this->fetch('main');
    }

    public function curPageURL()
    {
        $pageURL = 'http';

        if ($_SERVER["HTTPS"] == "on") {
            $pageURL .= "s";
        }
        $pageURL .= "://";

        if ($_SERVER["SERVER_PORT"] != "80") {
            $pageURL .= $_SERVER["SERVER_NAME"] . ":" . $_SERVER["SERVER_PORT"] . $_SERVER["REQUEST_URI"];
        } else {
            $pageURL .= $_SERVER["SERVER_NAME"] . $_SERVER["REQUEST_URI"];
        }
        return $pageURL;
    }

    public function question()
    {

        $type = input('type/d');
        if (empty($type)) {
            return ;
        }
        $tempConfig = config('mbti' . $type);
        $questionList = $tempConfig['qs'];
        $aList = $tempConfig['a'];
        $bList = $tempConfig['b'];
        $this->assign('questionList', $questionList);
        $this->assign('aList', $aList);
        $this->assign('bList', $bList);
        $this->assign('type', $type);
        Session::set(config("customer.haoshikey"),time());
        return $this->fetch();
    }

    /**
     * @return mixed 订单提交
     */
    public function handel_order()
    {
        if (Request()->isPost()) {
            $s = input();
            $order_price = 0;
            if ($s['type'] == 1) {
                $order_price = config('site.full_price');
            }
            elseif ($s['type'] == 2) {
                $order_price = config('site.base_price');
            }
            elseif ($s['type'] == 3) {
                $order_price = config('site.pro_price');
            }
            $this->com = new Commons();
            $start_time = Session::get(config("customer.haoshikey"));
            if (!$start_time){
                $start_time = time()-1449;
            }

            $addData = [
                'sn' => $this->com->create_trade_no(),
                'add_time' => date('Y-m-d H:i:s'),
                'answer_list' => json_encode(array_values($s['select_answer'])),
                'ti_type' => $s['type'],//此字段参与 题目计算 不做改动
                'qt_type' => $s['type'],//支付档位。初始默认和参与题目一致
                'order_price' => $order_price,
                'use_time'=>abs((time()-$start_time)),
            ];
            $res = model('MbtiOrder')->insert($addData, '', true);

            if ($res) {
                AdReportService::bindOrder($addData['sn']);
                return json(
                    ['code' => 0, 'msg' => '成功', 'url' => url('pay', ['sn' => $addData['sn']])]
                );
            } else {
                ['code' => 1, 'msg' => '测试失败，请重新测试'];
            }
        }
    }

    /**
     * @param string $sn
     * @return mixed|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * 结果页
     */
    public function results($sn = '')
    {
        if (empty($sn)) {
            return;
        }
        $com = new Commons();
        $row = model('MbtiOrder')->where('sn', $sn)->find();
        if ($row['order_stage'] !== 1) {
            exit('请先支付！');
        }
        
        $tpl = $com->get_score($row);
        $this->assign('row', $row);
        $this->assign('is_weixin_browser', $this->isWeixin());
        $this->assign('is_mobile_browser', isMobileBrowser());
        if($row['qt_type'] == 2){
            $pro_diff_price = 20;
        }else{
            $pro_diff_price = 10;
        }
        $this->assign('pro_diff_price', $pro_diff_price);
        return $this->fetch('result/' . $tpl);
    }

    public function paybak($sn = '')
    {

        $result = model('MbtiOrder')->where('sn', $sn)->find();
        $this->assign('result', $result);
        if ($result['order_stage'] == 1) {
            return $this->results($sn);
            exit;
        }
        //绑定上报信息

        $this->assign('sn', $sn);
        $this->assign('result', $result);
        $this->assign('is_weixin_browser', $this->isWeixin());
        $this->assign('is_mobile_browser', isMobileBrowser());
        return $this->fetch();
    }

    public function pay($sn = '')
    {

        $result = model('MbtiOrder')->where('sn', $sn)->find();
        $this->assign('result', $result);
        if ($result['order_stage'] == 1) {
            return $this->results($sn);
            exit;
        }
        //绑定上报信息

        $basick_price = config('site.base_price');
        $full_price = config('site.full_price');
        $pro_price = config('site.pro_price');
        $this->assign('basick_price', $basick_price);
        $this->assign('full_price', $full_price);
        $this->assign('pro_price', $pro_price);

        $this->assign('sn', $sn);
        $this->assign('result', $result);
        $this->assign('is_weixin_browser', $this->isWeixin());
        $this->assign('is_mobile_browser', isMobileBrowser());
        $this->assign('today', date('Y-m-d'));
        $this->assign('ti_num',getqt_type_to_tinum($result['qt_type']));
        $this->assign('use_time_str',formatDuration($result['use_time']));
        return $this->fetch();
    }

    public function read($sn = '')
    {
        $tpl = 'read';
        $result = model('MbtiOrder')->where('sn', $sn)->find();
        $this->assign('result', $result);
        $this->assign('is_weixin_browser', $this->isWeixin());
        if ($result['order_stage'] == 1) {
            return $this->results($sn);
            exit;
        }
        $this->assign('result', $result);
        return $this->fetch($tpl);
    }

    public function admin_read($sn) {
        $row = model('MbtiOrder')->where('sn', $sn)->find();
        if ($row['order_stage'] !== 1) {
            exit('支付后才能查看结果哦！');
        }
        return $this->read($sn);
    }
    public function checkStatus($sn = '')
    {
        if (!Request()->isPost()) {
            return;
        }
        $sn = input('sn');
        $result = model('MbtiOrder')->where('sn', $sn)->find();
        if ($result['order_stage'] == 1) {
            $this->success('支付成功', url('results', ['sn' => $sn]));
        }
        $this->error('未支付');
    }
    public function checkpaystatus($sn = '')
    {
        if (!Request()->isPost()) {
            return;
        }

        $sn = input('sn');
//        $is_two_pay = input('is_two_pay');
        $is_two_pay = $this->request->post('is_two_pay/d', 0);
//        var_dump($is_two_pay);
        if ($is_two_pay){
            $result = MbtiOrderTwopayService::getOrderTwopayInfo(['sn' => $sn],'order_stage');
        }else{
            $result = model('MbtiOrder')->where('sn', $sn)->find();
        }
        if (!$result){
            return json(
                ['code' => 0, 'msg' => '未支付']
            );
        }

        if ($result['order_stage'] == 1) {
            return json(
                ['code' => 200, 'msg' => '成功']
            );
        }else{
            return json(
                ['code' => 0, 'msg' => '未支付']
            );
        }

    }


    public function isWeixin()
    {
        if (strpos($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger') !== false) {
            return true;
        } else {
            return false;
        }
    }


    public function search()
    {
        if (Request()->isPost()) {
            $sn = input('post.keyword');
            if (empty($sn)) {
                $this->error('订单号错误!');
            }
            if(strpos($sn,'_') !== false){
                $sn = explode('_',$sn)[0];
            }
            $row = model('MbtiOrder')->where('sn', $sn)->where('order_stage', 1)->find();
            if (!empty($row)) {
                $url = url('/s/' . $sn);
                $this->success('成功', $url);
            }
            $this->error('没有找到订单!');
        } else {
            return $this->fetch();
        }
    }

    public function to_pay()
    {
        $channel = input('type');
        $price = input('amount');
        $sn = input('order_sn');
        $order_type = input('order_type');
        $prex = $_SERVER['REQUEST_SCHEME'];
        $host = $_SERVER['HTTP_HOST'];
        $pay_url = $prex . "://" . $host;
        $pay_url = $pay_url . '/addons/epay/mbti/submitpay?' . 'type=' . $channel . '&amount=' . $price . '&order_sn=' . $sn . '&order_type=' . $order_type;
        $tpl = '../application/mbti/view/default/public/to_pay.html';
        $this->assign('pay_url', $pay_url);
        return $this->fetch($tpl);
    }

    public function reportAd(){
        AdReportService::syncAdReport();
    }
}