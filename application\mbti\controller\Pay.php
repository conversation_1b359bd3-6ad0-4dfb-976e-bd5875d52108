<?php

namespace app\mbti\controller;


use app\common\model\MbtiOrder;
use app\common\service\AdReportService;
use app\common\service\MbtiOrderTwopayService;
use app\mbti\Logic\PayLogic;
use app\mbti\utils\HuiTongPayUtils;
use app\mbti\utils\QiXiangUtils;
use think\Controller;
use think\Log;
class Pay extends Controller
{

    public function submitpayqx(){

        $amount = $this->request->request('amount');
        $type = $this->request->request('type');
        $out_trade_no = $this->request->request('order_sn');
        $is_two_pay = $this->request->request('is_two_pay',0);
        $qt_type = $this->request->request('qt_type',0);
        $iscoupon = $this->request->request('iscoupon',0);


        $qt_type = getti_type($qt_type);//获取真实档位信息
        $orderInfo = MbtiOrder::where(['sn' => $out_trade_no])->field('order_stage,qt_type')->find();

        $returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;
        if ($is_two_pay){
            if ($orderInfo['qt_type'] == 3){
                echo '<script type="text/javascript">';
                echo 'alert("该订单已是PRO版报告,无需支付解锁！");';
                echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
//            echo 'history.go(-1)';
                echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
                return ;
            }
            //二次支付价格
//            $amount = getSecondPayMoney($qt_type,$orderInfo['qt_type']);
            $amount = 10;
        }else{
            $amount = getCurPrice($qt_type,$iscoupon);
        }
        var_dump('--金额--'.$amount);
        var_dump('--二次支付--'.$is_two_pay);
        var_dump('--支付类型--'.$qt_type);
        sleep(10);
        $amount = 0.01;
        //$method = $this->request->request('method');
        /*if ($out_trade_no == 'Z611371930432372') {
            $amount = 0.01;
        }*/

        if (!$amount || $amount < 0) {
            $this->error("支付金额必须大于0");
        }

        if (!$type || !in_array($type, ['alipay', 'wxpay'])) {
            $this->error("支付类型不能为空");
        }
        $returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;

        if ($orderInfo && $orderInfo['order_stage'] == 1 && $is_two_pay==0){
            echo '<script type="text/javascript">';
            echo 'alert("该订单已完成支付");';
            echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
//            echo 'history.go(-1)';
            echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
            return ;
        }
        //订单标题
        $title = 'MBTI在线性格测试';

        //回调链接
        //如果是 增量支付
        $payOrderSn = $out_trade_no.'_'.time();
//        $twoPayOderSn = '';
        if ($is_two_pay == 1){
            if ($orderInfo['qt_type'] == 3){
                echo '<script type="text/javascript">';
                echo 'alert("该订单已是PRO版报告,无需支付解锁！");';
                echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
//            echo 'history.go(-1)';
                echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
                return ;
            }
            $twopayInfo = MbtiOrderTwopayService::getOrderTwopayInfo(['sn' => $out_trade_no], "sn,two_sn,order_stage");
            if (!$twopayInfo){
//                $twoPayOderSn = $out_trade_no.'_'.time();
                $twopayData = [
                    'sn'=>$out_trade_no,
                    'order_price'=>$amount,
                    'add_time'=>date("Y-m-d H:i:s"),
                ];
                $res = MbtiOrderTwopayService::insertData($twopayData);
            }else{
                if ($twopayInfo['order_stage'] == 1){
//                    echo '已支付';
                    echo '<script type="text/javascript">';
                    echo 'alert("该订单已完成支付");';
                    echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
                    echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
                    return ;
                }
//                $twoPayOderSn = $twopayInfo['two_sn'];
            }

        }

//        $lastpayordersn = $twoPayOderSn ? $twoPayOderSn : $out_trade_no;
        $lastpayordersn = $payOrderSn;
        $notifyurl = $this->request->root(
                true
            ) . '/mbti/pay/notifyhandleqx/paytype/' . $type.'/is_two_pay/'.$is_two_pay.'/qt_type/'.$qt_type;
//        $returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;

        $response = PayLogic::submitOrderqx($amount, $lastpayordersn, $type, $title, $notifyurl, $returnurl);

        if (is_array($response)){
            if ($response['code'] == 1){
                header("Location: ".$response['payurl']);
                exit; // 确保脚本停止执行
            }else{
                echo '<script type="text/javascript">';
                echo 'alert("支付异常，请重新发起支付");';
                echo 'history.go(-1);'; // 重定向到当前页面并带上确认参数
                echo '</script>';
                return ;
            }

        }else{

            echo '<script type="text/javascript">';
            echo 'alert("支付异常，请重新发起支付");';
            echo 'history.go(-1);'; // 重定向到当前页面并带上确认参数
            echo '</script>';
            return ;
        }


    }

    public function notifyhandleqx(){
        $paytype = $this->request->param('type');
        $is_two_pay = $this->request->param('is_two_pay',0);
        $pid = $this->request->param('pid',0);
        $trade_no = $this->request->param('trade_no','');
        $out_trade_no = $this->request->param('out_trade_no','');
        $trade_status = $this->request->param('trade_status','');
        $money = $this->request->param('money',0);
        $qt_type = $this->request->param('qt_type',0);


        $QiXianggPayUtils = new QiXiangUtils($paytype);

        $verify = $QiXianggPayUtils->verifyNotify($this->request->get());
        if (!$verify){
            echo '签名错误';
            return;
        }

        if ($trade_status != 'TRADE_SUCCESS') {
            echo '支付失败';
            return;
        }

        $sub_paytype = 'qixiang';

        Log::record($this->request->get(), 'notifydata');
        try {

            $payamount = $money;
            //是二次支付
            if ($is_two_pay == 1){

                Log::record('second pay:'.$out_trade_no, 'out_trade_no');
                $true_out_trade_no = $out_trade_no;

                if(strpos($out_trade_no,'_') !== false){
                    $out_trade_no = explode('_',$out_trade_no)[0];
                }

//                $row = db('mbti_order')->where(['sn' => $out_trade_no])->find();
                $mbitTwoInfo = MbtiOrderTwopayService::getOrderTwopayInfo(['sn' => $out_trade_no],"sn,two_sn,order_stage");
                if (!$mbitTwoInfo) {
                    Log::record('second pay:'.$out_trade_no, 'no out_trade_no');
                    return;
                }
                if ($mbitTwoInfo['order_stage'] == 1) {
                    echo "success";
                    return;
                }

                $twoUpdata = [
                    'paymoney' => $payamount,
                    'order_stage' => 1,
                    'paytype' => $paytype,
                    'sub_paytype' => $sub_paytype,
                    'two_sn' => $true_out_trade_no,
                    'pay_time' => date('Y-m-d H:i:s'),
                ];
                MbtiOrderTwopayService::update(['sn'=>$out_trade_no], $twoUpdata);

                $updata = [
                    'is_twopay' => 1,
                    'qt_type' => $qt_type,
                ];

                db('mbti_order')->where(['sn' => $mbitTwoInfo['sn']])->update($updata);

            }else{
//                $out_trade_no = $data['out_trade_no'];
                $true_out_trade_no = $out_trade_no;
                Log::record($out_trade_no, 'out_trade_no');
                if(strpos($out_trade_no,'_') !== false){
                    $out_trade_no = explode('_',$out_trade_no)[0];
                }
                $row = db('mbti_order')->where(['sn' => $out_trade_no])->find();
                if (empty($row)) {
                    Log::record($out_trade_no, 'no out_trade_no');
                    return;
                }
                if ($row['order_stage'] == 1) {
                    echo "success";
                    return;
                }
                $updata = [
                    'paymoney' => $payamount,
                    'order_stage' => 1,
                    'paytype' => $paytype,
                    'out_trade_no' => $true_out_trade_no,
                    'sub_paytype' => $sub_paytype,
                    'qt_type' => $qt_type,

                ];
                $updata['pay_time'] = date('Y-m-d H:i:s');
                db('mbti_order')->where(['sn' => $out_trade_no])->update($updata);
                //处理上报逻辑
                AdReportService::updateReportStatus($out_trade_no);
            }

            //你可以在此编写订单逻辑
        } catch (Exception $e) {
            Log::record($e->getMessage(), 'catch_notifydata');
        }
        echo "success";


    }

    public function submitpay(){

        $amount = $this->request->request('amount');
        $type = $this->request->request('type');
        $out_trade_no = $this->request->request('order_sn');
        $is_two_pay = $this->request->request('is_two_pay',0);
        //$method = $this->request->request('method');
        /*if ($out_trade_no == 'Z611371930432372') {
            $amount = 0.01;
        }*/

        if (!$amount || $amount < 0) {
            $this->error("支付金额必须大于0");
        }

        if (!$type || !in_array($type, ['alipay', 'wxpay'])) {
            $this->error("支付类型不能为空");
        }
        $returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;
        $orderInfo = MbtiOrder::where(['sn' => $out_trade_no])->field('order_stage')->find();
        if ($orderInfo && $orderInfo['order_stage'] == 1 && $is_two_pay==0){
            echo '<script type="text/javascript">';
            echo 'alert("该订单已完成支付");';
            echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
//            echo 'history.go(-1)';
            echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
            return ;
        }
        //订单标题
        $title = 'MBTI在线性格测试';
        $returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;
        //回调链接
        //如果是 增量支付
        $payOrderSn = $out_trade_no.'_'.time();
//        $twoPayOderSn = '';
        if ($is_two_pay == 1){

            $twopayInfo = MbtiOrderTwopayService::getOrderTwopayInfo(['sn' => $out_trade_no], "sn,two_sn,order_stage");
            if (!$twopayInfo){
//                $twoPayOderSn = $out_trade_no.'_'.time();
                $twopayData = [
                    'sn'=>$out_trade_no,
                    'order_price'=>$amount,
                    'add_time'=>date("Y-m-d H:i:s"),
                ];
                $res = MbtiOrderTwopayService::insertData($twopayData);
            }else{
                if ($twopayInfo['order_stage'] == 1){
//                    echo '已支付';
                    echo '<script type="text/javascript">';
                    echo 'alert("该订单已完成支付");';
                    echo 'window.location.href = "'.$returnurl.'";'; // 重定向到当前页面并带上确认参数
                    echo '</script>';
//                    header("Location: ".$this->request->root(true) . '/mbti/index/results/sn/' . $out_trade_no);
                    return ;
                }
//                $twoPayOderSn = $twopayInfo['two_sn'];
            }

        }

//        $lastpayordersn = $twoPayOderSn ? $twoPayOderSn : $out_trade_no;
        $lastpayordersn = $payOrderSn;
        $notifyurl = $this->request->root(
                true
            ) . '/mbti/pay/notifyHandle/paytype/' . $type.'/is_two_pay/'.$is_two_pay;
        $returnurl = $this->request->root(true) . '/mbti/index/read/sn/' . $out_trade_no;

        $response = PayLogic::submitOrder($amount, $lastpayordersn, $type, $title, $notifyurl, $returnurl);

        if (is_array($response)){
            if (isWechatBrowser()){
                header("Location: ".$response['payurl']);
                exit; // 确保脚本停止执行
            }else{
//                header("Location: ".$response['payurl2']);
                $this->assign('tiaourl',$response['payurl2']);
                $this->assign('returnurl',$returnurl);
                $this->assign('order_sn',$out_trade_no);
                $this->assign('is_two_pay',$is_two_pay);
                return $this->fetch();
                exit; // 确保脚本停止执行

            }

        }
        if (is_string($response)){
            echo $response;
            exit; // 确保脚本停止执行
        }

    }

    public function notifyHandle(){
        $paytype = $this->request->param('type');
        $is_two_pay = $this->request->param('is_two_pay',0);
        $pid = $this->request->param('pid',0);
        $trade_no = $this->request->param('trade_no','');
        $out_trade_no = $this->request->param('out_trade_no','');
        $trade_status = $this->request->param('trade_status','');
        $money = $this->request->param('money',0);


        $HuiTongPayUtils = new HuiTongPayUtils($paytype);

        $verify = $HuiTongPayUtils->verifyNotify($this->request->get());
        if (!$verify){
            echo '签名错误';
            return;
        }

        if ($trade_status != 'TRADE_SUCCESS') {
            echo '支付失败';
            return;
        }

        $sub_paytype = 'huitong';

        Log::record($this->request->get(), 'notifydata');
        try {

            $payamount = $money;
            //是二次支付
            if ($is_two_pay == 1){

                Log::record('second pay:'.$out_trade_no, 'out_trade_no');
                $true_out_trade_no = $out_trade_no;

                if(strpos($out_trade_no,'_') !== false){
                    $out_trade_no = explode('_',$out_trade_no)[0];
                }

//                $row = db('mbti_order')->where(['sn' => $out_trade_no])->find();
                $mbitTwoInfo = MbtiOrderTwopayService::getOrderTwopayInfo(['sn' => $out_trade_no],"sn,two_sn,order_stage");
                if (!$mbitTwoInfo) {
                    Log::record('second pay:'.$out_trade_no, 'no out_trade_no');
                    return;
                }
                if ($mbitTwoInfo['order_stage'] == 1) {
                    echo "success";
                    return;
                }

                $twoUpdata = [
                    'paymoney' => $payamount,
                    'order_stage' => 1,
                    'paytype' => $paytype,
                    'sub_paytype' => $sub_paytype,
                    'two_sn' => $true_out_trade_no,
                    'pay_time' => date('Y-m-d H:i:s'),
                ];
                MbtiOrderTwopayService::update(['sn'=>$out_trade_no], $twoUpdata);

                $updata = [
                    'is_twopay' => 1,
                    'ti_type' => 3,
                ];

                db('mbti_order')->where(['sn' => $mbitTwoInfo['sn']])->update($updata);

            }else{
//                $out_trade_no = $data['out_trade_no'];
                $true_out_trade_no = $out_trade_no;
                Log::record($out_trade_no, 'out_trade_no');
                if(strpos($out_trade_no,'_') !== false){
                    $out_trade_no = explode('_',$out_trade_no)[0];
                }
                $row = db('mbti_order')->where(['sn' => $out_trade_no])->find();
                if (empty($row)) {
                    Log::record($out_trade_no, 'no out_trade_no');
                    return;
                }
                if ($row['order_stage'] == 1) {
                    echo "success";
                    return;
                }
                $updata = [
                    'paymoney' => $payamount,
                    'order_stage' => 1,
                    'paytype' => $paytype,
                    'out_trade_no' => $true_out_trade_no,
                    'sub_paytype' => $sub_paytype

                ];
                $updata['pay_time'] = date('Y-m-d H:i:s');
                db('mbti_order')->where(['sn' => $out_trade_no])->update($updata);
                //处理上报逻辑
                AdReportService::updateReportStatus($out_trade_no);
            }

            //你可以在此编写订单逻辑
        } catch (Exception $e) {
            Log::record($e->getMessage(), 'catch_notifydata');
        }
        echo "success";


    }


}