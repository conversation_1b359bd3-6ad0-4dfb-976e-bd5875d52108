<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字滚动动画测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .number-display {
            font-size: 48px;
            font-weight: bold;
            text-align: center;
            margin: 30px 0;
            color: #333;
        }
        
        .buy-info {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            color: #666;
        }
        
        /* 数字滚动动画样式 */
        .scroll1, .scroll2 {
            position: relative;
            overflow: hidden;
            display: inline-block;
            height: 1.2em;
            line-height: 1.2em;
            vertical-align: top;
        }
        
        .scroll-container {
            position: relative;
            transition: transform 1s ease-in-out;
            height: 100%;
        }
        
        .scroll-container.animate-up {
            transform: translateY(-100%);
        }
        
        .current-num, .next-num {
            display: block;
            height: 1.2em;
            line-height: 1.2em;
        }
        
        .next-num {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
        }
        
        .control-panel {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #333;">数字滚动动画测试</h1>
        
        <div class="number-display">
            <span class="buynumshow">393360</span><span class="scroll1">0</span><span class="scroll2">1</span>
        </div>
        
        <div class="buy-info">
            <div class="buy_btn">
                <i>3933601人购买</i>
            </div>
        </div>
        
        <div class="status">
            <strong>当前状态:</strong><br>
            总购买人数: <span class="highlight" id="total-count">3933601</span><br>
            Scroll1值: <span class="highlight" id="scroll1-val">0</span><br>
            Scroll2值: <span class="highlight" id="scroll2-val">1</span><br>
            下次增长: <span class="highlight" id="next-increment">随机1或2</span>
        </div>
        
        <div class="control-panel">
            <button class="btn" onclick="triggerAnimation()">手动触发动画</button>
            <button class="btn" onclick="toggleAutoAnimation()">
                <span id="auto-status">开启</span>自动动画
            </button>
            <button class="btn" onclick="resetNumbers()">重置数字</button>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #e9ecef; border-radius: 5px;">
            <h4>测试说明:</h4>
            <ul>
                <li>数字每3秒自动滚动一次</li>
                <li>每次随机增长1或2</li>
                <li>滚动动画持续1秒</li>
                <li>动画完成后数字保持稳定，不会跳跃</li>
            </ul>
        </div>
    </div>

    <script>
        let buyUserCount = parseInt(localStorage.getItem('buyUserCount2') || 3933600);
        let scroll1Value = buyUserCount % 10;
        let scroll2Value = (scroll1Value + 1) % 10;
        let autoAnimationInterval = null;
        let isAnimating = false;
        
        function updateScrollNumbers() {
            if (isAnimating) return; // 防止动画重叠
            isAnimating = true;
            
            // 随机增长1或2
            const increment = Math.random() < 0.5 ? 1 : 2;
            $('#next-increment').text(increment);
            
            // 计算新的数字值
            const newScroll1 = (scroll1Value + increment) % 10;
            const newScroll2 = (scroll2Value + increment) % 10;
            
            console.log(`动画开始: ${scroll1Value}${scroll2Value} -> ${newScroll1}${newScroll2} (增长${increment})`);
            
            // 创建滚动动画HTML结构
            $('.scroll1').html(`
                <div class="scroll-container">
                    <span class="current-num">${scroll1Value}</span>
                    <span class="next-num">${newScroll1}</span>
                </div>
            `);
            
            $('.scroll2').html(`
                <div class="scroll-container">
                    <span class="current-num">${scroll2Value}</span>
                    <span class="next-num">${newScroll2}</span>
                </div>
            `);
            
            // 触发CSS动画
            setTimeout(() => {
                $('.scroll1 .scroll-container').addClass('animate-up');
                $('.scroll2 .scroll-container').addClass('animate-up');
            }, 50);
            
            // 动画完成后更新数据
            setTimeout(() => {
                // 更新显示的数字
                scroll1Value = newScroll1;
                scroll2Value = newScroll2;
                
                // 清理动画，直接显示新数字
                $('.scroll1').text(scroll1Value);
                $('.scroll2').text(scroll2Value);
                
                // 更新总数和本地存储
                buyUserCount += increment;
                localStorage.setItem('buyUserCount2', buyUserCount);
                
                // 更新其他显示
                $('.buynumshow').text(Math.floor(buyUserCount / 10));
                $('.buy_btn i').text(buyUserCount + "人购买");
                
                // 更新状态显示
                updateStatus();
                
                isAnimating = false;
                console.log(`动画完成: 新值 ${scroll1Value}${scroll2Value}, 总数 ${buyUserCount}`);
            }, 1000); // 动画持续1秒
        }
        
        function updateStatus() {
            $('#total-count').text(buyUserCount);
            $('#scroll1-val').text(scroll1Value);
            $('#scroll2-val').text(scroll2Value);
        }
        
        function triggerAnimation() {
            updateScrollNumbers();
        }
        
        function toggleAutoAnimation() {
            if (autoAnimationInterval) {
                clearInterval(autoAnimationInterval);
                autoAnimationInterval = null;
                $('#auto-status').text('开启');
            } else {
                autoAnimationInterval = setInterval(updateScrollNumbers, 3000);
                $('#auto-status').text('关闭');
            }
        }
        
        function resetNumbers() {
            if (autoAnimationInterval) {
                clearInterval(autoAnimationInterval);
                autoAnimationInterval = null;
                $('#auto-status').text('开启');
            }
            
            buyUserCount = 3933600;
            scroll1Value = buyUserCount % 10;
            scroll2Value = (scroll1Value + 1) % 10;
            
            localStorage.setItem('buyUserCount2', buyUserCount);
            
            $('.scroll1').text(scroll1Value);
            $('.scroll2').text(scroll2Value);
            $('.buynumshow').text(Math.floor(buyUserCount / 10));
            $('.buy_btn i').text(buyUserCount + "人购买");
            
            updateStatus();
            isAnimating = false;
        }
        
        // 页面加载时初始化
        $(document).ready(function() {
            $('.scroll1').text(scroll1Value);
            $('.scroll2').text(scroll2Value);
            $('.buynumshow').text(Math.floor(buyUserCount / 10));
            $('.buy_btn i').text(buyUserCount + "人购买");
            
            updateStatus();
            
            // 自动开启动画
            autoAnimationInterval = setInterval(updateScrollNumbers, 3000);
            $('#auto-status').text('关闭');
        });
    </script>
</body>
</html>
