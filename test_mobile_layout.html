<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
    <title>手机端布局测试</title>
    <link rel="stylesheet" href="/Public/mbit/newpay_files/index-0fbdb63a.css">
    <link rel="stylesheet" href="/Public/mbit/newpay_files/jiesuoBtn-cc97e989.css">
    <link rel="stylesheet" href="/Public/mbit/newpay_files/statusTitle-c811fd91.css">
    <link rel="stylesheet" href="/Public/mbit/newpay_files/comp15-153fa799.css">
    <link rel="stylesheet" href="/Public/mbit/newpay_files/result-1dae3fbf.css">
    <link rel="stylesheet" href="/Public/mbit/newpay_files/mobile-fix.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            background: #612fc6;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .component-demo {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .component-title {
            background: #f8f9fa;
            padding: 10px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
        }
        .component-content {
            padding: 15px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>手机端布局修复测试</h1>
            <p>测试com10、com11、com12组件间距</p>
        </div>
        
        <div class="test-content">
            <!-- 性格优势组件测试 -->
            <div class="component-demo">
                <div class="component-title">
                    <span class="status-indicator status-ok"></span>
                    性格优势组件 (com10)
                </div>
                <div class="component-content">
                    <div class="com10" data-v-1f429bc7>
                        <div class="paddiing" data-v-1f429bc7>
                            <div class="com10_wrap" data-v-1f429bc7>
                                <div class="com10_title" data-v-1f429bc7>性格优势</div>
                                <p>这是性格优势的内容区域，用于测试组件间距是否正常。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 性格劣势组件测试 -->
            <div class="component-demo">
                <div class="component-title">
                    <span class="status-indicator status-warning"></span>
                    性格劣势组件 (com11)
                </div>
                <div class="component-content">
                    <div class="com11" data-v-b331d87b>
                        <div class="paddiing" data-v-b331d87b>
                            <div class="com11_wrap" data-v-b331d87b>
                                <div class="com11_title" data-v-b331d87b>性格劣势</div>
                                <p>这是性格劣势的内容区域，应该与上方组件有适当间距。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 个人成长建议组件测试 -->
            <div class="component-demo">
                <div class="component-title">
                    <span class="status-indicator status-ok"></span>
                    个人成长建议组件 (com12)
                </div>
                <div class="component-content">
                    <div class="com12" data-v-08b317a6>
                        <div class="com12_item" data-v-08b317a6>
                            <div class="com12_title" data-v-08b317a6>个人成长建议</div>
                            <div class="padding" data-v-08b317a6>
                                <p>这是个人成长建议的内容区域，应该与上方组件有适当间距，不会重叠。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 测试说明 -->
            <div class="component-demo">
                <div class="component-title">
                    <span class="status-indicator status-ok"></span>
                    修复说明
                </div>
                <div class="component-content">
                    <h4>修复内容：</h4>
                    <ul>
                        <li>增加了组件间的margin-bottom间距</li>
                        <li>添加了z-index层级管理</li>
                        <li>为不同屏幕尺寸设置了响应式间距</li>
                        <li>添加了清除浮动和overflow处理</li>
                    </ul>
                    
                    <h4>适用屏幕：</h4>
                    <ul>
                        <li>≤767px: 基础修复</li>
                        <li>≤480px: 增强间距</li>
                        <li>≤360px: 最大间距</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检测屏幕尺寸并显示当前应用的CSS规则
        function checkScreenSize() {
            const width = window.innerWidth;
            let rule = '';
            
            if (width <= 360) {
                rule = '超小屏幕 (≤360px): 最大间距 2.5rem';
            } else if (width <= 480) {
                rule = '小屏幕 (≤480px): 增强间距 2rem';
            } else if (width <= 767) {
                rule = '手机屏幕 (≤767px): 基础间距 1.5rem';
            } else {
                rule = '桌面屏幕 (>767px): 原始样式';
            }
            
            console.log(`当前屏幕宽度: ${width}px`);
            console.log(`应用规则: ${rule}`);
        }
        
        // 页面加载时检查
        window.addEventListener('load', checkScreenSize);
        
        // 窗口大小改变时检查
        window.addEventListener('resize', checkScreenSize);
    </script>
</body>
</html>
