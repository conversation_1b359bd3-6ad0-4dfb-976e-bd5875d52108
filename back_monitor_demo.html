<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器返回监控演示</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/layer@3.5.1/dist/layer.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .demo-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        
        .demo-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }
        
        .demo-desc {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .test-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background: #612fc6;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #4a1fa3;
            transform: translateY(-2px);
        }
        
        .test-btn.secondary {
            background: #89cdfa;
        }
        
        .test-btn.secondary:hover {
            background: #6bb6ff;
        }
        
        .instructions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #333;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            color: #666;
        }
        
        /* 隐藏的弹窗内容 */
        #huodongyouhui_dialog {
            display: none;
        }
        
        .dialog-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            max-width: 350px;
            margin: 0 auto;
        }
        
        .dialog-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        
        .dialog-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .dialog-price {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 20px;
        }
        
        .dialog2_btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(135deg, #612fc6 0%, #8b5cf6 100%);
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dialog2_btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(97, 47, 198, 0.4);
        }
        
        .close_btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            color: #999;
            cursor: pointer;
        }
        
        .status-info {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            color: #2d5a2d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-title">🔙 浏览器返回监控演示</div>
        <div class="demo-desc">
            这个演示展示了如何监控浏览器返回按钮，当用户尝试返回时弹出优惠弹窗并阻止返回操作。
        </div>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testBackDialog()">测试弹窗</button>
            <button class="test-btn secondary" onclick="simulateBack()">模拟返回</button>
            <button class="test-btn secondary" onclick="checkStatus()">检查状态</button>
        </div>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ul>
                <li><strong>点击浏览器返回按钮</strong> - 会弹出优惠弹窗并阻止返回</li>
                <li><strong>按ESC键</strong> - 也会触发弹窗（可选功能）</li>
                <li><strong>按F5刷新</strong> - 会显示确认对话框</li>
                <li><strong>关闭标签页</strong> - 会显示离开确认提示</li>
            </ul>
            
            <h3>🎯 功能特点</h3>
            <ul>
                <li>✅ 监控浏览器返回按钮</li>
                <li>✅ 使用layer插件显示弹窗</li>
                <li>✅ 阻止页面返回操作</li>
                <li>✅ 支持键盘事件监控</li>
                <li>✅ 页面卸载提示</li>
            </ul>
        </div>
        
        <div class="status-info" id="statusInfo">
            ✅ 返回监控已启动，请尝试点击浏览器返回按钮
        </div>
    </div>

    <!-- 隐藏的弹窗内容 -->
    <div id="huodongyouhui_dialog">
        <div class="dialog-content">
            <button class="close_btn">×</button>
            <div class="dialog-title">🎉 限时优惠</div>
            <div class="dialog-desc">
                检测到您要离开页面！<br>
                现在购买可享受特别优惠价格，机会难得！
            </div>
            <div class="dialog-price">
                ¥19.9 <span style="font-size:14px;color:#999;text-decoration:line-through;">原价 ¥99</span>
            </div>
            <button class="dialog2_btn">立即购买</button>
        </div>
    </div>

    <script>
        // 测试函数
        function testBackDialog() {
            if (typeof BrowserBackMonitor !== 'undefined') {
                BrowserBackMonitor.showDialog();
            } else {
                // 直接调用简化版本的函数
                showBackDialog();
            }
        }
        
        function simulateBack() {
            // 模拟返回操作
            window.history.back();
        }
        
        function checkStatus() {
            var status = '返回监控状态：';
            if (window.history && window.history.pushState) {
                status += '✅ 已启动';
            } else {
                status += '❌ 浏览器不支持';
            }
            
            if (typeof layer !== 'undefined') {
                status += ' | Layer插件：✅ 已加载';
            } else {
                status += ' | Layer插件：❌ 未加载';
            }
            
            document.getElementById('statusInfo').textContent = status;
        }
        
        // 简化版返回监控（内嵌版本）
        $(document).ready(function() {
            // 浏览器返回监控功能
            function initBackMonitor() {
                if (!window.history || !window.history.pushState) {
                    console.warn('浏览器不支持History API');
                    return;
                }
                
                // 添加虚拟历史记录
                window.history.pushState('preventBack', null, '');
                
                // 监听返回事件
                window.addEventListener('popstate', function(event) {
                    // 立即重新添加历史记录，阻止返回
                    window.history.pushState('preventBack', null, '');
                    
                    // 显示优惠弹窗
                    showBackDialog();
                });
                
                console.log('返回监控已启动');
            }
            
            // 显示返回拦截弹窗
            function showBackDialog() {
                var dialogElement = $('#huodongyouhui_dialog');
                
                if (dialogElement.length === 0) {
                    console.error('找不到弹窗元素');
                    return;
                }
                
                // 使用layer插件显示弹窗
                if (typeof layer !== 'undefined') {
                    var dialogContent = dialogElement.html();
                    
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 0,
                        area: ['90%', 'auto'],
                        maxWidth: '400px',
                        skin: 'layui-layer-nobg',
                        shadeClose: false,
                        shade: [0.8, '#000'],
                        content: dialogContent,
                        success: function(layero, index) {
                            console.log('返回拦截弹窗已显示');
                            
                            // 为弹窗内的按钮添加点击事件
                            layero.find('.dialog2_btn').on('click', function() {
                                console.log('用户点击了购买按钮');
                                layer.close(index);
                                alert('购买功能演示 - 实际项目中这里会跳转到支付页面');
                            });
                            
                            // 关闭按钮
                            layero.find('.close_btn').on('click', function() {
                                layer.close(index);
                            });
                        }
                    });
                } else {
                    alert('Layer插件未加载，请检查CDN链接');
                }
            }
            
            // 全局暴露函数供测试使用
            window.showBackDialog = showBackDialog;
            
            // ESC键监听
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) { // ESC键
                    e.preventDefault();
                    showBackDialog();
                }
            });
            
            // 页面卸载提示
            window.addEventListener('beforeunload', function(e) {
                var message = '确定要离开吗？您可能会错过限时优惠！';
                e.preventDefault();
                e.returnValue = message;
                return message;
            });
            
            // 启动监控
            initBackMonitor();
            
            // 初始状态检查
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>
