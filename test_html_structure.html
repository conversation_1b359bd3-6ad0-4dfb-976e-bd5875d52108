<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML结构测试</title>
    <script src="/Public/layer/jquery.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .center {
            text-align: center;
            font-size: 18px;
            margin: 20px 0;
            border: 2px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            /* 移除overflow-y: hidden，确保文字可见 */
            line-height: 1.5;
        }
        
        .buynumshow {
            color: #4CAF50;
            font-weight: bold;
            font-size: 24px;
            background: #e8f5e8;
            padding: 2px 8px;
            border-radius: 3px;
        }
        
        .scroll {
            display: inline-block;
            background: #e3f2fd;
            border: 2px solid #2196F3;
            border-radius: 5px;
            padding: 2px 8px;
            margin: 0 3px;
            min-width: 20px;
            text-align: center;
            font-weight: bold;
            position: relative;
            overflow: hidden;
            height: 1.2em; /* 与文字行高一致 */
            vertical-align: baseline; /* 与文字基线对齐 */
            line-height: 1.2em;
        }
        
        .scroll-wrapper {
            position: relative;
            height: 100%;
            width: 100%;
            transition: transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
        }
        
        .scroll-wrapper.scrolling {
            transform: translate3d(0, -100%, 0);
        }
        
        .scroll-item {
            display: block;
            height: 1.2em;
            line-height: 1.2em;
            text-align: center;
            width: 100%;
        }
        
        .scroll-item.current {
            position: relative;
            top: 0;
        }
        
        .scroll-item.next {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
        }
        
        .btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #1976D2;
        }
        
        .status {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>HTML结构测试 - 购买人数显示</h2>
        
        <div class="center">
            已有<span class="buynumshow">238997</span>
            <div class="scroll">=<div class="scroll-item">0</div></div>人购买
        </div>
        
        <div class="status">
            <strong>当前HTML结构：</strong><br>
            <span id="currentHtml"></span>
        </div>
        
        <div>
            <button class="btn" onclick="testAnimation()">测试动画</button>
            <button class="btn" onclick="showStructure()">显示结构</button>
            <button class="btn" onclick="resetStructure()">重置结构</button>
        </div>
        
        <div class="status">
            <strong>测试说明：</strong><br>
            1. 检查"已有xxx人购买"文字是否完整显示<br>
            2. 测试滚动动画是否正常<br>
            3. 验证HTML结构是否正确
        </div>
    </div>

    <script>
        let currentNumber = 0;
        
        function showStructure() {
            const html = $('.center').html();
            $('#currentHtml').html('<span class="highlight">' + html.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</span>');
        }
        
        function testAnimation() {
            const newNumber = currentNumber + 1;
            
            // 更新buynumshow
            $('.buynumshow').text('23899' + newNumber);
            
            // 准备动画结构
            const scrollHtml = `
                =<div class="scroll-wrapper">
                    <div class="scroll-item current">${currentNumber}</div>
                    <div class="scroll-item next">${newNumber}</div>
                </div>
            `;
            
            $('.scroll').html(scrollHtml);
            
            // 强制重排
            $('.scroll')[0].offsetHeight;
            
            // 触发动画
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    $('.scroll-wrapper').addClass('scrolling');
                });
            });
            
            // 动画完成后清理
            setTimeout(() => {
                $('.scroll').html(`=<div class="scroll-item">${newNumber}</div>`);
                currentNumber = newNumber;
                showStructure();
            }, 800);
        }
        
        function resetStructure() {
            currentNumber = 0;
            $('.buynumshow').text('238997');
            $('.scroll').html('=<div class="scroll-item">0</div>');
            showStructure();
        }
        
        // 页面加载时显示结构
        $(document).ready(function() {
            showStructure();
        });
    </script>
</body>
</html>
