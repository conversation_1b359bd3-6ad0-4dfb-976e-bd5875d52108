/* 修复手机浏览器中组件重叠问题 */

/* 确保com10、com11、com12组件在手机端有足够的间距 */
@media (max-width: 767px) {
    /* 性格优势组件 */
    .com10[data-v-1f429bc7] {
        margin-bottom: 1rem !important;
        position: relative !important;
        z-index: 1 !important;
    }
    
    /* 性格劣势组件 */
    .com11[data-v-b331d87b] {
        margin-bottom: 1rem !important;
        position: relative !important;
        z-index: 1 !important;
        margin-top: 0.5rem !important;
    }
    
    /* 个人成长建议组件 */
    .com12[data-v-08b317a6] {
        margin-bottom: 1rem !important;
        position: relative !important;
        z-index: 1 !important;
        margin-top: 0.5rem !important;
    }
    
    /* 确保购买按钮不会与内容重叠 */
    .buy_btn[data-v-bf9bd6de] {
        position: absolute !important;
        z-index: 99 !important;
        margin-top: 0.3rem !important;
    }
    
    /* 为每个组件的内容区域添加清除浮动 */
    .com10 .paddiing[data-v-1f429bc7],
    .com11 .paddiing[data-v-b331d87b],
    .com12[data-v-08b317a6] {
        clear: both !important;
        overflow: hidden !important;
    }
    
    /* 确保标题区域有足够的间距 */
    .status_title.pc_status_title[data-v-fa7a7b82] {
        margin-bottom: 0.4rem !important;
        position: relative !important;
        z-index: 2 !important;
    }
    
    /* 为模糊内容区域添加额外的内边距 */
    .blur {
        padding-bottom: 0.5rem !important;
        margin-bottom: 0.3rem !important;
    }
    
    /* 确保组件之间有明确的分隔 */
    .com10[data-v-1f429bc7]:after,
    .com11[data-v-b331d87b]:after,
    .com12[data-v-08b317a6]:after {
        content: "";
        display: block;
        height: 0.5rem;
        clear: both;
    }
}

/* 针对特定的手机屏幕尺寸进行额外优化 */
@media (max-width: 480px) {
    .com10[data-v-1f429bc7],
    .com11[data-v-b331d87b],
    .com12[data-v-08b317a6] {
        margin-bottom: 2rem !important;
    }
    
    /* 确保内容不会被截断 */
    .com10_wrap[data-v-1f429bc7],
    .com11_wrap[data-v-b331d87b],
    .com12_item[data-v-08b317a6] {
        min-height: auto !important;
        padding-bottom: 1rem !important;
    }
}

/* 为超小屏幕设备进行特殊处理 */
@media (max-width: 360px) {
    .com10[data-v-1f429bc7],
    .com11[data-v-b331d87b],
    .com12[data-v-08b317a6] {
        margin-bottom: 2.5rem !important;
        padding-bottom: 1rem !important;
    }
}
