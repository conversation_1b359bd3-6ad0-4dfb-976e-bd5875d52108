<?php if (!defined('THINK_PATH')) exit(); /*a:1:{s:92:"F:\PHPCUSTOM\wwwroot\web1.mbti366.com\public/../application/mbti/view/default/index\pay.html";i:1751119075;}*/ ?>
<html lang="en"  style="font-size: 38px;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="keywords" content="心理测试，心理学，心理咨询师，心理测评系统，心理学知识，MBTI性格测试，爱情测试，九型人格测试题，大五人格，性格测试，MBTI职业性格测试，霍兰德职业兴趣测试，性格色彩测试，测试性格，免费的性格测试，职业推荐，工作规划，职业发展，MBTI恋爱测试">
    <meta name="description" content="mbti-你是善于思考的INTP,是充满冒险精神的ESTP,还是善于照顾他人感受的ISFJ?您是16种人格中的哪一种?点击开始您的MBTI类型结果验证,用科学的方法指导工作和生活。">
    <title>MBTI测试</title>

    <link rel="stylesheet" href="/Public/mbit/newpay_files/index-0fbdb63a.css">
 
    <link rel="stylesheet" href="/Public/mbit/newpay_files/jiesuoBtn-cc97e989.css">
   
    <link rel="stylesheet" href="/Public/mbit/newpay_files/statusTitle-c811fd91.css">
   
    <link rel="stylesheet" href="/Public/mbit/newpay_files/comp15-153fa799.css">

    <link rel="stylesheet" href="/Public/mbit/newpay_files/result-1dae3fbf.css">
    <link rel="stylesheet" href="/Public/mbit/newpay_files/mobile-fix.css">
    <script src="/Public/layer/jquery.min.js"></script>
     <script src="/Public/layer/layer.js"></script>
    <style>
     /* .scroll1, .scroll2 {
        display: inline-block;
        height: 1em;
        overflow: hidden;
        position: relative;
      }
      .scroll1 span, .scroll2 span {
        position: absolute;
        width: 100%;
        text-align: center;
        transition: transform 0.5s ease-in-out;
      }*/
    </style>
    <style>
      .mbti-switch-buttons {
        position: absolute;
        bottom: 20px;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
      }
      .mbti-switch-buttons button {
        margin: 5px;
        padding: 5px 10px;
        background: #612fc6;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
      }
      .mbti-switch-buttons button:hover {
        background: #4a1fa3;
      }

      /* 进度条平滑过渡效果 */
      .van-progress__portion {
        transition: width 0.6s ease-in-out, background-color 0.3s ease;
      }


    </style>
    <script>

// 存储当前选中的报告类型
      var currentReportType = 2; // 默认选中第二个报告
      // 数字滚动功能
      $(document).ready(function() {
         all_type_descriptions = [{
                mbti_type: "INTJ",
                mbti_info: "战略家，军师气质",
                most_char: "最擅长制定计划、预测未来的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: "18.0",
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 98.1,
                    isResPersonality2: !0,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 57.1,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 29.4,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "INTP",
                mbti_info: "科学家，呆萌气质",
                most_char: "最擅长理论和思想系统的构建的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 30.3,
                    isResPersonality2: !0,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 5.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 20.2,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 29.9,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ENTJ",
                mbti_info: "领导者，王者气质",
                most_char: "最擅长领导指挥的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 10.4,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 15.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 20.2,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 50,
                    isResPersonality2: !0,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ENTP",
                mbti_info: "发明家，智多星气质",
                most_char: "最擅长创新和辩论的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 60,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 25.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 27.1,
                    isResPersonality1: !0,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 60,
                    isResPersonality2: !0,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "INFJ",
                mbti_info: "引路人，精神导师气质",
                most_char: "最擅长引导指引，遵循道德的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 30,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 65.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 17.1,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 40,
                    isResPersonality2: !0,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "INFP",
                mbti_info: "治愈者，幻想家气质",
                most_char: "最擅长安抚人心和价值判定的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 55,
                    isResPersonality2: !0,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !0,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 17.1,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 34,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ENFJ",
                mbti_info: "教育家，万人迷气质",
                most_char: "最擅长教导教育的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 20,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 15.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 27.1,
                    isResPersonality1: !0,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 6.9,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ENFP",
                mbti_info: "追梦人，精灵气质",
                most_char: "最擅长鼓舞激励人心、追逐梦想的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 0,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 25.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 17.1,
                    isResPersonality1: !0,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 9.9,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ISTJ",
                mbti_info: "检查者，精算师气质",
                most_char: "最擅长检查性管理的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 10,
                    isResPersonality2: !0,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 5.3,
                    isResPersonality2: !0,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 12.1,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 29.3,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ISFJ",
                mbti_info: "护卫者，守护者气质",
                most_char: "最擅长提供保护的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 20.2,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 15.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 37.1,
                    isResPersonality1: !0,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 2.9,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ESTJ",
                mbti_info: "管理者，监管者气质",
                most_char: "最擅长组织管理的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 20.9,
                    isResPersonality2: !0,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 5.3,
                    isResPersonality2: !0,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 17.1,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 29,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ESFJ",
                mbti_info: "东道主，人间烟火气质",
                most_char: "最擅长照顾关心他人的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 30.3,
                    isResPersonality2: !0,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 25.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 17.1,
                    isResPersonality1: !0,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 39.5,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ISTP",
                mbti_info: "手艺人，专家气质",
                most_char: "最擅长机械和实操相关技能的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 32.3,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 55.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 27.1,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 29.3,
                    isResPersonality2: !0,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ISFP",
                mbti_info: "艺术家，浪漫气质",
                most_char: "最擅长艺术审美的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 30,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 65.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 17.1,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 40,
                    isResPersonality2: !0,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ESTP",
                mbti_info: "实践者，痞子绅士气质",
                most_char: "最擅长说服推进的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 30.3,
                    isResPersonality2: !0,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 5.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 0,
                    isResPersonality1: !1,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 20.2,
                    isResPersonality2: !0,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 29.9,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }, {
                mbti_type: "ESFP",
                mbti_info: "表演家，乐天派气质",
                most_char: "最擅长娱乐、活跃气氛的类型",
                arr: [{
                    enName1: "E",
                    name1: "外向",
                    proportion1: 20,
                    isResPersonality1: !0,
                    enName2: "I",
                    name2: "内向",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#612fc6"
                }, {
                    enName1: "N",
                    name1: "直觉",
                    proportion1: 15.3,
                    isResPersonality1: !0,
                    enName2: "S",
                    name2: "实感",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#89cdfa"
                }, {
                    enName1: "F",
                    name1: "感性",
                    proportion1: 27.1,
                    isResPersonality1: !0,
                    enName2: "T",
                    name2: "理性",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#faba93"
                }, {
                    enName1: "J",
                    name1: "判断",
                    proportion1: 6.9,
                    isResPersonality1: !0,
                    enName2: "P",
                    name2: "知觉",
                    proportion2: 0,
                    isResPersonality2: !1,
                    color: "#a3daad"
                }]
            }]
        var currentIndex = 0;
        var timer = null;
        
        // 切换MBTI类型
        function switchMBTIType(index) {
          currentIndex = index;
          updateMBTIDisplay();
        }

        // 自动循环切换MBTI类型
        function autoSwitchMBTI() {
          if(timer) clearInterval(timer);
          timer = setInterval(function() {
            currentIndex = (currentIndex + 1) % all_type_descriptions.length;
            switchMBTIType(currentIndex);
          }, 1000); // 每4秒切换一次
        }

        // 更新MBTI显示 - 简化版本，只更新内容无动画
        function updateMBTIDisplay() {
          var type = all_type_descriptions[currentIndex];

          // 直接更新内容，无动画效果
          $('.com1_type').text(type.mbti_type);
          $('.com1_desc').text(type.mbti_info);
          $('.com1_ren img').attr('src', '/Public/mbit/newpay_files/' + type.mbti_type + '-1.png');

          // 更新进度条
          if (type.arr) {
            type.arr.forEach(function(item, i) {
              var $progressBars = $('.com1_proress ul li').eq(i);
              if ($progressBars.length > 0) {
                // 计算进度条宽度（使用百分比）
                var leftWidth = item.proportion1 > 0 ? Math.min(item.proportion1, 100) + '%' : '0%';
                var rightWidth = item.proportion2 > 0 ? Math.min(item.proportion2, 100) + '%' : '0%';

                // 更新左侧进度条
                $progressBars.find('.roter .van-progress__portion').css({
                  'width': leftWidth,
                  'background': item.color || '#612fc6'
                });

                // 更新右侧进度条
                $progressBars.find('.center:not(.roter) .van-progress__portion').css({
                  'width': rightWidth,
                  'background': item.color || '#612fc6'
                });
              }
            });
          }
        }
        


        // 暂停自动切换
        function pauseAutoSwitch() {
          if(timer) clearInterval(timer);
          setTimeout(function() {
            autoSwitchMBTI();
          }, 3000);
        }

        // 修改switchMBTIType函数
        function switchMBTIType(index) {
          currentIndex = index;
          updateMBTIDisplay();
        }

        // 初始化
        autoSwitchMBTI();
        
       
        

// 报告选择功能

  // 为报告选项添加点击事件
  $('.pay_type3 ul li, .pay_type2 ul li').on('click', function() {
    // 移除所有active类
    $('.pay_type3 ul li, .pay_type2 ul li').removeClass('active');
    // 为当前点击项添加active类
    let cindex = $(this).index();
    console.log(cindex);
    // 为当前点击项和对应位置的另一项添加active类
    $('.pay_type3 ul li').eq(cindex).addClass('active');
    $('.pay_type2 ul li').eq(cindex).addClass('active');
    
    // 获取当前选中的报告类型(1,2,3)
    currentReportType = $(this).index() + 1;
    
    // 根据选中的报告类型更新权限显示
    updateReportPermissions();
  });

  // 更新报告权限显示的函数
  function updateReportPermissions() {
    // 分别获取两个.item容器下的权限项
    const $permissionItems2 = $('.item2 li');
    const $permissionItems3 = $('.item3 li');
    
    // 处理第一个.item容器
    applyPermissionLogic($permissionItems2);
    
    // 处理第二个.item容器
    applyPermissionLogic($permissionItems3);
    
    function applyPermissionLogic($items) {
      // 根据当前选中的报告类型设置权限显示
      if (currentReportType === 1) {
        // 简要报告 - 从第5个开始变为del
        $items.each(function(index) {
          const $span = $(this).find('span');
          const $del = $(this).find('del');
          
          if (index >= 4) {
            if ($span.length) {
              const attrs = $span.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $span.replaceWith(`<del${attrStr}>` + $span.text() + '</del>');
            }
          } else {
            if ($del.length) {
              const attrs = $del.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
            }
          }
        });
      } else if (currentReportType === 2) {
        // 完整报告 - 只有最后两个变为del
        $items.each(function(index) {
          const $span = $(this).find('span');
          const $del = $(this).find('del');
          
          if (index >= $items.length - 2) {
            if ($span.length) {
              const attrs = $span.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $span.replaceWith(`<del${attrStr}>` + $span.text() + '</del>');
            } else if ($del.length === 0) {
              // 如果没有span但有内容，直接创建del标签
              const text = $(this).text().trim();
              if (text) {
                const $prevSpan = $(this).find('span');
                const attrs = $prevSpan.length ? $prevSpan.get(0).attributes : {};
                let attrStr = '';
                for (let i = 0; i < attrs.length; i++) {
                  attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
                }
                $(this).html(`<del${attrStr}>${text}</del>`);
              }
            }
          } else {
            if ($del.length) {
              const attrs = $del.get(0).attributes;
              let attrStr = '';
              for (let i = 0; i < attrs.length; i++) {
                attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
              }
              $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
            }
          }
        });
      } else if (currentReportType === 3) {
        // 完整解读Pro - 所有都是span
        $items.each(function() {
          const $del = $(this).find('del');
          if ($del.length) {
            const attrs = $del.get(0).attributes;
            let attrStr = '';
            for (let i = 0; i < attrs.length; i++) {
              attrStr += ` ${attrs[i].name}="${attrs[i].value}"`;
            }
            $del.replaceWith(`<span${attrStr}>` + $del.text() + '</span>');
          }
        });
      }
    }
  }

  // 初始化权限显示
  updateReportPermissions();


        let buyUserCount = parseInt(localStorage.getItem('buyUserCount2') || 3933600);
        let scroll1Value = buyUserCount % 10;
        let scroll2Value = (scroll1Value + 1) % 10;
        let isAnimating = false;

        function updateScrollNumbers() {
          if (isAnimating) return; // 防止动画重叠
          isAnimating = true;

          // 随机增长1或2
          const increment = Math.random() < 0.5 ? 1 : 2;

          // 计算新的数字值
          const newScroll1 = (scroll1Value + increment) % 10;
          const newScroll2 = (scroll2Value + increment) % 10;

          // 为每个滚动元素创建完整的动画结构
          $('.scroll1').html(`
            <div class="scroll-wrapper">
              <div class="scroll-item current">${scroll1Value}</div>
              <div class="scroll-item next">${newScroll1}</div>
            </div>
          `);

          $('.scroll2').html(`
            <div class="scroll-wrapper">
              <div class="scroll-item current">${scroll2Value}</div>
              <div class="scroll-item next">${newScroll2}</div>
            </div>
          `);

          // 立即触发动画
          requestAnimationFrame(() => {
            $('.scroll1 .scroll-wrapper').addClass('scrolling');
            $('.scroll2 .scroll-wrapper').addClass('scrolling');
          });

          // 动画完成后更新状态
          setTimeout(() => {
            // 更新数值
            scroll1Value = newScroll1;
            scroll2Value = newScroll2;
            buyUserCount += increment;
            localStorage.setItem('buyUserCount2', buyUserCount);

            // 重置为静态显示，避免空白
            $('.scroll1').html(`<div class="scroll-item">${scroll1Value}</div>`);
            $('.scroll2').html(`<div class="scroll-item">${scroll2Value}</div>`);

            // 更新其他显示
            $('.buynumshow').text(Math.floor(buyUserCount / 10));
            $('.buy_btn i').text(buyUserCount + "人购买");

            isAnimating = false;
          }, 800); // 稍微缩短动画时间
        }

        // 初始化显示
        function initializeDisplay() {
          $('.scroll1').html(`<div class="scroll-item">${scroll1Value}</div>`);
          $('.scroll2').html(`<div class="scroll-item">${scroll2Value}</div>`);
          $('.buynumshow').text(Math.floor(buyUserCount / 10));
          $('.buy_btn i').text(buyUserCount + "人购买");
        }

        // 页面加载完成后初始化
         $(document).ready(function() {
          initializeDisplay();
          // 每2.5秒执行一次动画
          setInterval(updateScrollNumbers, 2500);
        });
      });
      $(document).ready(function() {
        // 购买按钮点击事件
        $('.buy_btn').on('click', function() {
          // 检查是否已应用优惠，如果是则更新弹窗中的价格
          var discountApplied = localStorage.getItem('discountApplied') === 'true';
          if (discountApplied) {
            // 更新弹窗中的价格显示
            setTimeout(function() {
              $('.van-popup--bottom .pay_type ul li:first-child p:nth-child(2) .basick_price').html('¥9.9');
              $('.van-popup--bottom .pay_type ul li:nth-child(2) p:nth-child(2) .full_price').html('¥19.9');
              $('.van-popup--bottom .pay_type ul li:nth-child(3) p:nth-child(2) .pro_price').html('¥29.9');
            }, 100);
          }

          // 显示遮罩层
          $('#dialog-van-overlay-shade').css({'display': 'block'});
          // 显示底部弹窗
          $('.van-popup--bottom').css('display', 'block');
          // 给body添加样式
          $('body').addClass('van-overflow-hidden');
        });

        // 遮罩层点击事件
        $('#dialog-van-overlay-shade').on('click', function() {
          // 隐藏遮罩层
          $(this).css('display', 'none');
          // 隐藏底部弹窗
          $('.van-popup--bottom').css('display', 'none');
          // 移除body样式
          $('body').removeClass('van-overflow-hidden');
        });
        
        // 倒计时功能
        function startCountdown() {
          let minutes = 1;
          let seconds = 0;
          
          function updateCountdown() {
            $('.van-count-down').each(function() {
              $(this).text((minutes < 10 ? '0' + minutes : minutes) + ':' + (seconds < 10 ? '0' + seconds : seconds));
              if (minutes === 0 && seconds === 0) {
                return;
              }
            });
            
            if (seconds === 0) {
              if (minutes === 0) {
                return;
              }
              minutes--;
              seconds = 59;
            } else {
              seconds--;
            }
            
            setTimeout(updateCountdown, 1000);
          }
          
          updateCountdown();
        }
        
        // 页面加载时启动倒计时
        startCountdown();
      });

      // 浏览器返回监控功能
      $(document).ready(function() {
        // 标记是否已经应用过优惠
        var discountApplied = localStorage.getItem('discountApplied') === 'true';

        // 添加一个历史记录状态，用于检测返回操作
        if (window.history && window.history.pushState) {
            console.log('popstate true');


            window.history.pushState(null, null, location.href);

          // 监听popstate事件（浏览器返回/前进按钮）
          window.addEventListener('popstate', function(event) {
            // 检查是否是我们添加的状态
            if (event.state === 'preventBack') {

            }
            event.preventDefault();
            console.log('popstate addEventListener');


              // 显示优惠弹窗
              showBackDialog();
          });
        }

        // 应用价格优惠的函数
        function applyDiscount() {
          if (discountApplied) {
            console.log('优惠已经应用过，不再重复应用');
            return;
          }

          // 标记优惠已应用
          discountApplied = true;
          localStorage.setItem('discountApplied', 'true');

          // 更新三大报告的价格（减10元）
          // 更新页面中的价格显示
          $('.pay_type ul li:first-child p:nth-child(2) .basick_price').html('¥9.9');
          $('.pay_type ul li:nth-child(2) p:nth-child(2) .full_price').html('¥19.9');
          $('.pay_type ul li:nth-child(3) p:nth-child(2) .pro_price').html('¥29.9');

          // 更新弹窗中的价格显示
          $('.van-popup--bottom .pay_type ul li:first-child p:nth-child(2) .basick_price').html('¥9.9');
          $('.van-popup--bottom .pay_type ul li:nth-child(2) p:nth-child(2) .full_price').html('¥19.9');
          $('.van-popup--bottom .pay_type ul li:nth-child(3) p:nth-child(2) .pro_price').html('¥29.9');

          console.log('价格优惠已应用');
        }

        // 页面加载时检查是否已应用优惠
        if (discountApplied) {
          // 如果已经应用过优惠，直接更新价格显示
          $('.pay_type ul li:first-child p:nth-child(2) .basick_price').html('¥9.9');
          $('.pay_type ul li:nth-child(2) p:nth-child(2) .full_price').html('¥19.9');
          $('.pay_type ul li:nth-child(3) p:nth-child(2) .pro_price').html('¥29.9');

          $('.van-popup--bottom .pay_type ul li:first-child p:nth-child(2) .basick_price').html('¥9.9');
          $('.van-popup--bottom .pay_type ul li:nth-child(2) p:nth-child(2) .full_price').html('¥19.9');
          $('.van-popup--bottom .pay_type ul li:nth-child(3) p:nth-child(2) .pro_price').html('¥29.9');
        }

        // 显示返回拦截弹窗的函数
        function showBackDialog() {
          // 使用layer插件显示弹窗
          if (typeof layer !== 'undefined') {
            // 方法1：使用layer.open显示自定义内容
            layer.open({
              type: 1,
              title: false,
              closeBtn: 0,
              area: ['300px', 'auto'],
              skin: 'layui-layer-nobg',
              shadeClose: false, // 点击遮罩不关闭
              content: $('#huodongyouhui_dialog').html(),
              success: function(layero, index) {
                // 弹窗显示成功后的回调
                console.log('返回拦截弹窗已显示');

                // 为弹窗内的按钮添加点击事件
                layero.find('.dialog2_btn').on('click', function() {
                  // 点击按钮后的处理逻辑
                  layer.close(index);

                  // 应用价格优惠
                  applyDiscount();

                  // 这里可以添加跳转到购买页面的逻辑
                  console.log('用户点击了购买按钮');
                  // 再次添加历史记录，阻止真正的返回
                  window.history.pushState('preventBack', null, location.href);
                });
              }
            });
          } else {
            // 方法2：如果layer插件未加载，直接显示原有弹窗
            $('#huodongyouhui_dialog').show();

            // 添加点击事件关闭弹窗
            $('#huodongyouhui_dialog').on('click', '.van-overlay, .dialog2_btn', function() {
              $('#huodongyouhui_dialog').hide();

              // 如果点击的是购买按钮，应用价格优惠
              if ($(this).hasClass('dialog2_btn')) {
                applyDiscount();
              }
            });
          }
        }

        // 可选：添加键盘ESC键监听，防止用户通过ESC键退出
        $(document).on('keydown', function(e) {
          if (e.keyCode === 27) { // ESC键
            e.preventDefault();
            showBackDialog();
          }
        });

        // 可选：页面卸载前的提示（作为备用方案）
     /*    window.addEventListener('beforeunload', function(e) {
          // 现代浏览器会显示通用的确认对话框
          e.preventDefault();
          e.returnValue = '确定要离开吗？您可能会错过优惠！';
          return '确定要离开吗？您可能会错过优惠！';
        }); */
      });

      // 支付函数
      function updatOrder(e) {
        var price = getCurrentPrice();
        var channel = e ? 'alipay' : 'wxpay';
        var url = "/mbti/pay/submitpayqx";
        var $sn = '<?php echo $sn; ?>';

        
        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&qt_type='+currentReportType + '&iscoupon='+getIscoupon();
        return false;
      }

      function getIscoupon(){
        return localStorage.getItem('discountApplied') === 'true' ? 1 : 0;
      }

      function updatOldOrder(e) {
        var price = getCurrentPrice();
        var channel = e ? 'alipay' : 'wechat';
        var url = "/addons/epay/mbti/submitpay";
        var $sn = '<?php echo $sn; ?>';

        window.location.href = url + '?' + 'type=' + channel + '&amount=' + price + '&order_sn=' + $sn + '&qt_type='+currentReportType + '&iscoupon='+getIscoupon();
        return false;
      }

      // 获取当前选中的价格
      function getCurrentPrice() {
        var activeItem = $('.pay_type ul li.active, .van-popup--bottom .pay_type ul li.active');
        if (activeItem.length === 0) {
          // 如果没有选中项，默认选择第二个（完整报告）
          activeItem = $('.pay_type ul li:nth-child(2), .van-popup--bottom .pay_type ul li:nth-child(2)');
        }

        var priceText = activeItem.find('p:nth-child(2)').text();
        var price = priceText.match(/¥(\d+\.?\d*)/);
        return price ? parseFloat(price[1]) : <?php echo $full_price; ?>; // 默认价格28
      }

      // 处理支付选项点击
      $(document).on('click', '.pay_type ul li', function() {
        $(this).addClass('active').siblings().removeClass('active');
      });

      // 处理支付按钮点击
   /*    $(document).on('click', '.pay-button', function() {
        var isAlipay = $(this).find('.van-icon-alipay').length > 0;
        if (isAlipay) {
          updatOldOrder(1); // 支付宝
        } else {
          updatOrder(0); // 微信
        }
      }); */
    </script>
    <style data-id="immersive-translate-input-injected-css">
        .immersive-translate-input {
          position: absolute;
          top: 0;
          right: 0;
          left: 0;
          bottom: 0;
          z-index: 2147483647;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .immersive-translate-attach-loading::after {
          content: " ";
        
          --loading-color: #f78fb6;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          display: block;
          margin: 12px auto;
          position: relative;
          color: white;
          left: -100px;
          box-sizing: border-box;
          animation: immersiveTranslateShadowRolling 1.5s linear infinite;
        
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-2000%, -50%);
          z-index: 100;
        }
        
        .immersive-translate-loading-spinner {
          vertical-align: middle !important;
          width: 10px !important;
          height: 10px !important;
          display: inline-block !important;
          margin: 0 4px !important;
          border: 2px rgba(221, 244, 255, 0.6) solid !important;
          border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
          border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
          border-radius: 50% !important;
          padding: 0 !important;
          -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
          animation: immersive-translate-loading-animation 0.6s infinite linear !important;
        }
        
        @-webkit-keyframes immersive-translate-loading-animation {
          from {
            -webkit-transform: rotate(0deg);
          }
        
          to {
            -webkit-transform: rotate(359deg);
          }
        }
        
        @keyframes immersive-translate-loading-animation {
          from {
            transform: rotate(0deg);
          }
        
          to {
            transform: rotate(359deg);
          }
        }
        
        .immersive-translate-input-loading {
          --loading-color: #f78fb6;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          display: block;
          margin: 12px auto;
          position: relative;
          color: white;
          left: -100px;
          box-sizing: border-box;
          animation: immersiveTranslateShadowRolling 1.5s linear infinite;
        }
        
        @keyframes immersiveTranslateShadowRolling {
          0% {
            box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
              0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
          }
        
          12% {
            box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
              0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
          }
        
          25% {
            box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
              0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
          }
        
          36% {
            box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
              100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
          }
        
          50% {
            box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
              110px 0 var(--loading-color), 100px 0 var(--loading-color);
          }
        
          62% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
              120px 0 var(--loading-color), 110px 0 var(--loading-color);
          }
        
          75% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
              130px 0 var(--loading-color), 120px 0 var(--loading-color);
          }
        
          87% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
              200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
          }
        
          100% {
            box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
              200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
          }
        }

        
        .immersive-translate-modal {
          display: none;
          position: fixed;
          z-index: 2147483647;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          overflow: auto;
          background-color: rgb(0, 0, 0);
          background-color: rgba(0, 0, 0, 0.4);
          font-size: 15px;
        }
        
        .immersive-translate-modal-content {
          background-color: #fefefe;
          margin: 10% auto;
          padding: 40px 24px 24px;
          border: 1px solid #888;
          border-radius: 10px;
          width: 80%;
          max-width: 270px;
          font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
            "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
            "Segoe UI Symbol", "Noto Color Emoji";
          position: relative;
        }
        

        
        .immersive-translate-modal .immersive-translate-modal-content-in-input {
          max-width: 500px;
        }
        .immersive-translate-modal-content-in-input .immersive-translate-modal-body {
          text-align: left;
          max-height: unset;
        }
        
        .immersive-translate-modal-title {
          text-align: center;
          font-size: 16px;
          font-weight: 700;
          color: #333333;
        }
        
        .immersive-translate-modal-body {
          text-align: center;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          word-break: break-all;
          margin-top: 24px;
        }
        


        
        .immersive-translate-close {
          color: #666666;
          position: absolute;
          right: 16px;
          top: 16px;
          font-size: 20px;
          font-weight: bold;
        }
        
        .immersive-translate-close:hover,
        .immersive-translate-close:focus {
          color: black;
          text-decoration: none;
          cursor: pointer;
        }
        
        .immersive-translate-modal-footer {
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          margin-top: 24px;
        }
        
        .immersive-translate-btn {
          width: fit-content;
          color: #fff;
          background-color: #ea4c89;
          border: none;
          font-size: 16px;
          margin: 0 8px;
          padding: 9px 30px;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }
        
        .immersive-translate-btn:hover {
          background-color: #f082ac;
        }
        .immersive-translate-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
        .immersive-translate-btn:disabled:hover {
          background-color: #ea4c89;
        }
        
        .immersive-translate-cancel-btn {
          /* gray color */
          background-color: rgb(89, 107, 120);
        }
        
        .immersive-translate-cancel-btn:hover {
          background-color: hsl(205, 20%, 32%);
        }
        
        .immersive-translate-action-btn {
          background-color: transparent;
          color: #ea4c89;
          border: 1px solid #ea4c89;
        }
        
        .immersive-translate-btn svg {
          margin-right: 5px;
        }
        
        .immersive-translate-link {
          cursor: pointer;
          user-select: none;
          -webkit-user-drag: none;
          text-decoration: none;
          color: #007bff;
          -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        }
        
        .immersive-translate-primary-link {
          cursor: pointer;
          user-select: none;
          -webkit-user-drag: none;
          text-decoration: none;
          color: #ea4c89;
          -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        }
        
        .immersive-translate-modal input[type="radio"] {
          margin: 0 6px;
          cursor: pointer;
        }
        
        .immersive-translate-modal label {
          cursor: pointer;
        }
        
        .immersive-translate-close-action {
          position: absolute;
          top: 2px;
          right: 0px;
          cursor: pointer;
        }
        
        .imt-image-status {
          background-color: rgba(0, 0, 0, 0.5) !important;
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-content: center !important;
          border-radius: 16px !important;
        }
        .imt-image-status img,
        .imt-image-status svg,
        .imt-img-loading {
          width: 28px !important;
          height: 28px !important;
          margin: 0 0 8px 0 !important;
          min-height: 28px !important;
          min-width: 28px !important;
          position: relative !important;
        }
        .imt-img-loading {
          background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
          background-size: 28px 28px;
          animation: image-loading-rotate 1s linear infinite !important;
        }
        
        .imt-image-status span {
          color: var(--bg-2, #fff) !important;
          font-size: 14px !important;
          line-height: 14px !important;
          font-weight: 500 !important;
          font-family: "PingFang SC", Arial, sans-serif !important;
        }
        
        @keyframes image-loading-rotate {
          from {
            transform: rotate(360deg);
          }
          to {
            transform: rotate(0deg);
          }
        }
    </style>
</head>

<body style="font-size: 12px;" class="">
    <style>
        .mini-chat-beforeiframecreate{height:0 !important;}
    </style>
    
    <style>
       /* body{margin:0;}#scroll-wrapper{-webkit-overflow-scrolling: touch; overflow-y: scroll;}
        @-webkit-keyframes twinkling{0% {opacity:0;} 100%{ opacity:1;}}
        .face_twinkle{-webkit-animation: twinkling 0.5s infinite ease-in-out;}
        #toolbar img{display:inline;}#toolbar a{color:white;font-size:16px;}
        html{-webkit-tap-highlight-color: rgba(0,0,0,0);}
*/
        /* 数字滚动动画样式 */
        .scroll1, .scroll2 {
            position: relative;
            overflow: hidden;
            display: inline-block;
            height: 1.2em;
            line-height: 1.2em;
            vertical-align: top;
        }

        .scroll-wrapper {
            position: relative;
            height: 100%;
            transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .scroll-wrapper.scrolling {
            transform: translateY(-100%);
        }

        .scroll-item {
            display: block;
            height: 1.2em;
            line-height: 1.2em;
            text-align: center;
        }

        .scroll-item.current {
            position: relative;
        }

        .scroll-item.next {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
        }

        @keyframes twinkling {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @-webkit-keyframes twinkling {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }
    </style>
    
    <div class="app-container" style="max-width: 750px;margin: 0 auto;">
        <div class="layout-content">
            <div data-v-c3c1a80a="" class="main">
                <div data-v-c3c1a80a="" class="h5">
                    <div data-v-8826b650="" data-v-c3c1a80a="" class="complete_timer">
                        <div data-v-8826b650="" class="title">恭喜你答完了<?php echo $ti_num; ?>题，耗时<?php echo $use_time_str; ?></div>
                        <div data-v-8826b650="" class="title2">预计准确度98.99%</div>
                        <div data-v-8826b650="" class="card_box">
                            <div data-v-8826b650="" class="card" style="background: rgb(238, 245, 255);">
                                <div data-v-8826b650="" class="card1" style="border-color: rgb(39, 83, 148); color: rgb(39, 83, 148);"> 内向(I)-外向(E) 24题： </div>
                                <div data-v-8826b650="" class="card2">你的 I 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                                <div data-v-8826b650="" class="card2">你的 E 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                            </div>
                            <div data-v-8826b650="" class="card" style="background: rgb(255, 239, 224);">
                                <div data-v-8826b650="" class="card1" style="border-color: rgb(225, 116, 5); color: rgb(225, 116, 5);"> 直觉(N)-实感(S) 24题： </div>
                                <div data-v-8826b650="" class="card2">你的 N 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                                <div data-v-8826b650="" class="card2">你的 S 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                            </div>
                            <div data-v-8826b650="" class="card" style="background: rgb(244, 238, 255);">
                                <div data-v-8826b650="" class="card1" style="border-color: rgb(105, 71, 185); color: rgb(105, 71, 185);"> 理性(T)-感性(F) 20题： </div>
                                <div data-v-8826b650="" class="card2">你的 T 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                                <div data-v-8826b650="" class="card2">你的 F 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                            </div>
                            <div data-v-8826b650="" class="card" style="background: rgb(229, 252, 243);">
                                <div data-v-8826b650="" class="card1" style="border-color: rgb(38, 117, 86); color: rgb(38, 117, 86);"> 知觉(P)-判断(J) 25题： </div>
                                <div data-v-8826b650="" class="card2">你的 P 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                                <div data-v-8826b650="" class="card2">你的 J 维度得分<span data-v-8826b650="" class="blur">100</span>分</div>
                            </div>
                        </div>
                        <div data-v-8826b650="" class="info"> *基于MBTI中国常模统计以及维度判断难度不同，每个维度题目数量有所差异；最终得分与常模比较后得出。 </div>
                    </div>
                    <div data-v-c3c1a80a="" class="title">您的专属MBTI测试报告已生成</div>
                    <div data-v-c3c1a80a="" class="timer">生成于：<?php echo $result['add_time']; ?></div>
                    <div data-v-e7774774="" data-v-c3c1a80a="" class="com1 ismob" pricedata="[object Object],[object Object],[object Object]">
                        <div data-v-e7774774="" class="com1_opacity"><img data-v-e7774774="" src="/Public/mbit/newpay_files/section-header.png" alt="" class="com1_top_img">
                            <div data-v-e7774774="" class="com1_mbti">
                                <div data-v-e7774774="" class="absolute com1_time"><?php echo $today; ?></div>
                                <div data-v-e7774774="" class="absolute com1_you_type">你的测试类型 #</div>
                                <div data-v-e7774774="" class="absolute com1_mbti_img"><img data-v-e7774774="" src="/Public/mbit/newpay_files/mbti.png" alt=""></div>
                                <div data-v-e7774774="" class="absolute com1_type">ISTJ</div>
                                <div data-v-e7774774="" class="absolute com1_desc">检查者，精算师气质</div>
                                <div data-v-e7774774="" class="absolute com1_douhao"><img data-v-e7774774="" src="/Public/mbit/newpay_files/quote.png" alt=""></div>
                                <div data-v-e7774774="" class="absolute com1_juse_bg"><img data-v-e7774774="" src="/Public/mbit/newpay_files/char-bg.png" alt=""></div>
                                <div data-v-e7774774="" class="absolute com1_ren"><img data-v-e7774774="" src="/Public/mbit/newpay_files/ISTJ-1.png" alt=""></div>
                                <div data-v-e7774774="" class="com1_proress com1_proress_pc ismob">
                                    <div data-v-bf9bd6de="" data-v-e7774774="" style="--3544effe: 147px;">
                                        <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                                        <!---->
                                    </div>
                                    <ul data-v-e7774774="">
                                        <li data-v-e7774774="">
                                            <div data-v-e7774774="" class="name">外向<span data-v-e7774774="">(</span>E<span data-v-e7774774="">)</span></div>
                                            <div data-v-e7774774="" class="center roter">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(97, 47, 198); width: 0px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="center">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(97, 47, 198); width: 13px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="name">内向<span data-v-e7774774="">(</span>I<span data-v-e7774774="">)</span></div>
                                        </li>
                                        <li data-v-e7774774="">
                                            <div data-v-e7774774="" class="name">直觉<span data-v-e7774774="">(</span>N<span data-v-e7774774="">)</span></div>
                                            <div data-v-e7774774="" class="center roter">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(137, 205, 250); width: 0px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="center">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(137, 205, 250); width: 6.89px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="name">实感<span data-v-e7774774="">(</span>S<span data-v-e7774774="">)</span></div>
                                        </li>
                                        <li data-v-e7774774="">
                                            <div data-v-e7774774="" class="name">感性<span data-v-e7774774="">(</span>F<span data-v-e7774774="">)</span></div>
                                            <div data-v-e7774774="" class="center roter">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(250, 186, 147); width: 0px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="center">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(250, 186, 147); width: 15.73px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="name">理性<span data-v-e7774774="">(</span>T<span data-v-e7774774="">)</span></div>
                                        </li>
                                        <li data-v-e7774774="">
                                            <div data-v-e7774774="" class="name">判断<span data-v-e7774774="">(</span>J<span data-v-e7774774="">)</span></div>
                                            <div data-v-e7774774="" class="center roter">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(163, 218, 173); width: 38.09px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="center">
                                                <div data-v-e7774774="" class="rightPro van-progress" style="height: 10px;"><span class="van-progress__portion" style="background: rgb(163, 218, 173); width: 0px;"></span></div>
                                            </div>
                                            <div data-v-e7774774="" class="name">知觉<span data-v-e7774774="">(</span>P<span data-v-e7774774="">)</span></div>
                                        </li>
                                    </ul>
                                </div>
                            </div><img data-v-e7774774="" src="/Public/mbit/newpay_files/section-header.png" alt="" class="com1_foot_bg none">
                        </div>
                    </div>
                    <div data-v-21af5686="" data-v-c3c1a80a="" class="com3" id="chartDetail">
                        <div data-v-fa7a7b82="" data-v-21af5686="" class="status_title pc_status_title pc_status_title" style="--ec7d541c: 30px; --a4eb10dc: #ea8f6b; --162d92da: #612fc6;">
                            <!---->
                            <div data-v-fa7a7b82="" class="status_desc status_type"></div>
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">MBTI字母详解</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-21af5686="" class="esfj_four blur">
                            <div data-v-21af5686="" class="for"><span data-v-21af5686="" class="for_zimu">E</span><span data-v-21af5686="" class="for_tag"><i data-v-21af5686="">外向</i></span><span data-v-21af5686="" class="for_desc">与他人相处时充满活力</span></div>
                            <div data-v-21af5686="" class="for"><span data-v-21af5686="" class="for_zimu">S</span><span data-v-21af5686="" class="for_tag"><i data-v-21af5686="">实派</i></span><span data-v-21af5686="" class="for_desc">专注于事实与细节，而不是想法和概念</span></div>
                            <div data-v-21af5686="" class="for"><span data-v-21af5686="" class="for_zimu">F</span><span data-v-21af5686="" class="for_tag"><i data-v-21af5686="">感性</i></span><span data-v-21af5686="" class="for_desc">基于感觉和价值观做决定</span></div>
                            <div data-v-21af5686="" class="for"><span data-v-21af5686="" class="for_zimu">J</span><span data-v-21af5686="" class="for_tag"><i data-v-21af5686="">判断</i></span><span data-v-21af5686="" class="for_desc">更喜欢计划和组织，而不是自发和灵活</span></div>
                        </div>
                        <div data-v-bf9bd6de="" data-v-21af5686="" style="display: none; --3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-35e1e64d="" data-v-c3c1a80a="" class="com4">
                        <div data-v-fa7a7b82="" data-v-35e1e64d="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #fe8130; --162d92da: #fe8130;">
                            <div data-v-fa7a7b82="" class="status_desc">VALUES AND MOTIVATION</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">基础分析</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-35e1e64d="" class="padding blur">
                            <p data-v-3324c88e="" data-v-35e1e64d="">严格的道德准则行事，并希望其他人也这样做。你看待事物总是黑白分明，是非分明，而且你也不羞于分享自己对他人行为的评价。你寻求和谐与合作，并认为当每个人都遵循同一套规则时，是最好的状态。在人们相互联系的方式上，你有一种秩序感，并经常承担起共同落实推进这种社会秩序的角色。 </p>
                            <p data-v-3324c88e="" data-v-35e1e64d="">你对他人的需求有一种个人责任感，通常渴望参与并帮助他人。你可能是严肃和实际的，把尽职地工作放在娱乐之前——尤其是涉及关心他人的事情。你通常喜欢按惯例行事，保持有规律的日程安排，这使你能够富有条理、富有成效。</p>
                        </div>
                        <div data-v-bf9bd6de="" data-v-35e1e64d="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-51a6ce41="" data-v-c3c1a80a="" class="com2">
                        <div data-v-51a6ce41="" class="com2_title"> 一句话概括XXXX </div>
                        <div data-v-51a6ce41="" class="com2_padding blur">
                            <p data-v-3324c88e="" data-v-51a6ce41="">职尽责的帮助提供者，对他人的需求敏感，并积极专注于自己的责任。你能使自己的情感环境高度协调，并注重他人的感受和他人对你的感知。</p>
                            <p data-v-3324c88e="" data-v-51a6ce41="">你喜欢环境的和谐和合作感，并渴望取悦他人和为之付出。你看重忠诚和传统，通常把家人和朋友放在首位。你在时间、精力和情感上都很慷慨，经常关心别人的事情，就如同是你自己的事一样，并会尝试着利用自己强大的组织才能为别人的生活带来秩序。 </p>
                        </div>
                    </div>
                    <div data-v-c3d40b9e="" data-v-c3c1a80a="" class="com6">
                        <div data-v-fa7a7b82="" data-v-c3d40b9e="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">此类型人群占比</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-c3d40b9e="" class="status">
                            <h2 data-v-c3d40b9e="">人口中常见类型排行第??</h2>
                            <div data-v-c3d40b9e="" class="bili blur">
                                <div data-v-c3d40b9e="" class="item">
                                    <div data-v-c3d40b9e="" class="baifenbi"><span data-v-c3d40b9e="">INFP占总人口</span><span data-v-c3d40b9e="">12%</span></div>
                                    <div data-v-c3d40b9e="" class="van-circle" style="width: 80px; height: 80px;"><svg viewBox="0 0 1130 1130">
                                            <path d="M 565 565 m 0, -500 a 500, 500 0 1, 1 0, 1000 a 500, 500 0 1, 1 0, -1000" class="van-circle__layer" style="fill: none; stroke: rgb(235, 232, 239); stroke-width: 130px;"></path>
                                            <path d="M 565 565 m 0, -500 a 500, 500 0 1, 1 0, 1000 a 500, 500 0 1, 1 0, -1000" class="van-circle__hover" style="stroke: rgb(137, 101, 207); stroke-width: 131px; stroke-dasharray: 942px, 3140px;"></path>
                                        </svg><img data-v-c3d40b9e="" src="/Public/mbit/newpay_files/population-icon.png" alt=""></div>
                                </div>
                                <div data-v-c3d40b9e="" class="item">
                                    <div data-v-c3d40b9e="" class="baifenbi"><span data-v-c3d40b9e="">占男性</span><span data-v-c3d40b9e="">12%</span></div>
                                    <div data-v-c3d40b9e="" class="van-circle" style="width: 80px; height: 80px;"><svg viewBox="0 0 1130 1130">
                                            <path d="M 565 565 m 0, -500 a 500, 500 0 1, 1 0, 1000 a 500, 500 0 1, 1 0, -1000" class="van-circle__layer" style="fill: none; stroke: rgb(235, 232, 239); stroke-width: 130px;"></path>
                                            <path d="M 565 565 m 0, -500 a 500, 500 0 1, 1 0, 1000 a 500, 500 0 1, 1 0, -1000" class="van-circle__hover" style="stroke: rgb(77, 95, 247); stroke-width: 131px; stroke-dasharray: 942px, 3140px;"></path>
                                        </svg><img data-v-c3d40b9e="" src="/Public/mbit/newpay_files/real-male-icon.png" alt=""></div>
                                </div>
                                <div data-v-c3d40b9e="" class="item">
                                    <div data-v-c3d40b9e="" class="baifenbi"><span data-v-c3d40b9e="">占女性</span><span data-v-c3d40b9e="">12%</span></div>
                                    <div data-v-c3d40b9e="" class="van-circle" style="width: 80px; height: 80px;"><svg viewBox="0 0 1130 1130">
                                            <path d="M 565 565 m 0, -500 a 500, 500 0 1, 1 0, 1000 a 500, 500 0 1, 1 0, -1000" class="van-circle__layer" style="fill: none; stroke: rgb(235, 232, 239); stroke-width: 130px;"></path>
                                            <path d="M 565 565 m 0, -500 a 500, 500 0 1, 1 0, 1000 a 500, 500 0 1, 1 0, -1000" class="van-circle__hover" style="stroke: rgb(234, 143, 107); stroke-width: 131px; stroke-dasharray: 942px, 3140px;"></path>
                                        </svg><img data-v-c3d40b9e="" src="/Public/mbit/newpay_files/real-female-icon.png" alt=""></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-v-ecb30768="" data-v-c3c1a80a="" class="com5">
                        <div data-v-fa7a7b82="" data-v-ecb30768="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">统计数据之最</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-ecb30768="" class="people blur">
                            <div data-v-ecb30768="" class="people_flex">
                                <div data-v-ecb30768="" class="jieshao" style="margin-bottom: 12px;">（1）最可能喜欢瑜伽和冥想的类型TOP1</div>
                                <div data-v-ecb30768="" class="jieshao jieshao2">（2）最可能从事自由职业的类型TOP1</div>
                            </div>
                            <div data-v-ecb30768="" class="name"><span data-v-ecb30768="">山姆·沃尔顿</span><span data-v-ecb30768="">玛莎·斯图尔特</span><span data-v-ecb30768="">雷·克罗克</span><span data-v-ecb30768="">芭芭拉·沃尔特斯</span><span data-v-ecb30768="">戴夫·托马斯</span><span data-v-ecb30768="">威廉·霍华德·塔夫脱</span><span data-v-ecb30768="">JC Penney</span><span data-v-ecb30768="">莎莉·菲尔德</span><span data-v-ecb30768="">玛丽·泰勒·摩尔</span></div>
                        </div>
                    </div>
                    <div data-v-a06a2bb4="" data-v-c3c1a80a="" class="com7">
                        <div data-v-fa7a7b82="" data-v-a06a2bb4="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">同类型名人</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-a06a2bb4="" class="com7_name blur"><span data-v-a06a2bb4="">山姆·沃尔顿</span><span data-v-a06a2bb4="">玛莎·斯图尔特</span><span data-v-a06a2bb4="">雷·克罗克</span><span data-v-a06a2bb4="">芭芭拉·沃尔特斯</span><span data-v-a06a2bb4="">戴夫·托马斯</span><span data-v-a06a2bb4="">威廉·霍华德·塔夫脱</span><span data-v-a06a2bb4="">JC Penney</span><span data-v-a06a2bb4="">莎莉·菲尔德</span><span data-v-a06a2bb4="">玛丽·泰勒·摩尔</span></div>
                    </div>
                    <div data-v-13bc6eb1="" data-v-c3c1a80a="" class="com8">
                        <div data-v-fa7a7b82="" data-v-13bc6eb1="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <!---->
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">思维模式与价值观</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-13bc6eb1="" class="padding blur">
                            <p data-v-3324c88e="" data-v-13bc6eb1=""> 荣格的心理类型理论表明，人们使用四种主要的心理功能—思维、情感、直觉、感觉来体验世界。而这四种功能根据获取能量的来源又分为外倾、内倾，因此就形成了八个维度，简称“八维”。 </p>
                            <p data-v-3324c88e="" data-v-13bc6eb1=""> 八维又可分为阳面和阴面。其中，阳面四维是指我们人格形成过程中首要分化发展、并善于运用的四个维度；阴面四维是指不为人们的意识所接受的、是个体人格恐惧并抗拒的四个维度。 </p>
                        </div>
                    </div>
                    <div data-v-0551f446="" data-v-c3c1a80a="" class="com9">
                        <div data-v-fa7a7b82="" data-v-0551f446="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">他人眼中的你</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-0551f446="" class="padding blur">
                            <p data-v-3324c88e="" data-v-0551f446=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                            <p data-v-3324c88e="" data-v-0551f446=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                        </div>
                    </div>
                    <div data-v-1f429bc7="" data-v-c3c1a80a="" class="com10">
                        <div data-v-fa7a7b82="" data-v-1f429bc7="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">此类型的性格优势</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-1f429bc7="" class="paddiing blur">
                            <div data-v-1f429bc7="" class="com10_wrap">
                                <div data-v-1f429bc7="" class="com10_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-1f429bc7="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                                <div data-v-1f429bc7="" class="com10_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-1f429bc7="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                                <div data-v-1f429bc7="" class="com10_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-1f429bc7="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                                <div data-v-1f429bc7="" class="com10_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-1f429bc7="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                            </div>
                        </div>
                        <div data-v-bf9bd6de="" data-v-1f429bc7="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div style="height: 1px;"></div>
                    <div data-v-b331d87b="" data-v-c3c1a80a="" class="com11">
                        <div data-v-fa7a7b82="" data-v-b331d87b="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">此类型的性格劣势</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-b331d87b="" class="paddiing blur">
                            <div data-v-b331d87b="" class="com11_wrap">
                                <div data-v-b331d87b="" class="com11_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-b331d87b="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                                <div data-v-b331d87b="" class="com11_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-b331d87b="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                                <div data-v-b331d87b="" class="com11_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-b331d87b="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                                <div data-v-b331d87b="" class="com11_title">现实技能</div>
                                <p data-v-3324c88e="" data-v-b331d87b="">图固然美好，但除非有组织良好的行动计划作为后盾，否则没有多大意义。你比大多数人更了解如何完成任务，这就是为什么你制定的待办事项清单很少有未完成的项目。你非常认真仔细、注重细节，无法忍受一项重要的工作半途而废，如果你对一件事没有付出自己最大的努力，你会感到羞愧。 </p>
                            </div>
                        </div>
                        <div data-v-bf9bd6de="" data-v-b331d87b="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div style="height: 1px;"></div>
                    <div data-v-08b317a6="" data-v-c3c1a80a="" class="com12">
                        <div data-v-fa7a7b82="" data-v-08b317a6="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">个人成长建议</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-08b317a6="" class="com12_item blur">
                            <div data-v-08b317a6="" class="com12_title">多些情感</div>
                            <div data-v-08b317a6="" class="padding">
                                <p data-v-3324c88e="" data-v-08b317a6="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-08b317a6="" class="com12_item blur">
                            <div data-v-08b317a6="" class="com12_title com12_title2">多些情感</div>
                            <div data-v-08b317a6="" class="padding">
                                <p data-v-3324c88e="" data-v-08b317a6="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-08b317a6="" class="com12_item blur">
                            <div data-v-08b317a6="" class="com12_title">多些情感</div>
                            <div data-v-08b317a6="" class="padding">
                                <p data-v-3324c88e="" data-v-08b317a6="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-08b317a6="" class="com12_item blur">
                            <div data-v-08b317a6="" class="com12_title com12_title2">多些情感</div>
                            <div data-v-08b317a6="" class="padding">
                                <p data-v-3324c88e="" data-v-08b317a6="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-bf9bd6de="" data-v-08b317a6="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-49c356b4="" data-v-c3c1a80a="" class="com13">
                        <div data-v-fa7a7b82="" data-v-49c356b4="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">可能取得成就的方式</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-49c356b4="" class="com13_item blur">
                            <div data-v-49c356b4="" class="com13_title"><span data-v-49c356b4="">创造性表达</span><span data-v-49c356b4="">Method</span></div>
                            <div data-v-49c356b4="" class="padding">
                                <p data-v-3324c88e="" data-v-49c356b4="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-49c356b4="" class="com13_item blur">
                            <div data-v-49c356b4="" class="com13_title com13_title2"><span data-v-49c356b4="">创造性表达</span><span data-v-49c356b4="">Method</span></div>
                            <div data-v-49c356b4="" class="padding">
                                <p data-v-3324c88e="" data-v-49c356b4="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-49c356b4="" class="com13_item blur">
                            <div data-v-49c356b4="" class="com13_title"><span data-v-49c356b4="">创造性表达</span><span data-v-49c356b4="">Method</span></div>
                            <div data-v-49c356b4="" class="padding">
                                <p data-v-3324c88e="" data-v-49c356b4="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-49c356b4="" class="com13_item blur">
                            <div data-v-49c356b4="" class="com13_title com13_title2"><span data-v-49c356b4="">创造性表达</span><span data-v-49c356b4="">Method</span></div>
                            <div data-v-49c356b4="" class="padding">
                                <p data-v-3324c88e="" data-v-49c356b4="">人会给你带来极大的满足感，但这就像海绵一样，你吸收别人的焦虑，就好像这些烦恼就是你自己的一样。不幸的是，这让你在情感上疲惫不堪，可能会给自己带来巨大的压力，你可能会被自己所爱的人的痛苦和焦虑占据，而忽视了自己的个人需求。从长远来看，如果你能够学会在自己和想帮助的人之间保持一定的情感距离，你会更有效率。 </p>
                            </div>
                        </div>
                        <div data-v-bf9bd6de="" data-v-49c356b4="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-a93aeadf="" data-v-c3c1a80a="" class="com16">
                        <div data-v-fa7a7b82="" data-v-a93aeadf="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">AS A WORKER</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">作为一个工作者</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-a93aeadf="" class="padding blur">
                            <p data-v-3324c88e="" data-v-a93aeadf=""> 在工作中，你为人可靠且一丝不苟。你非常重视截止日期并且认真对待规范准则，他们能独立地并且有组织计划性地完成领导分配给他们的任务。你对工作环境有三个要求：稳定、有明确期望及少有意料之外的情况发生。当你能制定详细的行动计划并几乎毫无偏差地按照计划执行，他们能处于最佳状态。</p>
                            <p data-v-3324c88e="" data-v-a93aeadf=""> 在工作中，你为人可靠且一丝不苟。你非常重视截止日期并且认真对待规范准则，他们能独立地并且有组织计划性地完成领导分配给他们的任务。你对工作环境有三个要求：稳定、有明确期望及少有意料之外的情况发生。当你能制定详细的行动计划并几乎毫无偏差地按照计划执行，他们能处于最佳状态。</p>
                        </div>
                    </div>
                    <div data-v-c52bda90="" data-v-c3c1a80a="" class="com17">
                        <div data-v-fa7a7b82="" data-v-c52bda90="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">AS A COLLABORATOR</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">作为一个合作者</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-c52bda90="" class="padding blur">
                            <p data-v-3324c88e="" data-v-c52bda90=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                            <p data-v-3324c88e="" data-v-c52bda90=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                        </div>
                        <div data-v-bf9bd6de="" data-v-c52bda90="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-470a50da="" data-v-c3c1a80a="" class="com17">
                        <div data-v-fa7a7b82="" data-v-470a50da="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">AS A LEADER</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">作为一个领导者</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-470a50da="" class="padding blur">
                            <p data-v-3324c88e="" data-v-470a50da=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                            <p data-v-3324c88e="" data-v-470a50da=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                        </div>
                    </div>
                    <div data-v-21bd3e4e="" data-v-c3c1a80a="" class="com17">
                        <div data-v-fa7a7b82="" data-v-21bd3e4e="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">AS A COMMUNICATOR</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">作为一个沟通者</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-21bd3e4e="" class="padding blur">
                            <p data-v-3324c88e="" data-v-21bd3e4e=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                            <p data-v-3324c88e="" data-v-21bd3e4e=""> 你十分看重真实性，希望在所做的事情中保持原创和个性，所以你常常追寻内在的意义和真理。相比遵循传统原则，你更喜欢自发地探索一些价值观和理念，以此来判断是非。你不喜欢循规蹈矩，也不会试图融入主流人群的想法，更愿意忠于自己的想法。</p>
                        </div>
                        <div data-v-bf9bd6de="" data-v-21bd3e4e="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-b40f8936="" data-v-c3c1a80a="" class="com14">
                        <div data-v-b40f8936="" class="padding">
                            <div data-v-fa7a7b82="" data-v-b40f8936="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                                <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                                <!---->
                                <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">职业选择建议</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                            </div>
                        </div>
                        <div data-v-b40f8936="" class="com14_wrap blur">
                            <div data-v-b40f8936="" class="com14_fff">
                                <div data-v-b40f8936="" class="zy_jianyi">
                                    <div data-v-b40f8936="" class="zy_card marginL">
                                        <div data-v-b40f8936="" class="zu_l"><img data-v-b40f8936="" src="/Public/mbit/newpay_files/clip-icon-0.png" alt=""></div>
                                        <!---->
                                        <div data-v-b40f8936="" class="card_t">创意/艺术</div>
                                        <div data-v-b40f8936="" class="card_con"> 对你来说，艺术的魅力在于能以独创的、个性化的方式表达自己。职业艺术家所拥有的自由和灵活是你所向往的生活方式，不管是通过文字、画笔还是其他媒介表达自己，也不管是从事建筑设计、成为演员或者音乐家，你都力求创造出新颖独特的作品来表达自己的真实心声。即使不从事艺术行业，你也会称自己为“内心深邃的艺术家”。 </div>
                                        <div data-v-b40f8936="" class="card_span"><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span></div>
                                    </div>
                                    <div data-v-b40f8936="" class="zy_card marginR">
                                        <!---->
                                        <div data-v-b40f8936="" class="zu_r"><img data-v-b40f8936="" src="/Public/mbit/newpay_files/clip-icon-1.png" alt=""></div>
                                        <div data-v-b40f8936="" class="card_t card_t_r">创意/艺术</div>
                                        <div data-v-b40f8936="" class="card_con"> 对你来说，艺术的魅力在于能以独创的、个性化的方式表达自己。职业艺术家所拥有的自由和灵活是你所向往的生活方式，不管是通过文字、画笔还是其他媒介表达自己，也不管是从事建筑设计、成为演员或者音乐家，你都力求创造出新颖独特的作品来表达自己的真实心声。即使不从事艺术行业，你也会称自己为“内心深邃的艺术家”。 </div>
                                        <div data-v-b40f8936="" class="card_span"><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span></div>
                                    </div>
                                    <div data-v-b40f8936="" class="zy_card marginL">
                                        <div data-v-b40f8936="" class="zu_l"><img data-v-b40f8936="" src="/Public/mbit/newpay_files/clip-icon-0.png" alt=""></div>
                                        <!---->
                                        <div data-v-b40f8936="" class="card_t">创意/艺术</div>
                                        <div data-v-b40f8936="" class="card_con"> 对你来说，艺术的魅力在于能以独创的、个性化的方式表达自己。职业艺术家所拥有的自由和灵活是你所向往的生活方式，不管是通过文字、画笔还是其他媒介表达自己，也不管是从事建筑设计、成为演员或者音乐家，你都力求创造出新颖独特的作品来表达自己的真实心声。即使不从事艺术行业，你也会称自己为“内心深邃的艺术家”。 </div>
                                        <div data-v-b40f8936="" class="card_span"><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span></div>
                                    </div>
                                    <div data-v-b40f8936="" class="zy_card marginR">
                                        <!---->
                                        <div data-v-b40f8936="" class="zu_r"><img data-v-b40f8936="" src="/Public/mbit/newpay_files/clip-icon-1.png" alt=""></div>
                                        <div data-v-b40f8936="" class="card_t card_t_r">创意/艺术</div>
                                        <div data-v-b40f8936="" class="card_con"> 对你来说，艺术的魅力在于能以独创的、个性化的方式表达自己。职业艺术家所拥有的自由和灵活是你所向往的生活方式，不管是通过文字、画笔还是其他媒介表达自己，也不管是从事建筑设计、成为演员或者音乐家，你都力求创造出新颖独特的作品来表达自己的真实心声。即使不从事艺术行业，你也会称自己为“内心深邃的艺术家”。 </div>
                                        <div data-v-b40f8936="" class="card_span"><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">音乐家</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span><span data-v-b40f8936="">演员</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div data-v-bf9bd6de="" data-v-b40f8936="" style="--3544effe: 50px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-045dbb09="" data-v-c3c1a80a="" class="com15">
                        <div data-v-fa7a7b82="" data-v-045dbb09="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <!---->
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">xxxxxxxxx</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-045dbb09="" class="bikeng_jinayi blur">
                            <div data-v-045dbb09="" class="bikeng_l">
                                <div data-v-045dbb09="" class="bikeng_l_img">01</div>
                            </div>
                            <div data-v-045dbb09="" class="bikeng_r">
                                <h1 data-v-045dbb09="">对生活和工作要有切合实际的期望</h1>
                                <div data-v-045dbb09="" class="h1_con"> 你要意识到找到一份合适的工作所需要的时间比预期的要长。关注现实并把纳入你选择职业的影响因素中，会让你在求职时更有效率。 学会何时在不太关键的问题上妥协是很有意义的一课。也许在你的薪水水平上、或所居住的地区并没有一份“完美”的工作，至少现在没有，你可能需要在一些不太重要的问题上上妥协 </div>
                            </div>
                        </div>
                        <div data-v-045dbb09="" class="bikeng_jinayi blur">
                            <div data-v-045dbb09="" class="bikeng_l">
                                <div data-v-045dbb09="" class="bikeng_l_img">02</div>
                            </div>
                            <div data-v-045dbb09="" class="bikeng_r">
                                <h1 data-v-045dbb09="">对生活和工作要有切合实际的期望</h1>
                                <div data-v-045dbb09="" class="h1_con"> 你要意识到找到一份合适的工作所需要的时间比预期的要长。关注现实并把纳入你选择职业的影响因素中，会让你在求职时更有效率。 学会何时在不太关键的问题上妥协是很有意义的一课。也许在你的薪水水平上、或所居住的地区并没有一份“完美”的工作，至少现在没有，你可能需要在一些不太重要的问题上上妥协 </div>
                            </div>
                        </div>
                        <div data-v-045dbb09="" class="bikeng_jinayi blur">
                            <div data-v-045dbb09="" class="bikeng_l">
                                <div data-v-045dbb09="" class="bikeng_l_img">03</div>
                            </div>
                            <div data-v-045dbb09="" class="bikeng_r">
                                <h1 data-v-045dbb09="">对生活和工作要有切合实际的期望</h1>
                                <div data-v-045dbb09="" class="h1_con"> 你要意识到找到一份合适的工作所需要的时间比预期的要长。关注现实并把纳入你选择职业的影响因素中，会让你在求职时更有效率。 学会何时在不太关键的问题上妥协是很有意义的一课。也许在你的薪水水平上、或所居住的地区并没有一份“完美”的工作，至少现在没有，你可能需要在一些不太重要的问题上上妥协 </div>
                            </div>
                        </div>
                        <div data-v-bf9bd6de="" data-v-045dbb09="" style="--3544effe: 30px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            
                        </div>
                    </div>
                    <div data-v-b8e44e26="" data-v-c3c1a80a="" class="com16">
                        <div data-v-fa7a7b82="" data-v-b8e44e26="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">CHARACTERISTICS OF A RELATIONSHIP</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">恋爱特点</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-b8e44e26="" class="padding blur">
                            <p data-v-3324c88e="" data-v-b8e44e26=""> 恋爱中的你忠诚可靠。他们非常尊重传统，并且在他们的恋爱中承担了典型的性别角色：男性你承担赚钱养家的角色，女性你则负责照顾家人和家庭。你重视稳定性，在恋爱中，他们希望双方能够长期依赖。他们信守承诺，并希望他人也能如此。</p>
                            <p data-v-3324c88e="" data-v-b8e44e26=""> 恋爱中的你忠诚可靠。他们非常尊重传统，并且在他们的恋爱中承担了典型的性别角色：男性你承担赚钱养家的角色，女性你则负责照顾家人和家庭。你重视稳定性，在恋爱中，他们希望双方能够长期依赖。他们信守承诺，并希望他人也能如此。</p>
                        </div>
                    </div>
                    <div data-v-34ab6e93="" data-v-c3c1a80a="" class="com15">
                        <div data-v-fa7a7b82="" data-v-34ab6e93="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">RELATIONSHIP TIPS</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">恋爱宝典</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-34ab6e93="" class="bikeng_jinayi blur">
                            <div data-v-34ab6e93="" class="bikeng_l">
                                <div data-v-34ab6e93="" class="bikeng_l_img">01</div>
                            </div>
                            <div data-v-34ab6e93="" class="bikeng_r">
                                <h1 data-v-34ab6e93="">尝试放松控制欲，让关系更具灵活性。</h1>
                            </div>
                        </div>
                        <div data-v-34ab6e93="" class="bikeng_jinayi blur">
                            <div data-v-34ab6e93="" class="bikeng_l">
                                <div data-v-34ab6e93="" class="bikeng_l_img">02</div>
                            </div>
                            <div data-v-34ab6e93="" class="bikeng_r">
                                <h1 data-v-34ab6e93="">尝试放松控制欲，让关系更具灵活性。</h1>
                            </div>
                        </div>
                        <div data-v-34ab6e93="" class="bikeng_jinayi blur">
                            <div data-v-34ab6e93="" class="bikeng_l">
                                <div data-v-34ab6e93="" class="bikeng_l_img">03</div>
                            </div>
                            <div data-v-34ab6e93="" class="bikeng_r">
                                <h1 data-v-34ab6e93="">尝试放松控制欲，让关系更具灵活性。</h1>
                            </div>
                        </div>
                    </div>
                    <div data-v-04d5eb4a="" data-v-c3c1a80a="" class="com17">
                        <div data-v-fa7a7b82="" data-v-04d5eb4a="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #612fc6; --162d92da: #612fc6;">
                            <div data-v-fa7a7b82="" class="status_desc">IN THE EYES OF YOUR PARTNER</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">伴侣眼中的你</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-04d5eb4a="" class="padding blur">
                            <p data-v-3324c88e="" data-v-04d5eb4a=""> 在伴侣眼中，可能会被看作是可靠、负责任，善于处理事务，但也可能被认为有时候过于保守或者严谨。</p>
                        </div>
                    </div>
                    <div data-v-d488ddeb="" data-v-c3c1a80a="" class="com17">
                        <div data-v-fa7a7b82="" data-v-d488ddeb="" class="status_title pc_status_title" style="--ec7d541c: 12px; --a4eb10dc: #da2651; --162d92da: #da2651;">
                            <div data-v-fa7a7b82="" class="status_desc">FAMOUS CELEBRITIES</div>
                            <!---->
                            <div data-v-fa7a7b82="" class="status_txt"><span data-v-fa7a7b82="">与其他类型的匹配程度</span><span data-v-fa7a7b82=""><i data-v-fa7a7b82=""></i></span></div>
                        </div>
                        <div data-v-d488ddeb="" class="padding">
                            <div data-v-d488ddeb="" class="orther_cd blur">
                                <div data-v-d488ddeb="" class="orther_cd_t">• 志趣相投</div>
                                <div data-v-d488ddeb="" class="orther_cd_desc"> 以下几种类型的人比大多数人更有可能理解INFP的价值观、兴趣和生活方式。他们不一定在所有事情上都达成一致，也不能保证他们会一直相处融洽，但当他们相处时更有可能感到轻松融洽，因为他们有很多共同点。 INTP INFP INFJ ENFP </div>
                            </div>
                            <div data-v-d488ddeb="" class="orther_cd blur">
                                <div data-v-d488ddeb="" class="orther_cd_t">• 志趣相投</div>
                                <div data-v-d488ddeb="" class="orther_cd_desc"> 以下几种类型的人比大多数人更有可能理解INFP的价值观、兴趣和生活方式。他们不一定在所有事情上都达成一致，也不能保证他们会一直相处融洽，但当他们相处时更有可能感到轻松融洽，因为他们有很多共同点。 INTP INFP INFJ ENFP </div>
                            </div>
                            <div data-v-d488ddeb="" class="orther_cd blur">
                                <div data-v-d488ddeb="" class="orther_cd_t">• 志趣相投</div>
                                <div data-v-d488ddeb="" class="orther_cd_desc"> 以下几种类型的人比大多数人更有可能理解INFP的价值观、兴趣和生活方式。他们不一定在所有事情上都达成一致，也不能保证他们会一直相处融洽，但当他们相处时更有可能感到轻松融洽，因为他们有很多共同点。 INTP INFP INFJ ENFP </div>
                            </div>
                            <div data-v-d488ddeb="" class="orther_cd blur">
                                <div data-v-d488ddeb="" class="orther_cd_t">• 志趣相投</div>
                                <div data-v-d488ddeb="" class="orther_cd_desc"> 以下几种类型的人比大多数人更有可能理解INFP的价值观、兴趣和生活方式。他们不一定在所有事情上都达成一致，也不能保证他们会一直相处融洽，但当他们相处时更有可能感到轻松融洽，因为他们有很多共同点。 INTP INFP INFJ ENFP </div>
                            </div>
                        </div>
                        <div data-v-d488ddeb="" class="line"></div>
                        <div data-v-bf9bd6de="" data-v-d488ddeb="" style="--3544effe: 0px;">
                            <div data-v-bf9bd6de="" class="buy_btn animate__animated animate__pulse animate__infinite"><span data-v-bf9bd6de="">你的类型占比极低，仅占2.7%</span><i data-v-bf9bd6de="">解锁我的个人报告</i><i data-v-bf9bd6de="">2389982人购买</i></div>
                            <!---->
                        </div>
                    </div>
                    <div data-v-6d27db42="" data-v-c3c1a80a="" class="pay_wrap_box none">
                        <div data-v-6d27db42="" class="pay_t">
                            <p data-v-6d27db42=""> 临时报告将于
                              <span data-v-6d27db42="" class="count">
                                    <span data-v-6d27db42="" class="van-count-down">03:00</span>
                                </span>后失效 
                            </p>
                            <p data-v-6d27db42="">解锁完整报告即可<span data-v-6d27db42="">永久保存及转发</span></p>
                        </div>
                        <div data-v-6d27db42="" class="pay_type pay_type2">
                            <ul data-v-6d27db42="" class="ul">
                                <li data-v-6d27db42="" class="">
                                    <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">简要报告</p>
                                    <p data-v-6d27db42=""> <span class="basick_price">¥<?php echo $basick_price; ?></span> <del data-v-6d27db42="">¥38</del></p>
                                    <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">包含MBTI性格类型，性格概述，字母详解，等7项内容</p>
                                    <!---->
                                    <!---->
                                </li>
                                <li data-v-6d27db42="" class="active">
                                    <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整报告</p>
                                    <p data-v-6d27db42=""> <span class="full_price">¥<?php echo $full_price; ?></span> <del data-v-6d27db42="">¥99</del></p>
                                    <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">在简要报告的基础上，增加了性格优劣势详细分析，成长建议与成就以及荣格八维的解读，真正有深度的性格剖析</p>
                                    <div data-v-6d27db42="" class="tuijian">92%的人选择</div>
                                    <!---->
                                </li>
                                <li data-v-6d27db42="" class="">
                                    <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整解读Pro</p>
                                    <p data-v-6d27db42=""> <span class="pro_price">¥<?php echo $pro_price; ?></span> <del data-v-6d27db42="">¥699</del></p>
                                    <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">MBTI十六型人格完整解读报告，解锁12个大类，45个子类全部报告内容，自我认知，人际交往，职业发展，恋爱交友等维度深度剖析，详实呈现</p>
                                    <!---->
                                    <div data-v-6d27db42="" class="tuijian-today">仅限今日</div>
                                </li>
                            </ul>
                            <div data-v-6d27db42="" class="btn">
                                <div data-v-6d27db42="" class="pay-btn-wrapper">
                                    <button data-v-6d27db42="" onclick="updatOrder(0)" class="pay-button van-button van-button--primary van-button--small van-button--block van-button--round">
                                        <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-wechat-pay van-button__icon">
                                                <!---->
                                            </i><span data-v-6d27db42="" class="van-button__text">微信支付</span></div>
                                    </button>
                                    <button data-v-6d27db42="" onclick="updatOldOrder(1)" class="pay-button van-button van-button--info van-button--small van-button--block van-button--round">
                                        <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-alipay van-button__icon">
                                                <!---->
                                            </i><span data-v-6d27db42="" class="van-button__text">支付宝支付</span></div>
                                    </button>
                                </div>
                                <div data-v-6d27db42="" class="center" style="overflow-y: hidden;"> 已有<span class="buynumshow">238997</span>
                                  <div data-v-6d27db42="" class="scroll">=<div data-v-6d27db42="" class="scroll1">9</div>
                                        <div data-v-6d27db42="" class="scroll2">0</div>
                                    </div>人购买 
                                  </div>
                               <!--  <div data-v-6d27db42="" class="give_up_pay">
                                    <div data-v-6d27db42=""><img data-v-6d27db42="" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAYAAAAOwvOmAAAAAXNSR0IArs4c6QAABGNJREFUWEfVmGnoVVUUxX8rmqG5KBIaFIvKqIgGi4qSRgm/REGUIUmRVlTSpCCZDWSDBdE8fOhLUBCYZQXNNNA8WWnSQNFERRMRUe3Okn3lef/3vuHfE/R8eY93zzl3nXX2XnvtJ9bCobUQE6MCFRHrA8cBxwAHAjsDO+QBfwG+Bd4DXgMWS/p4kMMPBCoitgEuBc4HNhrgRU8DdwKPSPq717q+QCUzBnItsCEQwPvAw8nGu5LMDhGxRbJmBs3kScDGCeQ5YKakD7sB6wkq2VkMHAz8Wz6fAOZLerXXiRPkZsDJwJXAjsBfwAxJ97at7woqIhwrpn5c2eh7YJqkx/sBU58TEQZ3C3BGAbgesECSQ2HEaAUVEduVYDYbY4GlwBGSfhwNoM41ETEt42sD4DJJ19X3bAQVEY6bFzOznEWHSvr9/wKq1kfEVOAewMBOlOTwWDXaQM0CbsjUHj9MQB3A5gLzgB9KwkyQ9F31bASoiNi1ZMwHeYrjJTmm1siIiBdKzB4G3Czpwm6gri4ZMhtYIumENYImN42Ifcr1vZlZ7Rv5wo9WYyozxHqzaVLqAF9tRMQmwDnAz5Lu6wY6IvZ1zADPSHqpaW5EPFa0zoefK2l+EyhvsAh4R9J+LZs8CJySz+6WdFYbsIhwydk8n5uJFQ2HNCAD+0jSnk2gnJ6XALeW9D+vBdSvgDXHY6mkCV1A/ZOa5Cn7S3qrAZQz0Bq4JbCTpC/r1/d8ofpw0ylpSQuoyaUA35+lZpIkJ0XjKOLreacBr7iAS/qjZU/r4UHAZItzHdQ3Wbd2qYJuTQZ6tXdEVCEx3eWnDsqFFkk9a+IwwUbEHcDZDh1J168ToKrrG1Oy6uthstFDOh5Ki9N4fS8DE4EpkiwNrSMizPIk4PQMUjsK+yaXi2V2nE4ISS4jXUdEWMMOKQlxrKSn6td3I3ARsFCSP9uyaus0eEf2eJ/lw+bwAUn2YiNGRFgS7D4sMytvqA6qErJu4mln+Qlga2PnsCAFd5mkPyNi+6xnFtWjE8W8kjtXtICq3rlC0viVidY5McuMUduEHSDp7fpGHWXBwDzHqt3G6Iw0dm40dpe0vGE/66GbkDmSrhkByj9EhE9+cTF1z0o6qmGTR9MajytX7OvpFS8LAbO2t6RPayTs4ZKW5IyV9FUbqDHFsn6WbNmANSp7LzD9PI8I2yIf/DZJM6s1/Zi8vST91M9LBpkTEXOAq4pguvS45q2y2uuOHc7Ycha5kNqJ2ldNlPTbIGy0ZNt0X1c621mSbqrPG7TFOnW09jibkbtSbEfXYlXoI2Kr0tW413Mzan/k77O7WZZahrm9P9ftVPFp22YzeoGk29tY78sNZNt+eUpFZfDeKFf6ZMkem//lkj7Pa7e4uuTYf9ub2aVWa+ybpkqyxrWOvkB1sGa5cPt1ZofN7SfMXs9M8z8wjeWmc5OBQHWAs0JPSSbcHOzW8FeQ1dssLmpS8qEx1Q8lw5gzKqaG8eJue/wHg5GrNP/P5mAAAAAASUVORK5CYII=" style="margin-right: 4px; width: 14.6px;"> 放弃支付并退出 </div>
                                </div> -->
                                <div data-v-6d27db42="" class="center quanli">付费解锁后，您可以享受以下权利</div>
                                <div data-v-6d27db42="" class="quanli_item">
                                    <ul data-v-6d27db42="" class="item item2">
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI类型</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">基础解读</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI字母详解</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">报告永久保存</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">完整解读</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">同类型占比</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">价值观分析</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">性格解析-优劣势</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">成长建议</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">荣格八维性格解读</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">不同阶段的你</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职场避雷锦囊</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">团队中的你</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职业参考宝典</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">恋爱状态</span></li>
                                        <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">最佳恋爱匹配类型</span></li>
                                    </ul>
                                </div>
                                <div data-v-6d27db42="" class="renzheng"><img data-v-6d27db42="" src="/Public/mbit/newpay_files/anquanrz.png" alt=""></div>
                                <div data-v-6d27db42="" class="lv">支付系统已经过安全联盟认证请放心使用</div>
                            </div>
                        </div>
                    </div>
                   <!-- <div data-v-c3c1a80a="" class="van-overlay" style="z-index: 9; display: none;"></div>
                    <div data-v-c3c1a80a="" class="find-report">找回报告</div>-->
                </div>
                <div data-v-c3c1a80a="" class="pc_pay">
                    <div data-v-c3c1a80a="" class="pc_pay_box">
                        <!---->
                    </div>
                </div>
                <!---->
                <!---->
                <!---->
                <!---->
            </div>
            <!---->
        </div>
        
    </div>
    <div class="van-overlay" id="dialog-van-overlay-shade" style="z-index: 1000002; display: none;"></div>
    <div data-v-bf9bd6de="" class="van-popup van-popup--bottom" style="height: 15.4rem; z-index: 1000003; display: none;">
        <div data-v-bf9bd6de="" style="padding: 0.2rem 0px;">
            <div data-v-6d27db42="" data-v-bf9bd6de="" class="pay_wrap_box none">
              <div data-v-6d27db42="" class="pay_t">
                <p data-v-6d27db42=""> 临时报告将于
                  <span data-v-6d27db42="" class="count">
                    <span data-v-6d27db42="" class="van-count-down" > 03:00 </span>
                  </span>后失效 
                </p>

                <p data-v-6d27db42="">解锁完整报告即可
                  <span data-v-6d27db42="">永久保存及转发</span>
                </p>
            </div>
                <div data-v-6d27db42="" class="pay_type pay_type3">
                    <ul data-v-6d27db42="" class="ul">
                        <li data-v-6d27db42="" class="">
                            <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">简要报告</p>
                            <p data-v-6d27db42=""> <span class="basick_price">¥<?php echo $basick_price; ?></span> <del data-v-6d27db42="">¥38</del></p>
                            <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">包含MBTI性格类型，性格概述，字母详解，等7项内容</p>
                            <!---->
                            <!---->
                        </li>
                        <li data-v-6d27db42="" class="active">
                            <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整报告</p>
                            <p data-v-6d27db42=""> <span class="full_price">¥<?php echo $full_price; ?></span> <del data-v-6d27db42="">¥99</del></p>
                            <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">在简要报告的基础上，增加了性格优劣势详细分析，成长建议与成就以及荣格八维的解读，真正有深度的性格剖析</p>
                            <div data-v-6d27db42="" class="tuijian">92%的人选择</div>
                            <!---->
                        </li>
                        <li data-v-6d27db42="" class="">
                            <p data-v-6d27db42="" class="pay_type_title" style="height: 37px;">完整解读Pro</p>
                            <p data-v-6d27db42=""> <span class="pro_price">¥<?php echo $pro_price; ?></span> <del data-v-6d27db42="">¥699</del></p>
                            <p data-v-6d27db42="" style="color: rgb(153, 153, 153);">MBTI十六型人格完整解读报告，解锁12个大类，45个子类全部报告内容，自我认知，人际交往，职业发展，恋爱交友等维度深度剖析，详实呈现</p>
                            <!---->
                            <div data-v-6d27db42="" class="tuijian-today">仅限今日</div>
                        </li>
                    </ul>
                    <div data-v-6d27db42="" class="btn">
                        <div data-v-6d27db42="" class="pay-btn-wrapper">
                            <button data-v-6d27db42="" onclick="updatOrder(0)" class="pay-button van-button van-button--primary van-button--small van-button--block van-button--round">
                                <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-wechat-pay van-button__icon">
                                        <!---->
                                    </i><span data-v-6d27db42="" class="van-button__text">微信支付</span></div>
                            </button>
                            <button data-v-6d27db42="" onclick="updatOldOrder(1)" class="pay-button van-button van-button--info van-button--small van-button--block van-button--round">
                                <div data-v-6d27db42="" class="van-button__content"><i data-v-6d27db42="" class="van-icon van-icon-alipay van-button__icon">
                                        <!---->
                                    </i><span data-v-6d27db42="" class="van-button__text">支付宝支付</span></div>
                            </button></div>
                        <div data-v-6d27db42="" class="center" style="overflow-y: hidden;"> 已有<span class="buynumshow">238997</span>
                          <div data-v-6d27db42="" class="scroll">=<div data-v-6d27db42="" class="scroll1">0</div>
                                <div data-v-6d27db42="" class="scroll2">2</div>
                            </div>人购买 
                        </div>
                        <!-- <div data-v-6d27db42="" class="give_up_pay">
                            <div data-v-6d27db42=""><img data-v-6d27db42="" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAkCAYAAAAOwvOmAAAAAXNSR0IArs4c6QAABGNJREFUWEfVmGnoVVUUxX8rmqG5KBIaFIvKqIgGi4qSRgm/REGUIUmRVlTSpCCZDWSDBdE8fOhLUBCYZQXNNNA8WWnSQNFERRMRUe3Okn3lef/3vuHfE/R8eY93zzl3nXX2XnvtJ9bCobUQE6MCFRHrA8cBxwAHAjsDO+QBfwG+Bd4DXgMWS/p4kMMPBCoitgEuBc4HNhrgRU8DdwKPSPq717q+QCUzBnItsCEQwPvAw8nGu5LMDhGxRbJmBs3kScDGCeQ5YKakD7sB6wkq2VkMHAz8Wz6fAOZLerXXiRPkZsDJwJXAjsBfwAxJ97at7woqIhwrpn5c2eh7YJqkx/sBU58TEQZ3C3BGAbgesECSQ2HEaAUVEduVYDYbY4GlwBGSfhwNoM41ETEt42sD4DJJ19X3bAQVEY6bFzOznEWHSvr9/wKq1kfEVOAewMBOlOTwWDXaQM0CbsjUHj9MQB3A5gLzgB9KwkyQ9F31bASoiNi1ZMwHeYrjJTmm1siIiBdKzB4G3Czpwm6gri4ZMhtYIumENYImN42Ifcr1vZlZ7Rv5wo9WYyozxHqzaVLqAF9tRMQmwDnAz5Lu6wY6IvZ1zADPSHqpaW5EPFa0zoefK2l+EyhvsAh4R9J+LZs8CJySz+6WdFYbsIhwydk8n5uJFQ2HNCAD+0jSnk2gnJ6XALeW9D+vBdSvgDXHY6mkCV1A/ZOa5Cn7S3qrAZQz0Bq4JbCTpC/r1/d8ofpw0ylpSQuoyaUA35+lZpIkJ0XjKOLreacBr7iAS/qjZU/r4UHAZItzHdQ3Wbd2qYJuTQZ6tXdEVCEx3eWnDsqFFkk9a+IwwUbEHcDZDh1J168ToKrrG1Oy6uthstFDOh5Ki9N4fS8DE4EpkiwNrSMizPIk4PQMUjsK+yaXi2V2nE4ISS4jXUdEWMMOKQlxrKSn6td3I3ARsFCSP9uyaus0eEf2eJ/lw+bwAUn2YiNGRFgS7D4sMytvqA6qErJu4mln+Qlga2PnsCAFd5mkPyNi+6xnFtWjE8W8kjtXtICq3rlC0viVidY5McuMUduEHSDp7fpGHWXBwDzHqt3G6Iw0dm40dpe0vGE/66GbkDmSrhkByj9EhE9+cTF1z0o6qmGTR9MajytX7OvpFS8LAbO2t6RPayTs4ZKW5IyV9FUbqDHFsn6WbNmANSp7LzD9PI8I2yIf/DZJM6s1/Zi8vST91M9LBpkTEXOAq4pguvS45q2y2uuOHc7Ycha5kNqJ2ldNlPTbIGy0ZNt0X1c621mSbqrPG7TFOnW09jibkbtSbEfXYlXoI2Kr0tW413Mzan/k77O7WZZahrm9P9ftVPFp22YzeoGk29tY78sNZNt+eUpFZfDeKFf6ZMkem//lkj7Pa7e4uuTYf9ub2aVWa+ybpkqyxrWOvkB1sGa5cPt1ZofN7SfMXs9M8z8wjeWmc5OBQHWAs0JPSSbcHOzW8FeQ1dssLmpS8qEx1Q8lw5gzKqaG8eJue/wHg5GrNP/P5mAAAAAASUVORK5CYII=" style="margin-right: 4px; width: 14.6px;"> 放弃支付并退出 </div>
                        </div> -->
                        <div data-v-6d27db42="" class="center quanli">付费解锁后，您可以享受以下权利</div>
                        <div data-v-6d27db42="" class="quanli_item">
                            <ul data-v-6d27db42="" class="item item3">
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI类型</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">基础解读</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">MBTI字母详解</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">报告永久保存</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">完整解读</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">同类型占比</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">价值观分析</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">性格解析-优劣势</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">成长建议</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">荣格八维性格解读</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">不同阶段的你</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职场避雷锦囊</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">团队中的你</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">职业参考宝典</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">恋爱状态</span></li>
                                <li data-v-6d27db42=""><img data-v-6d27db42="" src="/Public/mbit/result2_files/lipoint.png" alt="" /><span data-v-6d27db42="">最佳恋爱匹配类型</span></li>
                            </ul>
                        </div>
                        <div data-v-6d27db42="" class="renzheng"><img data-v-6d27db42="" src="/Public/mbit/newpay_files/anquanrz.png" alt=""></div>
                        <div data-v-6d27db42="" class="lv">支付系统已经过安全联盟认证请放心使用</div>
                    </div>
                </div>
            </div>
        </div>
        <div id="huodongyouhui_dialog" data-v-ce9da9f2="" class="main" style="display: none;">
    <div data-v-fb0411b3="" data-v-ce9da9f2="" class="dialog dialog2">
        <div class="van-overlay" style="z-index: 1000004;"></div>
        <div data-v-fb0411b3="" class="van-popup van-popup--center" style="z-index: 1000005;">
            <div data-v-fb0411b3="" class="popup-box">
                <div data-v-fb0411b3="" class="dialog2_title">10元券</div>
                <div data-v-fb0411b3="" class="dialog2_desc">新人专属10元优惠券</div>
                <div data-v-fb0411b3="" class="dialog2_btn">立即使用优惠券</div>
            </div>
        </div>
    </div>
</div>
    </div>
    <style>

    .van-popup {
        position: fixed;
        max-height: 100%;
        overflow-y: auto;
        background-color: #fff;
        -webkit-transition: -webkit-transform .3s;
        transition: -webkit-transform .3s;
        transition: transform .3s;
        transition: transform .3s,-webkit-transform .3s;
        -webkit-overflow-scrolling: touch
    }

    .van-popup--center {
        top: 50%;
        left: 50%;
        -webkit-transform: translate3d(-50%,-50%,0);
        transform: translate3d(-50%,-50%,0)
    }
    .dialog[data-v-fb0411b3] .van-popup {
        background-color: transparent
    }

    .dialog .popup-box[data-v-fb0411b3] {
        width: 4.8667rem;
        height: 6.5333rem;
        background: url(/Public/mbit/result2_files/hongbao-huodong.png);
        background-size: 100% 100%;
        color: #fff;
        padding-top: 1.0667rem;
        box-sizing: border-box
    }

    .dialog2 .popup-box[data-v-fb0411b3] {
        width: 11.4rem;
        height: 11.3333rem;
        background: url(/Public/mbit/result2_files/hongbao-huodong.png);
        background-size: 100% 100%;
        text-align: center;
        padding-top: 3.2333rem
    }

    .dialog2 .popup-box .dialog2_title[data-v-fb0411b3] {
        font-size: 1rem;
        color: #f76868;
        font-weight: 700;
        line-height: 2rem;
    }

    .dialog2 .popup-box .dialog2_desc[data-v-fb0411b3] {
        font-size: .52rem;
        color: #fcaaaa;
        font-weight: 700
    }

    .dialog2 .dialog2_btn[data-v-fb0411b3] {
        font-size: 0.52rem;
        color: #cc4c25;
        padding-top: 2.7333rem;
        font-weight: 700;
    }

</style>
</body>
<div id="immersive-translate-popup" style="all: initial"></div>

</html>