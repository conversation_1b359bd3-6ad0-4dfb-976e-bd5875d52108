@charset "UTF-8";.com1[data-v-e7774774] {
    height: 11.3333rem;
    background-image: url(/Public/mbit/newpay_files/section-bg-71de1307.png);
    background-size: 100% 100%;
    overflow: hidden
}

.com1.ismob[data-v-e7774774] {
    height: 13.3333rem
}

.com1 .com1_proress_pc[data-v-e7774774] {
    display: block
}

.com1 .van-progress[data-v-e7774774] {
    height: .1333rem
}

.com1 .com1_opacity[data-v-e7774774] {
    height: 100%;
    background-color: #ffffff69;
    padding: .1333rem .2667rem 0;
    position: relative
}

.com1 .com1_opacity .com1_top_img[data-v-e7774774] {
    width: 100%
}

.com1 .com1_opacity .com1_mbti[data-v-e7774774] {
    height: 4.2667rem;
    position: relative;
    font-size: .32rem;
    color: #523787
}

.com1 .com1_opacity .com1_mbti .absolute[data-v-e7774774] {
    position: absolute
}

.com1 .com1_opacity .com1_mbti .com1_time[data-v-e7774774] {
    left: .8rem;
    top: .2667rem
}

.com1 .com1_opacity .com1_mbti .com1_you_type[data-v-e7774774] {
    top: .6667rem;
    font-size: .3733rem;
    font-weight: 700
}

.com1 .com1_opacity .com1_mbti .com1_mbti_img[data-v-e7774774] {
    width: .72rem;
    bottom: 0
}

.com1 .com1_opacity .com1_mbti .com1_mbti_img img[data-v-e7774774] {
    width: 100%
}

.com1 .com1_opacity .com1_mbti .com1_type[data-v-e7774774] {
    top: 1rem;
    left: .9333rem;
    font-size: 2.08rem
}

.com1 .com1_opacity .com1_mbti .com1_desc[data-v-e7774774] {
    font-size: .5067rem;
    bottom: 0;
    font-weight: 700;
    left: 1.0667rem
}

.com1 .com1_opacity .com1_mbti .com1_douhao[data-v-e7774774] {
    width: .56rem;
    top: .7333rem;
    right: 3.0667rem
}

.com1 .com1_opacity .com1_mbti .com1_douhao img[data-v-e7774774] {
    width: 100%
}

.com1 .com1_opacity .com1_mbti .com1_juse_bg[data-v-e7774774] {
    width: 4.8rem;
    top: -.4rem;
    right: -.7333rem
}

.com1 .com1_opacity .com1_mbti .com1_juse_bg img[data-v-e7774774] {
    width: 100%
}

.com1 .com1_opacity .com1_mbti .com1_ren[data-v-e7774774] {
    width: 4.1333rem;
    right: -.4rem
}

.com1 .com1_opacity .com1_mbti .com1_ren img[data-v-e7774774] {
    width: 100%
}

.com1 .com1_opacity .com1_proress[data-v-e7774774] {
    position: relative;
    padding-top: 4.8rem;
    box-sizing: border-box;
    font-size: .3733rem;
    color: #333
}

.com1 .com1_opacity .com1_proress.ismob[data-v-e7774774] {
    padding-top: 7.2rem
}

.com1 .com1_opacity .com1_proress ul[data-v-e7774774] {
    font-size: .3733rem
}

.com1 .com1_opacity .com1_proress ul li[data-v-e7774774] {
    display: flex;
    align-items: center;
    margin-bottom: .4rem
}

.com1 .com1_opacity .com1_proress ul li .name[data-v-e7774774] {
    width: 1.6rem;
    text-align: center
}

.com1 .com1_opacity .com1_proress ul li .center[data-v-e7774774] {
    flex: 1
}

.com1 .com1_opacity .com1_proress ul li .center[data-v-e7774774] .el-progress {
    display: flex
}

.com1 .com1_opacity .com1_proress ul li .center .rightPro[data-v-e7774774] .el-progress-bar__innerText {
    color: #333!important;
    font-size: .32rem!important;
    display: flex!important;
    transform: scale(.8);
    height: 100%;
    align-items: center;
    position: absolute;
    right: -1rem
}

.com1 .com1_opacity .com1_proress ul li .center .rightPro70[data-v-e7774774] .el-progress-bar__innerText {
    color: #fff!important;
    right: 0!important
}

.com1 .com1_opacity .com1_proress ul li .center .leftPro[data-v-e7774774] .el-progress-bar__innerText {
    color: #333!important;
    font-size: .32rem!important;
    display: flex!important;
    transform: scale(.8);
    height: 100%;
    align-items: center;
    position: absolute;
    right: -.9333rem
}

.com1 .com1_opacity .com1_proress ul li .center .leftPro70[data-v-e7774774] .el-progress-bar__innerText {
    color: #fff!important;
    right: 0rem!important
}

.com1 .com1_opacity .com1_proress ul li .center[data-v-e7774774] .el-progress-bar__inner {
    border-radius: 0!important;
    position: relative
}

.com1 .com1_opacity .com1_proress ul li .center[data-v-e7774774] .el-progress-bar__outer {
    border-radius: 0!important
}

.com1 .com1_opacity .com1_proress ul li .roter[data-v-e7774774] {
    transform: rotate(180deg)
}

.com1 .com1_opacity .com1_proress ul li .roter[data-v-e7774774] .el-progress-bar__innerText {
    transform: rotate(180deg) scale(.8)!important
}

.com1 .com1_opacity .com1_proress ul li .line[data-v-e7774774] {
    width: .08rem;
    background: #fff;
    height: .2667rem
}

.com1 .com1_opacity .com1_foot_bg[data-v-e7774774] {
    width: 100%;
    transform: rotate(180deg);
    position: absolute;
    bottom: .1333rem
}

@media (min-width: 768px) {
    .com1 .com1_opacity .com1_foot_bg[data-v-e7774774] {
        display: none;
    }
}


.com2[data-v-51a6ce41] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-top: .16rem;
    position: relative
}

.com2 .com2_title[data-v-51a6ce41] {
    padding: .32rem;
    box-sizing: border-box;
    background-image: url(/Public/mbit/newpay_files/header-tag-bg.png);
    background-size: 100% 100%;
    color: #ea8f6b;
    font-size: .4rem;
    text-align: center;
    font-weight: 700
}

.com2 .com2_padding[data-v-51a6ce41] {
    padding: 0 .1333rem;
    box-sizing: border-box
}




.com3[data-v-21af5686] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    position: relative
}

.com3 .esfj_four[data-v-21af5686] {
    padding: 0 .1333rem;
    box-sizing: border-box;
    margin-top: .5333rem
}

.com3 .esfj_four .for[data-v-21af5686] {
    margin-bottom: .5333rem;
    display: flex;
    align-items: center;
    color: #36235c;
    font-size: .3733rem
}

.com3 .esfj_four .for .for_zimu[data-v-21af5686] {
    font-weight: 700;
    font-size: .4267rem
}

.com3 .esfj_four .for .for_tag[data-v-21af5686] {
    background: #ea8f6b;
    display: inline-block;
    padding: .0267rem .16rem;
    color: #fff;
    transform: skew(-10deg);
    margin: 0 .2667rem;
    white-space: nowrap;
    font-size: .32rem
}

.com3 .esfj_four .for .for_tag i[data-v-21af5686] {
    font-style: normal;
    display: inline-block;
    transform: skew(10deg)
}



.com4[data-v-35e1e64d] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    position: relative
}

.com4 .padding[data-v-35e1e64d] {
    padding: 0 .1333rem
}






.com5[data-v-ecb30768] {
    padding: 0 .5333rem;
    box-sizing: border-box
}

.com5 .people[data-v-ecb30768] {
    position: relative;
    margin-top: .4rem
}

.com5 .people .people_flex[data-v-ecb30768] {
    padding: 0 .1333rem;
    box-sizing: border-box
}

.com5 .people .people_flex .jieshao[data-v-ecb30768] {
    background: #36235c;
    color: #fff;
    height: .8rem;
    padding-left: .16rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: .32rem
}

.com5 .people .people_flex .jieshao2[data-v-ecb30768] {
    background: #ea8f6b
}

.com5 .people .name[data-v-ecb30768] {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    font-size: .32rem;
    color: #36235c;
    padding: .4rem 0rem
}

.com5 .people .name span[data-v-ecb30768] {
    padding: .2667rem 0;
    width: 33%;
    text-align: center;

}








.com6[data-v-c3d40b9e] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com6 .status[data-v-c3d40b9e] {
    position: relative
}

.com6 .status h2[data-v-c3d40b9e] {
    color: #7446cd;
    font-size: .4267rem;
    text-align: center;
    margin: .4rem 0 1.2rem
}

.com6 .status .bili[data-v-c3d40b9e] {
    display: flex;
    justify-content: space-around;
    padding: 0 .16rem
}

.com6 .status .bili .item[data-v-c3d40b9e] {
    position: relative
}

.com6 .status .bili .item .baifenbi[data-v-c3d40b9e] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: absolute;
    top: -.7333rem;
    left: 0;
    right: 0
}

.com6 .status .bili .item .baifenbi span[data-v-c3d40b9e] {
    color: #36235c;
    font-size: .32rem;
    text-align: center;
    white-space: nowrap;
    width: 100%
}

.com6 .status .bili .item .baifenbi span[data-v-c3d40b9e]:last-child {
    color: #333;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-end;
    font-size: .32rem;
    padding-left: .4rem
}

.com6 .status .bili .item[data-v-c3d40b9e] .van-circle {
    display: flex;
    align-items: center;
    justify-content: center
}

.com6 .status .bili .item img[data-v-c3d40b9e] {
    width: 40%
}







.com7[data-v-a06a2bb4] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: .8rem
}

.com7 .com7_name[data-v-a06a2bb4] {
    font-size: .32rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    color: #36235c
}

.com7 .com7_name span[data-v-a06a2bb4] {
    padding: .2667rem 0;
    width: 33%;
    text-align: center
}



.com9[data-v-0551f446] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem
}

.com9 .padding[data-v-0551f446] {
    padding: 0 .1333rem
}






.com10[data-v-1f429bc7] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com10 .paddiing[data-v-1f429bc7] {
    margin-top: .4rem;
    padding: .4rem 0 0 1rem;
    background-image: url(/Public/mbit/newpay_files/advantage.png);
    background-size: 100%;
    background-repeat: no-repeat
}

.com10 .paddiing .com10_wrap[data-v-1f429bc7] {
    background: #fff;
    /*height: 100%;*/
    padding: .2667rem 0 0 .4rem;
    box-sizing: border-box
}

.com10 .paddiing .com10_wrap .com10_title[data-v-1f429bc7] {
    font-size: .48rem;
    color: #36235c;
    font-weight: 700
}

@media (min-width: 768px)  {
    .com10[data-v-1f429bc7] {
        padding:0 .1333rem;
        margin-bottom: .8rem
    }

    .com10 .paddiing[data-v-1f429bc7] {
        margin-top: .2rem;
        padding: .4rem 0 0 1rem;
        background-size: 60%
    }

    .com10 .paddiing .com10_wrap[data-v-1f429bc7] {
        padding: .2667rem 0 0 .4rem;
        height: auto;
    }

    .com10 .paddiing .com10_wrap .com10_title[data-v-1f429bc7] {
        font-size: .2667rem
    }
}


.com11[data-v-b331d87b] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com11 .paddiing[data-v-b331d87b] {
    margin-top: .4rem;
    padding: .4rem 0 0 1rem;
    background-image: url(/Public/mbit/newpay_files/disadvantage.png);
    background-size: 100%;
    background-repeat: no-repeat
}

.com11 .paddiing .com11_wrap[data-v-b331d87b] {
    background: #fff;
    /*height: 100%;*/
    padding: .2667rem 0 0 .4rem;
    box-sizing: border-box
}

.com11 .paddiing .com11_wrap .com11_title[data-v-b331d87b] {
    font-size: .48rem;
    color: #36235c;
    font-weight: 700
}



@media (min-width: 768px) {
    .com11[data-v-b331d87b] {
        padding:0 .1333rem;
        margin-bottom: .8rem
    }

    .com11 .paddiing[data-v-b331d87b] {
        margin-top: .2rem;
        padding: .4rem 0 0 1rem;
        background-size: 60%
    }

    .com11 .paddiing .com11_wrap[data-v-b331d87b] {
        padding: .2667rem 0 0 .4rem;
        height: auto;
    }

    .com11 .paddiing .com11_wrap .com11_title[data-v-b331d87b] {
        font-size: .2667rem
    }
}








.com12[data-v-08b317a6] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com12 .com12_item .com12_title[data-v-08b317a6] {
    height: 1.28rem;
    background: #8965cf;
    display: flex;
    align-items: center;
    font-size: .4267rem;
    color: #fff;
    padding-left: .32rem;
    box-sizing: border-box;
    background-image: url(/Public/mbit/newpay_files/potential-icon.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: calc(100% - .2667rem)
}

.com12 .com12_item .com12_title2[data-v-08b317a6] {
    background-color: #ea8f6b
}

.com12 .com12_item .padding[data-v-08b317a6] {
    padding: 0 .1333rem
}









.com13[data-v-49c356b4] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com13 .com13_item .com13_title[data-v-49c356b4] {
    height: 1.28rem;
    background: #8965cf;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: .4rem;
    font-size: .4267rem;
    color: #fff;
    padding-left: .32rem;
    box-sizing: border-box;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: calc(100% - .2667rem)
}

.com13 .com13_item .com13_title span[data-v-49c356b4]:last-child {
    font-size: .8rem;
    opacity: .1;
    font-weight: 700
}

.com13 .com13_item .com13_title2[data-v-49c356b4] {
    background-color: #ea8f6b
}

.com13 .com13_item .padding[data-v-49c356b4] {
    padding: 0 .1333rem
}








.com14[data-v-b40f8936] {
    position: relative
}

.com14 .padding[data-v-b40f8936] {
    padding: 0 .5333rem
}

.com14 .com14_wrap[data-v-b40f8936] {
    background: #f7f7fc;
    box-sizing: border-box
}

.com14 .com14_wrap .com14_fff[data-v-b40f8936] {
    background: #fff
}

.com14 .com14_wrap .com14_fff .zy_jianyi[data-v-b40f8936] {
    background: #f8f7f9;
    padding: .1333rem 0
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card[data-v-b40f8936] {
    background: #fff;
    position: relative;
    margin-bottom: .5333rem!important
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .zu_l[data-v-b40f8936] {
    width: .7467rem;
    height: .9867rem;
    position: absolute;
    top: -.2933rem;
    left: .1867rem
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .zu_l img[data-v-b40f8936] {
    width: 100%;
    height: 100%
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .zu_r[data-v-b40f8936] {
    width: .7467rem;
    height: .9867rem;
    position: absolute;
    top: -.2933rem;
    right: .1867rem
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .zu_r img[data-v-b40f8936] {
    width: 100%;
    height: 100%
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .card_t[data-v-b40f8936] {
    padding: .6667rem .6667rem .4rem;
    box-sizing: border-box;
    font-size: .48rem;
    font-weight: 700;
    color: #000
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .card_con[data-v-b40f8936] {
    padding: 0 .6667rem .6667rem;
    box-sizing: border-box;
    color: #36235c;
    font-size: .3733rem;
    line-height: 1.5
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .card_span[data-v-b40f8936] {
    padding: 0 .6667rem .6667rem;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .card_span span[data-v-b40f8936] {
    color: #8965cf;
    background: #f1edf9;
    border-radius: 1.6rem;
    padding: .08rem .2667rem;
    display: inline-block;
    margin-bottom: .2667rem;
    margin-right: .32rem;
    font-weight: 500;
    font-size: .32rem
}

.com14 .com14_wrap .com14_fff .zy_jianyi .zy_card .card_t_r[data-v-b40f8936] {
    text-align: right!important
}

.com14 .com14_wrap .com14_fff .zy_jianyi .marginL[data-v-b40f8936] {
    margin: .4rem 0 .4rem .4rem
}

.com14 .com14_wrap .com14_fff .zy_jianyi .marginR[data-v-b40f8936] {
    margin: .4rem .4rem .4rem 0
}



.com16[data-v-a93aeadf] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com16 .padding[data-v-a93aeadf] {
    padding: 0 .1333rem
}



.com17[data-v-c52bda90] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com17 .padding[data-v-c52bda90] {
    padding: 0 .1333rem
}


.com17[data-v-470a50da] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem
}

.com17 .padding[data-v-470a50da] {
    padding: 0 .1333rem
}



.com17[data-v-21bd3e4e] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com17 .padding[data-v-21bd3e4e] {
    padding: 0 .1333rem
}



.com17[data-v-d488ddeb] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: .5333rem;
    position: relative
}

.com17 .padding[data-v-d488ddeb] {
    margin-top: .2667rem
}

.com17 .padding .orther_cd .orther_cd_t[data-v-d488ddeb] {
    background: #da2651;
    font-size: .3733rem;
    height: .9867rem;
    display: inline-block;
    line-height: .9867rem;
    padding: 0 .8rem 0 .4rem;
    color: #fff
}

.com17 .padding .orther_cd .orther_cd_desc[data-v-d488ddeb] {
    font-size: .3733rem;
    color: #36235c;
    padding: .4rem .2667rem .6667rem .6667rem;
    box-sizing: border-box;
    line-height: 1.7
}

.com17 .line[data-v-d488ddeb] {
    height: .16rem;
    background: #f8f8f8
}



.com16[data-v-b8e44e26] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com16 .padding[data-v-b8e44e26] {
    padding: 0 .1333rem
}



.com15[data-v-34ab6e93] {
    padding: .5333rem;
    box-sizing: border-box;
    position: relative
}

.com15 .bikeng_jinayi[data-v-34ab6e93] {
    display: flex;
    margin: .4rem 0rem .6667rem 0
}

.com15 .bikeng_jinayi .bikeng_l[data-v-34ab6e93] {
    width: .8667rem
}

.com15 .bikeng_jinayi .bikeng_l .bikeng_l_img[data-v-34ab6e93] {
    height: .8667rem;
    background: url(/Public/mbit/newpay_files/number-bg.png);
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: .4267rem;
    color: #fff;
    font-weight: 700
}

.com15 .bikeng_jinayi .bikeng_r[data-v-34ab6e93] {
    display: flex;
    flex-direction: column;
    margin-left: .2667rem;
    flex: 1
}

.com15 .bikeng_jinayi .bikeng_r h1[data-v-34ab6e93] {
    margin: 0;
    font-size: .48rem;
    color: #523787;
    font-weight: 700
}

.com15 .bikeng_jinayi .bikeng_r .h1_con[data-v-34ab6e93] {
    padding: .2667rem 0;
    color: #36235c;
    font-size: .3733rem;
    line-height: 1.7
}



.com17[data-v-04d5eb4a] {
    padding: 0 .5333rem;
    box-sizing: border-box;
    margin-bottom: 1.0667rem;
    position: relative
}

.com17 .padding[data-v-04d5eb4a] {
    padding: 0 .1333rem
}




.complete_timer .title[data-v-8826b650] {
    font-size: .56rem;
    text-align: center;
    color: #333;
    padding-top: .4rem
}

.complete_timer .title2[data-v-8826b650] {
    font-size: .4rem;
    text-align: center;
    color: #333;
    padding-top: .3467rem;
    font-weight: 500
}

.complete_timer .card_box[data-v-8826b650] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap
}

.complete_timer .card_box .card[data-v-8826b650] {
    border-radius: .16rem;
    padding-bottom: .2933rem;
    margin-top: .2933rem
}

.complete_timer .card_box .card .card1[data-v-8826b650] {
    margin-top: .2933rem;
    margin-bottom: .2133rem;
    border-left: .1333rem solid;
    font-size: .2533rem;
    font-weight: 600;
    padding-left: .1067rem
}

.complete_timer .card_box .card .card2[data-v-8826b650] {
    font-size: .28rem;
    padding: .1067rem .1067rem 0 .1867rem;
    color: #1e1a33;
    font-weight: 400
}

.complete_timer .card_box .blur[data-v-8826b650] {
    filter: blur(.1rem)!important
}

.complete_timer .info[data-v-8826b650] {
    font-size: .24rem;
    text-align: center;
    color: #b5b5c8;
    margin-top: .2667rem
}




    .complete_timer[data-v-8826b650] {
        padding:.1333rem .2667rem 0
    }

    .complete_timer .card_box[data-v-8826b650] {
        gap: .2667rem;
        margin: .2667rem
    }

    .complete_timer .card_box .card[data-v-8826b650] {
        width: 47%
    }

    .complete_timer .card_box .card .card1[data-v-8826b650] {
        font-size: .32rem
    }

    .complete_timer .card_box .card .card2[data-v-8826b650] {
        font-size: .3733rem
    }

    .complete_timer .info[data-v-8826b650] {
        font-size: .2933rem
    }




.pay-code-dialog .van-popup {
    background-color: transparent
}

.pay-code-dialog .pay-code-tips {
    text-align: center;
    background: url(/assets/nopay_title_bg_pc-204da3a9.png) center center no-repeat;
    background-size: cover;
    margin: 0 auto;
    color: #992514;
    font-size: 14px;
    width: 140px;
    line-height: 34px;
    position: relative;
    top: 34px
}

.pay-code-dialog .pay-group {
    padding: 30px 12px 0;
    border-radius: 20px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-around;
    min-width: 500px;
    min-height: 260px
}

.pay-code-dialog .pay-group .pay-item {
    position: relative;
    height: 200px;
    width: 170px
}

.pay-code-dialog .qr {
    width: 170px;
    height: 170px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0
}

.pay-code-dialog .qr.qr-wx {
    width: 170px;
    height: 170px
}

.pay-code-dialog .w {
    font-size: 14px;
    line-height: 2.5;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0;
    width: 100%
}

.dialog[data-v-fb0411b3] .van-popup {
    background-color: transparent
}

.dialog .popup-box[data-v-fb0411b3] {
    width: 4.8667rem;
    height: 6.5333rem;
    background: url(/assets/seven-56f524fd.png);
    background-size: 100% 100%;
    color: #fff;
    padding-top: 1.0667rem;
    box-sizing: border-box
}

.dialog2 .popup-box[data-v-fb0411b3] {
    width: 6.4rem;
    height: 7.3333rem;
    background: url(/assets/seven-56f524fd.png);
    background-size: 100% 100%;
    text-align: center;
    padding-top: 2rem
}

.dialog2 .popup-box .dialog2_title[data-v-fb0411b3] {
    font-size: .8rem;
    color: #f76868;
    font-weight: 700;
    line-height: 2
}

.dialog2 .popup-box .dialog2_desc[data-v-fb0411b3] {
    font-size: .32rem;
    color: #fcaaaa;
    font-weight: 700
}

.dialog2 .dialog2_btn[data-v-fb0411b3] {
    font-size: .32rem;
    color: #cc4c25;
    padding-top: 1.56rem;
    font-weight: 700
}








    .dialog[data-v-fb0411b3] {
        overflow:hidden
    }

    .dialog .popup-box[data-v-fb0411b3] {
        width: 3.6667rem;
        height: 5.3333rem;
        padding-top: 1.0667rem
    }

    .dialog2 .popup-box[data-v-fb0411b3] {
        width: 5.0667rem;
        height: 6rem;
        padding-top: 1.7333rem
    }

    .dialog2 .popup-box .dialog2_title[data-v-fb0411b3] {
        font-size: .5333rem
    }

    .dialog2 .popup-box .dialog2_desc[data-v-fb0411b3] {
        font-size: .2133rem
    }

    .dialog2 .dialog2_btn[data-v-fb0411b3] {
        font-size: .2133rem;
        padding-top: 1.5333rem;
        cursor: pointer
    }


.dialog[data-v-73c99cb5] .van-popup {
    background-color: transparent
}

.dialog .popup-box[data-v-73c99cb5] {
    width: 4.8667rem;
    height: 6.5333rem;
    background: url(/assets/coupon-bg-3c02b6e3.png);
    background-size: 100% 100%;
    color: #fff;
    padding-top: 1.0667rem;
    box-sizing: border-box
}

.dialog .popup-box .coupon-title[data-v-73c99cb5] {
    font-size: 1.0667rem;
    text-align: center;
    font-weight: 700;
    display: flex;
    flex-direction: column
}

.dialog .popup-box .coupon-title span[data-v-73c99cb5] {
    font-size: .32rem;
    font-weight: 400
}

.dialog .popup-box .coupon-title i[data-v-73c99cb5] {
    font-size: .32rem;
    color: #bb5520;
    font-style: normal;
    padding-top: .2667rem
}

.dialog .popup-box .coupon-text[data-v-73c99cb5] {
    display: flex;
    flex-direction: column;
    font-size: .3733rem;
    align-items: center;
    padding-top: .9333rem
}

.dialog .popup-box .coupon-text .coupon-text-price[data-v-73c99cb5] {
    font-size: .32rem
}

.dialog .popup-box .coupon-text .coupon-text-price span[data-v-73c99cb5] {
    color: #fdeb96
}

.dialog .popup-box .coupon-text .coupon-btn[data-v-73c99cb5] {
    background: linear-gradient(180deg,#fcfb78,#fddd19);
    font-size: .32rem;
    padding: .08rem .5333rem;
    margin-top: .16rem;
    border-radius: 1.3333rem;
    box-shadow: 0 .02133rem .42667rem #00000059;
    color: #bc6d05;
    font-weight: 500
}

.dialog .popup-close[data-v-73c99cb5] {
    text-align: center;
    color: #fff;
    font-size: .8rem
}


.report_generator[data-v-8d49c2a3] {
    width: 100%;
    margin: 0 auto;
    overflow-x: hidden
}

.report_generator .bg[data-v-8d49c2a3] {
    width: 100%;
    height: 5.7333rem;
    background-color: #88619a;
    padding-top: .5333rem;
    z-index: -1;
    padding-left: 10%;
    padding-right: 10%;
    position: relative;
}

.report_generator .bg img[data-v-8d49c2a3] {
    width: 80%;
    margin: 0 auto
}

.report_generator .card[data-v-8d49c2a3] {
    border-radius: .2rem!important;
    margin: 0 auto;
    background-color: #fff;
    width: 88%;
    margin-top: -1.2rem;
    padding-bottom: .5867rem
}

.report_generator .card .title[data-v-8d49c2a3] {
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 1.3333rem;
    font-size: .3733rem;
    font-weight: 500;
    position: relative
}

.report_generator .card .title_active[data-v-8d49c2a3] {
    color: #88619a
}

.report_generator .card .slider[data-v-8d49c2a3] {
    position: absolute;
    bottom: 0;
    height: .08rem;
    left: 0;
    border-radius: .2667rem;
    background-color: #88619a
}

.report_generator .card .content[data-v-8d49c2a3] {
    padding: 0 .4rem
}

.report_generator .card .content .content_title[data-v-8d49c2a3] {
    margin-top: .5067rem;
    font-size: .36rem;
    font-weight: 400;
    color: #333
}

.report_generator .card .content .process[data-v-8d49c2a3] {
    margin-top: .1333rem;
    border-radius: .3467rem;
    height: .32rem;
    background-color: #ececec
}

.report_generator .card .content .process-active[data-v-8d49c2a3] {
    height: 100%;
    border-radius: .3467rem;
    background-image: linear-gradient(45deg,rgba(255,255,255,.15) 25%,transparent 25%,transparent 50%,rgba(255,255,255,.15) 50%,rgba(255,255,255,.15) 75%,transparent 75%,transparent);
    background-size: .52rem .52rem;
    /*animation: progress-stripes-8d49c2a3 .5s linear infinite;*/
    /*animation: progress-width-8d49c2a3 .5s linear 1;*/
    background-color: #88619a;
    /*transition: width .5s*/
}

@keyframes progress-stripes-8d49c2a3 {
    0% {
        background-position: 0 0
    }

    to {
        background-position: .52rem 0
    }
}

@keyframes progress-width-8d49c2a3 {
    0% {
        width: 0
    }

    to {
        width: 100%
    }
}




.main[data-v-c3c1a80a] {
    background: #fff
}

.main .pc_pay[data-v-c3c1a80a] {
    display: none
}



.main .find-report[data-v-c3c1a80a] {
    position: fixed;
    padding: .32rem .16rem;
    border-radius: .1333rem;
    z-index: 1000100;
    width: .32rem;
    font-size: .32rem;
    background-color: #826497;
    color: #fff;
    bottom: 2.6667rem;
    right: .1333rem
}

.main .title[data-v-c3c1a80a] {
    font-size: .5867rem;
    text-align: center;
    color: #333;
    padding-top: .4333rem
}

.main .timer[data-v-c3c1a80a] {
    text-align: center;
    font-size: .32rem;
    color: #ccc;
    margin-top: .0267rem
}
